/**
 * KRA TIMS Service
 * Handles integration with Kenya Revenue Authority (KRA) for fiscal receipts
 */
const axios = require('axios');
const fs = require('fs');
const https = require('https');
const crypto = require('crypto');
const QRCode = require('qrcode');
const logger = require('../utils/logger');
const timsLogger = require('../utils/tims-logger');

/**
 * Generate a random verification code
 * @returns {string} - Random verification code
 */
function generateVerificationCode() {
  return crypto.randomBytes(8).toString('hex').toUpperCase();
}

/**
 * KRA TIMS Service
 */
class KraTimsService {
  constructor() {
    // Load configuration from environment variables
    this.prodApiEndpoint = process.env.KRA_API_ENDPOINT;
    this.devApiEndpoint = process.env.KRA_DEV_API_ENDPOINT;
    this.useDevApi = process.env.KRA_USE_DEV_API === 'true';
    this.apiKey = process.env.KRA_API_KEY;
    this.certificatePath = process.env.KRA_CERTIFICATE_PATH;
    this.enabled = true; // Force enabled for testing

    // Use sample data mode
    this.useSampleData = true;
    this.offlineMode = false;

    this.pendingTransactions = [];

    // Determine which API endpoint to use
    this.apiEndpoint = this.useDevApi ? this.devApiEndpoint : this.prodApiEndpoint;

    // Initialize the service
    this.init();

    // Log that we're using sample data
    logger.warn('KRA Integration Service initialized with SAMPLE DATA mode');
  }

  /**
   * Initialize the KRA integration service
   */
  init() {
    // Check if required configuration is available
    if (!this.apiKey && !this.useSampleData && !this.offlineMode) {
      logger.warn('KRA Integration is missing required configuration');
    }

    // Set up periodic sync for offline transactions
    setInterval(() => this.syncPendingTransactions(), 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Register a sale with KRA
   * @param {Object} saleData - The sale data to register
   * @returns {Promise<Object>} - KRA verification data
   */
  async registerSale(saleData) {
    logger.info(`Registering sale ID ${saleData.id} with KRA`);

    try {
      // Always use the new KRA integration endpoint
      logger.info(`Using KRA process-sale endpoint for sale ID ${saleData.id}`);
      return await this.useNewKraEndpoint(saleData);
    } catch (error) {
      logger.error(`Error using KRA process-sale endpoint: ${error.message}`);
      logger.error(error.stack);
      // Fall back to offline mode if the process-sale endpoint fails
      return await this.generateOfflineResponse(saleData);
    }
  }

  /**
   * Format sale data according to KRA requirements
   * @param {Object} saleData - The sale data to format
   * @returns {Object} - Formatted sale data
   */
  formatSaleForKRA(saleData) {
    // Extract relevant data
    const {
      id,
      total_amount,
      created_at,
      Branch,
      User,
      SaleItems
    } = saleData;

    // Format items
    const items = SaleItems.map(item => ({
      name: item.Product.name,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.total_price,
      tax_rate: 16 // Assume 16% VAT
    }));

    // Calculate tax amount (16% VAT)
    const taxAmount = total_amount * 0.16;
    const subtotal = total_amount - taxAmount;

    // Format data according to KRA requirements
    return {
      invoice_number: String(id),
      invoice_date: created_at,
      seller_info: {
        name: Branch?.name || 'Unknown Branch',
        pin_number: 'P051234567X', // Placeholder PIN number
        address: Branch?.location || 'Unknown Location',
        contact_number: Branch?.phone || 'Unknown Phone'
      },
      buyer_info: {
        name: 'Walk-in Customer',
        pin_number: '',
        address: '',
        contact_number: ''
      },
      items,
      totals: {
        subtotal,
        tax_amount: taxAmount,
        total_amount
      },
      payment_info: {
        method: 'Cash',
        amount: total_amount
      },
      user_info: {
        name: User?.name || 'Unknown User',
        id: User?.id || 0
      }
    };
  }

  /**
   * Generate an offline response when KRA API is unavailable
   * @param {Object} saleData - The sale data
   * @returns {Promise<Object>} - Offline response
   */
  async generateOfflineResponse(saleData) {
    try {
      // Generate data in the new format
      const timestamp = Date.now().toString().slice(-8);
      const saleId = saleData.id || 0;
      const saleIdStr = String(saleId).padStart(6, '0');
      const invoiceNum = `00801107900000${saleIdStr}`;
      const cuNumber = `KRAMW00820220701${timestamp}`;
      const qrCode = `https://tims-test.kra.go.ke/KRA-Portal/invoiceChk.htm?actionCode=loadPage&invoiceNo=${invoiceNum}`;
      const dateTime = new Date().toISOString();

      // Generate TraderSystemInvNum using actual receipt number if available
      const traderSystemInvNum = saleData.receipt_number || `INV${saleId}`;
      logger.info(`Generated offline TraderSystemInvNum: ${traderSystemInvNum} for sale/invoice ID ${saleId}`);
      logger.info(`Offline receipt number source: ${saleData.receipt_number ? 'actual receipt_number' : 'generated from ID'}`);

      // Log the verification URL for debugging
      logger.info(`Generated offline KRA verification URL: ${qrCode}`);

      // Generate a QR code
      let qrCodeImage = null;
      try {
        logger.info(`Generating QR code for offline verification URL: ${qrCode}`);
        qrCodeImage = await QRCode.toDataURL(qrCode, {
          errorCorrectionLevel: 'H',
          margin: 1,
          width: 300
        });
        logger.info(`Generated QR code for offline verification, length: ${qrCodeImage ? qrCodeImage.length : 0} characters`);
      } catch (qrError) {
        logger.error(`Error generating QR code: ${qrError.message}`);
      }

      // Create the response
      const offlineResponse = {
        success: true,
        offline: true,
        verificationCode: cuNumber,
        fiscalReceiptNumber: invoiceNum,
        qrCodeUrl: qrCode,
        verificationUrl: qrCode,
        timestamp: dateTime,
        qrCodeImage
      };

      // Include the full response data in the new format
      offlineResponse.fullResponseData = JSON.stringify({
        invoiceNum,
        qrCode,
        cuNumber,
        dateTime,
        traderSystemInvNum,
        message: 'This receipt was generated in offline mode and will be synchronized with KRA when connectivity is restored.'
      });

      // Store the transaction for later synchronization
      this.storePendingTransaction(saleData);

      return offlineResponse;
    } catch (error) {
      logger.error(`Error generating offline response: ${error.message}`);

      // Return a minimal response in the new format
      const timestamp = Date.now().toString();
      const saleId = saleData.id || 0;
      const invoiceNum = `OFFLINE${timestamp.slice(-12)}`;
      const cuNumber = `OFFLINE${timestamp.slice(-8)}`;
      const dateTime = new Date().toISOString();

      // Generate TraderSystemInvNum using actual receipt number if available
      const traderSystemInvNum = saleData.receipt_number || `INV${saleId}`;
      logger.info(`Generated minimal offline TraderSystemInvNum: ${traderSystemInvNum} for sale/invoice ID ${saleId}`);
      logger.info(`Minimal offline receipt number source: ${saleData.receipt_number ? 'actual receipt_number' : 'generated from ID'}`);

      // Generate a simple QR code for the offline mode
      let qrCodeImage = null;
      try {
        const offlineQrUrl = `https://dukalink.com/verify?ref=${invoiceNum}`;
        logger.info(`Generating simple QR code for offline mode: ${offlineQrUrl}`);
        qrCodeImage = await this.generateQRCodeForUrl(offlineQrUrl);
        logger.info(`Generated simple QR code for offline mode: ${qrCodeImage ? 'Success' : 'Failed'}`);
      } catch (qrError) {
        logger.error(`Error generating simple QR code for offline mode: ${qrError.message}`);
      }

      return {
        success: true,
        offline: true,
        verificationCode: cuNumber,
        fiscalReceiptNumber: invoiceNum,
        qrCodeUrl: `https://dukalink.com/verify?ref=${invoiceNum}`,
        verificationUrl: `https://dukalink.com/verify?ref=${invoiceNum}`,
        timestamp: dateTime,
        qrCodeImage,
        fullResponseData: JSON.stringify({
          invoiceNum,
          cuNumber,
          dateTime,
          traderSystemInvNum,
          message: 'Offline mode, error generating complete response'
        })
      };
    }
  }

  /**
   * Store a pending transaction for later synchronization
   * @param {Object} transactionData - The transaction data (sale, invoice, or credit note)
   * @param {string} type - The type of transaction ('sale', 'invoice', 'credit_note')
   */
  storePendingTransaction(transactionData, type = 'sale') {
    this.pendingTransactions.push({
      id: transactionData.id,
      type: type,
      timestamp: new Date(),
      data: transactionData
    });

    logger.info(`Stored ${type} ID ${transactionData.id} for later synchronization with KRA`);
    logger.info(`Pending transactions: ${this.pendingTransactions.length}`);
  }

  /**
   * Synchronize pending transactions with KRA
   */
  async syncPendingTransactions() {
    if (this.offlineMode || this.pendingTransactions.length === 0) {
      return;
    }

    logger.info(`Attempting to synchronize ${this.pendingTransactions.length} pending transactions with KRA`);

    const successfulSyncs = [];

    for (const transaction of this.pendingTransactions) {
      try {
        // Skip transactions that are too old (more than 24 hours)
        const ageInHours = (new Date() - transaction.timestamp) / (1000 * 60 * 60);
        if (ageInHours > 24) {
          logger.warn(`Skipping ${transaction.type || 'sale'} ${transaction.id} as it is too old (${ageInHours.toFixed(2)} hours)`);
          successfulSyncs.push(transaction);
          continue;
        }

        // Attempt to register the transaction with KRA based on type
        let response;
        const transactionType = transaction.type || 'sale';

        switch (transactionType) {
          case 'credit_note':
            response = await this.registerCreditNote(transaction.data);
            break;
          case 'invoice':
            response = await this.registerInvoice(transaction.data);
            break;
          case 'sale':
          default:
            response = await this.registerSale(transaction.data);
            break;
        }

        if (response.success && !response.offline) {
          logger.info(`Successfully synchronized ${transactionType} ID ${transaction.id} with KRA`);
          successfulSyncs.push(transaction);

          // Update the database record with KRA data
          await this.updateDatabaseWithKraData(transaction.id, transactionType, response);
        }
      } catch (error) {
        logger.error(`Error synchronizing ${transaction.type || 'sale'} ${transaction.id}: ${error.message}`);
      }
    }

    // Remove successful synchronizations from pending transactions
    this.pendingTransactions = this.pendingTransactions.filter(
      transaction => !successfulSyncs.some(sync => sync.id === transaction.id)
    );

    logger.info(`Synchronized ${successfulSyncs.length} transactions with KRA`);
    logger.info(`Remaining pending transactions: ${this.pendingTransactions.length}`);
  }

  /**
   * Update database record with KRA data after successful synchronization
   * @param {number} id - Record ID
   * @param {string} type - Record type ('sale', 'invoice', 'credit_note')
   * @param {Object} kraResponse - KRA response data
   */
  async updateDatabaseWithKraData(id, type, kraResponse) {
    try {
      const updateData = {
        kra_verification_code: kraResponse.verification_code || kraResponse.verificationCode,
        kra_fiscal_receipt_number: kraResponse.fiscal_receipt_number || kraResponse.fiscalReceiptNumber,
        kra_verification_url: kraResponse.verification_url || kraResponse.verificationUrl,
        kra_verification_timestamp: kraResponse.timestamp,
        kra_integration_status: 'completed',
        kra_response_data: kraResponse.fullResponseData
      };

      // Add type-specific status updates
      if (type === 'credit_note') {
        updateData.status = 'issued';
      }

      // Import models dynamically to avoid circular dependencies
      const { Sale, Invoice, CreditNote } = require('../models');

      switch (type) {
        case 'credit_note':
          await CreditNote.update(updateData, { where: { id } });
          logger.info(`Updated credit note ${id} with KRA data`);
          break;
        case 'invoice':
          await Invoice.update(updateData, { where: { id } });
          logger.info(`Updated invoice ${id} with KRA data`);
          break;
        case 'sale':
        default:
          await Sale.update(updateData, { where: { id } });
          logger.info(`Updated sale ${id} with KRA data`);
          break;
      }
    } catch (error) {
      logger.error(`Error updating ${type} ${id} with KRA data: ${error.message}`);
    }
  }

  /**
   * Register an invoice with KRA
   * @param {Object} invoice - The invoice to register
   * @returns {Promise<Object>} KRA response data
   */
  async registerInvoice(invoice) {
    logger.info(`Registering invoice ${invoice.invoice_number || invoice.id} with KRA`);

    try {
      // Adapt the invoice to a sale-like structure for the KRA endpoint
      // For supplier invoices, use supplier data; for customer invoices, use customer data
      let customerData = null;
      let customerPin = '';

      if (invoice.type === 'supplier' && invoice.supplier) {
        // For supplier invoices, treat supplier as the "customer" for KRA purposes
        customerData = {
          name: invoice.supplier.name,
          address: invoice.supplier.address,
          postal_code: invoice.supplier.postal_code,
          pin_number: invoice.supplier.krapin // Use krapin field for suppliers
        };
        customerPin = invoice.supplier.krapin || '';
        logger.info(`Using supplier data for KRA: ${customerData.name}, PIN: ${customerPin}`);
      } else if (invoice.type === 'customer' && invoice.customer) {
        // For customer invoices, use customer data
        customerData = invoice.customer;
        customerPin = invoice.customer_pin || invoice.customer.pin_number || '';
        logger.info(`Using customer data for KRA: ${customerData.name}, PIN: ${customerPin}`);
      } else {
        logger.warn(`No customer/supplier data found for invoice ${invoice.invoice_number}`);
      }

      const adaptedData = {
        id: invoice.id,
        receipt_number: invoice.invoice_number, // Use actual invoice number
        total_amount: invoice.total_amount,
        created_at: invoice.invoice_date,
        Branch: invoice.Branch,
        Customer: customerData, // Use appropriate customer/supplier data
        SaleItems: invoice.items?.map(item => ({
          Product: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.total_price,
          vat_rate: item.vat_rate || 16,
          vat_amount: item.vat_amount || 0,
          is_vat_exempt: item.is_vat_exempt || false
        })) || [],
        // Add invoice-specific fields
        invoice_type: invoice.type,
        customer_pin: customerPin,
        // Pass through raw KRA data if provided
        raw_items: invoice.raw_items,
        raw_customer_info: invoice.raw_customer_info
      };

      logger.info(`Adapted invoice data for KRA: Invoice ${invoice.invoice_number}, Customer: ${adaptedData.Customer?.name || 'N/A'}`);

      // Use the same KRA endpoint as for sales
      logger.info(`Using KRA process-sale endpoint for invoice ${invoice.invoice_number}`);
      return await this.useNewKraEndpoint(adaptedData);
    } catch (error) {
      logger.error(`Error using KRA process-sale endpoint for invoice: ${error.message}`);
      logger.error(error.stack);
      // Fall back to offline mode if the process-sale endpoint fails
      return await this.generateOfflineResponse(invoice);
    }
  }

  /**
   * Register a credit note with KRA
   * @param {Object} creditNote - The credit note to register
   * @returns {Promise<Object>} KRA response data
   */
  async registerCreditNote(creditNote) {
    logger.info(`Registering credit note ${creditNote.credit_note_number || creditNote.id} with KRA`);

    try {
      // Use the KRA process-credit-note endpoint
      logger.info(`Using KRA process-credit-note endpoint for credit note ${creditNote.credit_note_number}`);
      return await this.useCreditNoteKraEndpoint(creditNote);
    } catch (error) {
      logger.error(`Error using KRA process-credit-note endpoint: ${error.message}`);
      logger.error(error.stack);
      // Fall back to offline mode and store for retry
      const offlineResponse = await this.generateOfflineResponse(creditNote);

      // Store credit note for later synchronization
      this.storePendingTransaction(creditNote, 'credit_note');

      return offlineResponse;
    }
  }

  /**
   * Generate a QR code for a URL
   * @param {string} url - The URL to encode in the QR code
   * @returns {Promise<string>} - Data URL of the QR code
   */
  async generateQRCodeForUrl(url) {
    try {
      // Check if URL is valid
      if (!url || url.trim() === '') {
        logger.warn('Cannot generate QR code for empty URL');
        return null;
      }

      // Clean the URL (remove trailing spaces)
      const cleanUrl = url.trim();

      // Log the URL being used for QR code generation
      logger.info(`Generating QR code for URL: ${cleanUrl}`);

      // Generate the QR code with high error correction level
      const qrCodeImage = await QRCode.toDataURL(cleanUrl, {
        errorCorrectionLevel: 'H',
        margin: 1,
        width: 300
      });

      // Log success
      logger.info(`Successfully generated QR code, data URL length: ${qrCodeImage ? qrCodeImage.length : 0} characters`);

      return qrCodeImage;
    } catch (error) {
      logger.error(`Error generating QR code for URL: ${error.message}`);

      // Try with a simplified URL if the original fails
      try {
        if (url && url.includes('?')) {
          const simplifiedUrl = url.split('?')[0];
          logger.info(`Retrying QR code generation with simplified URL: ${simplifiedUrl}`);

          return await QRCode.toDataURL(simplifiedUrl, {
            errorCorrectionLevel: 'H',
            margin: 1,
            width: 300
          });
        }
      } catch (retryError) {
        logger.error(`Error generating QR code with simplified URL: ${retryError.message}`);
      }

      // If all else fails, generate a QR code with a simple text
      try {
        const fallbackText = `DukaLink Receipt: ${new Date().toISOString()}`;
        logger.info(`Using fallback text for QR code: ${fallbackText}`);

        return await QRCode.toDataURL(fallbackText, {
          errorCorrectionLevel: 'H',
          margin: 1,
          width: 300
        });
      } catch (fallbackError) {
        logger.error(`Error generating fallback QR code: ${fallbackError.message}`);
        return null;
      }
    }
  }

  /**
   * Generate sample KRA data for testing
   * @param {Object} invoice - The invoice (not used but kept for API compatibility)
   * @returns {Promise<Object>} Sample KRA data
   */
  async generateSampleKraDataForInvoice(_invoice) {
    // Generate data in the new format
    const invoiceNum = `00801107900000${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
    const cuNumber = `KRAMW00820220701${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
    const qrCode = `https://tims-test.kra.go.ke/KRA-Portal/invoiceChk.htm?actionCode=loadPage&invoiceNo=${invoiceNum}`;
    const dateTime = new Date().toISOString();

    // Generate QR code
    const qrCodeImage = await this.generateQRCodeForUrl(qrCode);

    return {
      success: true,
      verificationCode: cuNumber,
      fiscalReceiptNumber: invoiceNum,
      verificationUrl: qrCode,
      qrCodeUrl: qrCode,
      qrCodeImage,
      timestamp: new Date(dateTime),
      offline: true,
      fullResponseData: JSON.stringify({
        invoiceNum,
        qrCode,
        cuNumber,
        dateTime
      })
    };
  }

  /**
   * Use the new KRA integration endpoint
   * @param {Object} saleData - The sale data to register
   * @returns {Promise<Object>} - KRA verification data
   */
  async useNewKraEndpoint(saleData) {
    try {
      logger.info(`Using new KRA endpoint for sale ID ${saleData.id}`);

      // Check if we have the raw sale data from the mobile app
      const rawItems = saleData.raw_items || null;
      const rawCustomerInfo = saleData.raw_customer_info || null;

      let formattedItems, customerInfo;

      if (rawItems && Array.isArray(rawItems) && rawItems.length > 0) {
        // Use the raw items directly from the mobile app
        logger.info(`Using raw items from mobile app for sale ID ${saleData.id}`);
        formattedItems = rawItems.map(item => {
          // Get the raw VAT rate value - use nullish coalescing to handle 0 values properly
          const rawVatRate = item.VATGrRate !== undefined && item.VATGrRate !== null
            ? item.VATGrRate
            : item.vat_rate !== undefined && item.vat_rate !== null
            ? item.vat_rate
            : 16;

          // Log the raw VAT rate for debugging
          logger.info(`Raw VAT rate value for item ${item.NamePLU || item.name || 'Unknown'}: ${rawVatRate} (type: ${typeof rawVatRate})`);

          // Parse the VAT rate to a number
          const vatRate = parseFloat(rawVatRate);

          // Check if item is explicitly marked as VAT exempt or has a zero VAT rate
          const isExemptByProperty = item.is_vat_exempt === true || item.isVatExempt === true;
          const isExemptByRate = vatRate === 0 || rawVatRate === '0' || rawVatRate === '0.0' || rawVatRate === '0.00';
          const isExempt = isExemptByProperty || isExemptByRate;

          // Log detailed exemption status
          logger.info(`Raw item ${item.NamePLU || item.name || 'Unknown'}:`);
          logger.info(`  - is_vat_exempt property: ${isExemptByProperty ? 'Yes' : 'No'}`);
          logger.info(`  - VAT rate is zero: ${isExemptByRate ? 'Yes' : 'No'} (rate: ${vatRate}, raw: ${rawVatRate})`);
          logger.info(`  - Final exemption status: ${isExempt ? 'Exempt' : 'Taxable'}`);

          // If item is exempt, ensure VAT rate is 0
          const effectiveVatRate = isExempt ? 0 : vatRate;

          // Set VAT class based on exemption status
          const providedVatClass = item.OptionVATClass;
          const calculatedVatClass = isExempt ? 'E' : 'A';
          const vatClass = providedVatClass || calculatedVatClass;

          // Log VAT class information
          logger.info(`  - Provided OptionVATClass: ${providedVatClass || 'None'}`);
          logger.info(`  - Calculated OptionVATClass: ${calculatedVatClass}`);
          logger.info(`  - Final OptionVATClass: ${vatClass}`);

          // Set HS code based on exemption status
          let hsCode = '';
          if (isExempt) {
            // Always use the default HS code for exempt items
            hsCode = '0016.21.00';
            logger.info(`Raw item ${item.NamePLU || item.name} is VAT exempt, using HS code 0016.21.00`);
          } else {
            hsCode = item.HSCode || '';
          }

          // Format VAT rate as string for exempt items
          const formattedVatRate = isExempt ? "0" : effectiveVatRate;

          return {
            NamePLU: item.NamePLU || item.description || item.name || 'Unknown Product',
            OptionVATClass: vatClass,
            Price: parseFloat(item.Price || item.price || 0),
            MeasureUnit: item.MeasureUnit || 'Pcs',
            HSCode: hsCode,
            HSName: item.HSName || '',
            VATGrRate: formattedVatRate,
            Quantity: parseFloat(item.Quantity || item.quantity || 0),
            DiscAddP: item.DiscAddP || ''
          };
        });
      } else {
        // Format the sale data for KRA using our standard method
        formattedItems = this.formatItemsForKRA(saleData);
      }

      if (rawCustomerInfo) {
        // Use the raw customer info directly from the mobile app
        logger.info(`Using raw customer info from mobile app for sale ID ${saleData.id}`);

        // Ensure the sale ID is properly formatted for the TraderSystemInvNum
        const saleId = saleData.id || '';
        const traderSystemInvNum = `INV${saleId}`;

        logger.info(`Generated TraderSystemInvNum: ${traderSystemInvNum} for sale ID ${saleId}`);

        customerInfo = {
          CompanyName: rawCustomerInfo.CompanyName || rawCustomerInfo.company_name || 'Walk-in Customer',
          ClientPINnum: rawCustomerInfo.ClientPINnum || rawCustomerInfo.pin_number || '',
          HeadQuarters: rawCustomerInfo.HeadQuarters || '',
          Address: rawCustomerInfo.Address || rawCustomerInfo.address || '',
          PostalCodeAndCity: rawCustomerInfo.PostalCodeAndCity || '00100 Nairobi',
          ExemptionNum: rawCustomerInfo.ExemptionNum || '',
          TraderSystemInvNum: traderSystemInvNum
        };
      } else {
        // Format the customer info using our standard method
        customerInfo = this.formatCustomerInfoForKRA(saleData);
      }

      // Prepare the request data (remove saleId and id as they may cause 500 errors)
      const requestData = {
        items: formattedItems,
        customerInfo
      };

      logger.info(`Formatted KRA request data: ${JSON.stringify(requestData, null, 2)}`);

      // Make the request to the new KRA endpoint
      const kraEndpoint = process.env.KRA_PROCESS_SALE_ENDPOINT || 'http://*************:3000/api/process-sale';

      // Create request options
      const requestOptions = {
        headers: {
          'Content-Type': 'application/json'
        }
      };

      // Send the request
      logger.info(`Sending request to KRA endpoint: ${kraEndpoint}`);
      const response = await axios.post(kraEndpoint, requestData, requestOptions);

      logger.info(`KRA response received: ${JSON.stringify(response.data, null, 2)}`);

      // Extract the KRA data from the response
      const kraData = response.data.kraData || response.data;

      logger.info(`Received KRA data for sale ID ${saleData.id}`);

      // Clean up the QR code URL (remove trailing spaces)
      const qrCodeUrl = kraData.qrCode ? kraData.qrCode.trim() : '';

      logger.info(`QR Code URL (after trimming): "${qrCodeUrl}"`);

      // Generate QR code image
      logger.info(`Generating QR code image for URL: ${qrCodeUrl}`);
      const qrCodeImage = await this.generateQRCodeForUrl(qrCodeUrl);

      // Log whether QR code image was generated
      logger.info(`QR code image generated: ${qrCodeImage ? 'Yes' : 'No'}`);
      if (!qrCodeImage) {
        logger.warn(`Failed to generate QR code image for URL: ${qrCodeUrl}`);

        // If QR code generation failed but we have a URL, try again with a simplified URL
        if (qrCodeUrl) {
          logger.info(`Retrying QR code generation with simplified URL`);
          const simplifiedUrl = qrCodeUrl.split('?')[0]; // Remove query parameters
          const retryQrCodeImage = await this.generateQRCodeForUrl(simplifiedUrl);

          if (retryQrCodeImage) {
            logger.info(`Successfully generated QR code with simplified URL: ${simplifiedUrl}`);
            return {
              success: true,
              verificationCode: kraData.cuNumber || '',
              fiscalReceiptNumber: kraData.invoiceNum || '',
              verificationUrl: qrCodeUrl, // Keep the original URL for verification
              qrCodeUrl: qrCodeUrl,       // Also store as qrCodeUrl for compatibility
              qrCodeImage: retryQrCodeImage,
              timestamp: kraData.dateTime ? new Date(kraData.dateTime) : new Date(),
              offline: false,
              fullResponseData: JSON.stringify(kraData)
            };
          }
        }
      }

      // Return the KRA data in the expected format
      return {
        success: true,
        verificationCode: kraData.cuNumber || '',
        fiscalReceiptNumber: kraData.invoiceNum || '',
        verificationUrl: qrCodeUrl,
        qrCodeUrl: qrCodeUrl,       // Also store as qrCodeUrl for compatibility
        qrCodeImage,
        timestamp: kraData.dateTime ? new Date(kraData.dateTime) : new Date(),
        offline: false,
        fullResponseData: JSON.stringify(kraData)
      };
    } catch (error) {
      logger.error(`Error using new KRA endpoint: ${error.message}`);
      logger.error(error.stack);

      // Fall back to offline mode
      return await this.generateOfflineResponse(saleData);
    }
  }

  /**
   * Format sale items for KRA
   * @param {Object} saleData - The sale data
   * @returns {Array} - Formatted items
   */
  formatItemsForKRA(saleData) {
    const { SaleItems } = saleData;

    // Log the sale data for debugging
    logger.info(`Formatting items for KRA from sale ID ${saleData.id}`);
    logger.info(`Sale items count: ${SaleItems?.length || 0}`);

    if (!SaleItems || SaleItems.length === 0) {
      logger.warn(`No sale items found for sale ID ${saleData.id}`);
      return [];
    }

    return SaleItems.map(item => {
      // For invoices, item.Product contains the description string
      // For sales, item.Product is an object with product details
      const product = typeof item.Product === 'string' ? null : item.Product;
      const productDescription = typeof item.Product === 'string' ? item.Product : null;

      if (!product && !productDescription) {
        logger.warn(`Product/description not found for sale item in sale ID ${saleData.id}`);
      }

      // Extract VAT information - use nullish coalescing to handle 0 values properly
      const vatRate = item.vat_rate !== undefined && item.vat_rate !== null ? item.vat_rate : 16;

      // Log the raw VAT rate for debugging
      logger.info(`Raw VAT rate for product ${product?.name || 'Unknown'}: ${item.vat_rate} (type: ${typeof item.vat_rate}), Processed vatRate: ${vatRate}`);

      // Check if item is explicitly marked as VAT exempt or has a zero VAT rate
      const isExemptByProperty = item.is_vat_exempt === true || product?.is_vat_exempt === true;
      const isExemptByRate = vatRate === 0 || vatRate === '0' || vatRate === 0.0 || vatRate === '0.0' || vatRate === '0.00';
      const isExempt = isExemptByProperty || isExemptByRate;

      // Set VAT class based on exemption status
      const vatClass = isExempt ? 'E' : 'A'; // A for taxable, E for exempt

      // Log detailed exemption status
      logger.info(`Product ${product?.name || 'Unknown'} (ID: ${product?.id || 'N/A'}):`);
      logger.info(`  - is_vat_exempt property: ${isExemptByProperty ? 'Yes' : 'No'}`);
      logger.info(`  - VAT rate is zero: ${isExemptByRate ? 'Yes' : 'No'} (rate: ${vatRate})`);
      logger.info(`  - Final exemption status: ${isExempt ? 'Exempt' : 'Taxable'}`);
      logger.info(`  - OptionVATClass: ${vatClass}`);

      // If product is exempt, ensure VAT rate is 0, otherwise convert to number
      const effectiveVatRate = isExempt ? 0 : Number(vatRate);

      // Extract product details - prioritize description from invoice items
      const productName = productDescription || item.description || product?.name || item.product_name || 'Unknown Product';

      // Set HSCode based on VAT status
      let hsCode = '';
      if (isExempt) {
        // Always use the default HS code for exempt items
        hsCode = '0016.21.00';
        logger.info(`Item ${productName} is VAT exempt, using HS code 0016.21.00`);
      } else {
        hsCode = product?.hs_code || item.hs_code || '';
      }

      // Extract pricing and quantity
      const unitPrice = parseFloat(item.unit_price) || 0;
      const quantity = parseFloat(item.quantity) || 0;

      // Format VAT rate as string for exempt items, number for taxable items (consistent with mobile app)
      const formattedVatRate = isExempt ? "0" : effectiveVatRate;

      // Log the formatted item for debugging
      logger.info(`Formatted KRA item: ${productName}, Price: ${unitPrice}, Qty: ${quantity}, VAT: ${effectiveVatRate}%, Class: ${vatClass}, HSCode: ${hsCode}`);
      logger.info(`  - formattedVatRate: ${formattedVatRate} (type: ${typeof formattedVatRate})`);

      return {
        NamePLU: productName,
        OptionVATClass: vatClass,
        Price: unitPrice,
        MeasureUnit: 'Pcs',
        HSCode: hsCode,
        HSName: '',
        VATGrRate: formattedVatRate,
        Quantity: quantity,
        DiscAddP: item.discount_amount ? (item.discount_amount / (unitPrice * quantity) * 100).toFixed(2) : ''
      };
    });
  }

  /**
   * Format customer info for KRA
   * @param {Object} saleData - The sale data
   * @returns {Object} - Formatted customer info
   */
  formatCustomerInfoForKRA(saleData) {
    const { Customer, Branch, customer_name, customer_pin } = saleData;

    // Log the customer data for debugging
    logger.info(`Formatting customer info for KRA from sale ID ${saleData.id}`);

    // Prioritize customer data from the sale request
    const companyName = customer_name || Customer?.name || 'Walk-in Customer';
    const pinNumber = customer_pin || Customer?.pin_number || '';
    const branchName = Branch?.name || '';
    const address = Customer?.address || Branch?.location || '';
    const postalCode = Customer?.postal_code || '00100 Nairobi';

    // Log the formatted customer info for debugging
    logger.info(`Formatted KRA customer: ${companyName}, PIN: ${pinNumber}`);
    logger.info(`Customer PIN sources - customer_pin: ${customer_pin}, Customer.pin_number: ${Customer?.pin_number}`);

    // Use the actual receipt/invoice number if available, fallback to INV + ID
    const saleId = saleData.id || '';
    const traderSystemInvNum =  `INV${saleId}`;

    logger.info(`Generated TraderSystemInvNum: ${traderSystemInvNum} for sale/invoice ID ${saleId}`);
    logger.info(`Receipt number source: ${saleData.receipt_number ? 'actual receipt_number' : 'generated from ID'}`);

    return {
      CompanyName: companyName,
      ClientPINnum: pinNumber,
      HeadQuarters: branchName,
      Address: address,
      PostalCodeAndCity: postalCode,
      ExemptionNum: '',
      TraderSystemInvNum: traderSystemInvNum
    };
  }

  /**
   * Use the KRA credit note integration endpoint
   * @param {Object} creditNoteData - The credit note data to register
   * @returns {Promise<Object>} - KRA verification data
   */
  async useCreditNoteKraEndpoint(creditNoteData) {
    try {
      logger.info(`Using KRA credit note endpoint for credit note ID ${creditNoteData.id}`);

      // Format the credit note data for KRA
      const formattedData = await this.formatCreditNoteForKRA(creditNoteData);

      logger.info(`Formatted KRA credit note request data: ${JSON.stringify(formattedData, null, 2)}`);

      // Make the request to the KRA credit note endpoint
      const kraEndpoint = process.env.KRA_PROCESS_CREDIT_NOTE_ENDPOINT || 'http://*************:3000/api/process-credit-note';

      // Create request options
      const requestOptions = {
        headers: {
          'Content-Type': 'application/json'
        }
      };

      // Send the request
      logger.info(`Sending request to KRA credit note endpoint: ${kraEndpoint}`);
      logger.info(`Request headers: ${JSON.stringify(requestOptions.headers, null, 2)}`);
      logger.info(`Request payload: ${JSON.stringify(formattedData, null, 2)}`);

      const response = await axios.post(kraEndpoint, formattedData, requestOptions);

      logger.info(`KRA credit note response received: ${JSON.stringify(response.data, null, 2)}`);

      // Extract the KRA data from the response
      const kraData = response.data.data || response.data;

      logger.info(`Received KRA credit note data for credit note ID ${creditNoteData.id}`);

      // Clean up the QR code URL (remove trailing spaces)
      const qrCodeUrl = kraData.QRCode ? kraData.QRCode.trim() : '';

      logger.info(`Credit note QR Code URL (after trimming): "${qrCodeUrl}"`);

      // Generate QR code image
      logger.info(`Generating QR code image for credit note URL: ${qrCodeUrl}`);
      const qrCodeImage = await this.generateQRCodeForUrl(qrCodeUrl);

      // Log whether QR code image was generated
      logger.info(`Credit note QR code image generated: ${qrCodeImage ? 'Yes' : 'No'}`);

      // Return the KRA data in the expected format
      return {
        success: true,
        verification_code: kraData.cuNumber || '',
        fiscal_receipt_number: kraData.CU_Invoice_N || '',
        verification_url: qrCodeUrl,
        qrCodeUrl: qrCodeUrl,
        qrCodeImage,
        timestamp: kraData.dateTime ? new Date(kraData.dateTime) : new Date(),
        offline: false,
        fullResponseData: JSON.stringify(kraData),
        // Additional credit note specific fields
        related_invoice_number: kraData.RelatedInvoiceNum || '',
        type: kraData.type || 'credit_note'
      };
    } catch (error) {
      logger.error(`Error using KRA credit note endpoint: ${error.message}`);

      // Log detailed error information
      if (error.response) {
        logger.error(`KRA API Response Status: ${error.response.status}`);
        logger.error(`KRA API Response Headers: ${JSON.stringify(error.response.headers, null, 2)}`);
        logger.error(`KRA API Response Data: ${JSON.stringify(error.response.data, null, 2)}`);
      } else if (error.request) {
        logger.error(`KRA API Request failed - no response received`);
        logger.error(`Request details: ${JSON.stringify(error.request, null, 2)}`);
      } else {
        logger.error(`KRA API Error during request setup: ${error.message}`);
      }

      logger.error(error.stack);

      // Fall back to offline mode
      return await this.generateOfflineResponse(creditNoteData);
    }
  }

  /**
   * Format credit note data for KRA
   * @param {Object} creditNoteData - The credit note data
   * @returns {Promise<Object>} - Formatted credit note data
   */
  async formatCreditNoteForKRA(creditNoteData) {
    logger.info(`Formatting credit note for KRA from credit note ID ${creditNoteData.id}`);

    // Get customer information
    const customer = creditNoteData.Customer || creditNoteData.customer;
    const supplier = creditNoteData.Supplier || creditNoteData.supplier;

    // Determine buyer information based on reference type
    let buyerCompanyName = 'Walk-in Customer';
    let buyerPIN = '';

    // For invoices, check if it's a supplier invoice and get PIN from supplier table
    if (creditNoteData.reference_type === 'invoice') {
      // For invoice credit notes, we need to fetch the invoice to determine if it's a supplier invoice
      try {
        const { Invoice } = require('../models');
        const originalInvoice = await Invoice.findByPk(creditNoteData.reference_id, {
          include: [
            { model: require('../models/supplier.model'), as: 'supplier' },
            { model: require('../models/customer.model'), as: 'customer' }
          ]
        });

        if (originalInvoice) {
          if (originalInvoice.type === 'supplier' && originalInvoice.supplier) {
            // Supplier invoice - get PIN from supplier table using 'krapin' field
            buyerCompanyName = originalInvoice.supplier.name || 'Unknown Supplier';
            buyerPIN = originalInvoice.supplier.krapin || '';
            logger.info(`Credit note for supplier invoice: Company: ${buyerCompanyName}, PIN: ${buyerPIN}`);
          } else if (originalInvoice.customer) {
            // Customer invoice - get PIN from customer table using 'pin_number' field
            buyerCompanyName = originalInvoice.customer.name || 'Walk-in Customer';
            buyerPIN = originalInvoice.customer.pin_number || '';
            logger.info(`Credit note for customer invoice: Company: ${buyerCompanyName}, PIN: ${buyerPIN}`);
          }
        }
      } catch (error) {
        logger.error(`Error fetching invoice details for credit note: ${error.message}`);
        // Fall back to existing logic
        if (customer) {
          buyerCompanyName = customer.name || customer.company_name || 'Walk-in Customer';
          buyerPIN = customer.pin_number || '';
        } else if (supplier) {
          buyerCompanyName = supplier.name || supplier.company_name || 'Unknown Supplier';
          buyerPIN = supplier.krapin || ''; // Use krapin for supplier
        }
      }
    } else {
      // For sales credit notes, use customer information
      if (customer) {
        buyerCompanyName = customer.name || customer.company_name || 'Walk-in Customer';
        buyerPIN = customer.pin_number || '';
      } else if (supplier) {
        buyerCompanyName = supplier.name || supplier.company_name || 'Unknown Supplier';
        buyerPIN = supplier.krapin || ''; // Use krapin for supplier
      }
    }

    // Get the original document's KRA fiscal receipt number
    let originalKraFiscalReceiptNumber = '';
    try {
      const { Sale, Invoice } = require('../models');

      if (creditNoteData.reference_type === 'sale') {
        const originalSale = await Sale.findByPk(creditNoteData.reference_id, {
          attributes: ['id', 'receipt_number', 'kra_fiscal_receipt_number']
        });

        if (originalSale && originalSale.kra_fiscal_receipt_number) {
          originalKraFiscalReceiptNumber = originalSale.kra_fiscal_receipt_number;
          logger.info(`Found original sale KRA fiscal receipt number: ${originalKraFiscalReceiptNumber}`);
        } else {
          logger.warn(`Original sale ${creditNoteData.reference_id} not found or missing KRA fiscal receipt number`);
        }
      } else if (creditNoteData.reference_type === 'invoice') {
        const originalInvoice = await Invoice.findByPk(creditNoteData.reference_id, {
          attributes: ['id', 'invoice_number', 'kra_fiscal_receipt_number']
        });

        if (originalInvoice && originalInvoice.kra_fiscal_receipt_number) {
          originalKraFiscalReceiptNumber = originalInvoice.kra_fiscal_receipt_number;
          logger.info(`Found original invoice KRA fiscal receipt number: ${originalKraFiscalReceiptNumber}`);
        } else {
          logger.warn(`Original invoice ${creditNoteData.reference_id} not found or missing KRA fiscal receipt number`);
        }
      }
    } catch (error) {
      logger.error(`Error fetching original document KRA data: ${error.message}`);
    }

    // Format items
    const items = (creditNoteData.items || []).map(item => {
      const product = item.Product;

      // Extract VAT information - use nullish coalescing to handle 0 values properly
      const vatRate = item.vat_rate !== undefined && item.vat_rate !== null ? item.vat_rate : 16;

      // Log the raw VAT rate for debugging
      logger.info(`Credit note item ${item.description || 'Unknown'}: Raw vat_rate: ${item.vat_rate} (type: ${typeof item.vat_rate}), Processed vatRate: ${vatRate}`);

      // Check if item is explicitly marked as VAT exempt or has a zero VAT rate
      const isExemptByProperty = item.is_vat_exempt === true || product?.is_vat_exempt === true;
      const isExemptByRate = vatRate === 0 || vatRate === '0' || vatRate === 0.0 || vatRate === '0.0' || vatRate === '0.00';
      const isExempt = isExemptByProperty || isExemptByRate;

      // Set VAT class and HS code based on exemption status
      const vatClass = isExempt ? 'E' : 'A';
      const hsCode = isExempt ? '0016.21.00' : (product?.hs_code || '');

      // Format VAT rate as string for exempt items, number for taxable items
      const formattedVatRate = isExempt ? "0" : Number(vatRate);

      logger.info(`Credit note item ${item.description || 'Unknown'}:`);
      logger.info(`  - is_vat_exempt property: ${isExemptByProperty ? 'Yes' : 'No'}`);
      logger.info(`  - VAT rate is zero: ${isExemptByRate ? 'Yes' : 'No'} (rate: ${vatRate})`);
      logger.info(`  - Final exemption status: ${isExempt ? 'Exempt' : 'Taxable'}`);
      logger.info(`  - OptionVATClass: ${vatClass}, VATGrRate: ${formattedVatRate} (type: ${typeof formattedVatRate})`);

      return {
        NamePLU: item.description || product?.name || 'Unknown Product',
        OptionVATClass: vatClass,
        Price: parseFloat(item.unit_price) || 0,
        VATGrRate: formattedVatRate,
        HSCode: hsCode,
        MeasureUnit: 'Pcs',
        Quantity: parseFloat(item.quantity) || 0,
        DiscAddP: ''
      };
    });

    logger.info(`Formatted KRA credit note: ${buyerCompanyName}, PIN: ${buyerPIN}, Items: ${items.length}`);
    logger.info(`Original KRA fiscal receipt number: ${originalKraFiscalReceiptNumber || 'Not found'}`);
    logger.info(`Reference type: ${creditNoteData.reference_type}, Reference ID: ${creditNoteData.reference_id}, Reference number: ${creditNoteData.reference_number}`);

    return {
      Buyer_Company_Name: buyerCompanyName,
      Buyer_PIN: buyerPIN,
      CU_Invoice_N: originalKraFiscalReceiptNumber, // KRA fiscal receipt number from original invoice/sale
      Invoice_Nr: creditNoteData.reference_number, // Original invoice/sale number (e.g., "INV100", "CORP-123")
      items: items
    };
  }
}

module.exports = KraTimsService;
