const mysql = require('mysql2/promise');
require('dotenv').config();

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${new Date().toISOString()} - ${msg}`)
};

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dukalink_api',
  port: process.env.DB_PORT || 3306
};

async function fixRbacReadOwnAttributes() {
  let connection;
  
  try {
    logger.info('🔧 Fixing RBAC read:own attribute issues...');
    
    // Connect to database
    logger.info('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    logger.info('Starting transaction...');
    await connection.beginTransaction();
    
    // Step 1: Find all read:own grants
    logger.info('=== STEP 1: Finding problematic read:own grants ===');
    const [readOwnGrants] = await connection.execute(`
      SELECT id, role, resource, action, attributes 
      FROM rbac_grants 
      WHERE action = 'read:own'
      ORDER BY role, resource
    `);
    
    logger.info(`✓ Found ${readOwnGrants.length} read:own grants to check`);
    
    // Step 2: Fix the attributes for read:own grants
    logger.info('=== STEP 2: Fixing read:own attributes ===');
    
    let fixedCount = 0;
    
    for (const grant of readOwnGrants) {
      try {
        // Parse current attributes
        const currentAttrs = JSON.parse(grant.attributes);
        
        // For read:own actions, we need to use wildcard attributes to avoid AccessControl validation issues
        // The AccessControl library is very strict about read:own attributes and expects them to match
        // actual resource properties, which is difficult to maintain across different resources
        const newAttributes = { '*': true };
        
        // Update the grant
        await connection.execute(`
          UPDATE rbac_grants 
          SET attributes = ? 
          WHERE id = ?
        `, [JSON.stringify(newAttributes), grant.id]);
        
        logger.info(`✓ Fixed ${grant.role}:${grant.resource}:${grant.action}`);
        fixedCount++;
        
      } catch (parseError) {
        logger.error(`Failed to parse attributes for grant ${grant.id}: ${parseError.message}`);
        // Set to wildcard as fallback
        await connection.execute(`
          UPDATE rbac_grants 
          SET attributes = ? 
          WHERE id = ?
        `, ['{"*":true}', grant.id]);
        
        logger.info(`✓ Set fallback wildcard for ${grant.role}:${grant.resource}:${grant.action}`);
        fixedCount++;
      }
    }
    
    // Step 3: Check for any other problematic grants
    logger.info('=== STEP 3: Checking for other problematic grants ===');
    
    // Look for grants with empty or null attributes
    const [emptyGrants] = await connection.execute(`
      SELECT id, role, resource, action, attributes 
      FROM rbac_grants 
      WHERE attributes IS NULL OR attributes = '' OR attributes = '{}'
    `);
    
    if (emptyGrants.length > 0) {
      logger.warn(`Found ${emptyGrants.length} grants with empty attributes`);
      
      for (const grant of emptyGrants) {
        await connection.execute(`
          UPDATE rbac_grants 
          SET attributes = ? 
          WHERE id = ?
        `, ['{"*":true}', grant.id]);
        
        logger.info(`✓ Fixed empty attributes for ${grant.role}:${grant.resource}:${grant.action}`);
        fixedCount++;
      }
    }
    
    // Step 4: Verify float_manager grants are still correct
    logger.info('=== STEP 4: Verifying float_manager grants ===');
    
    const [floatManagerGrants] = await connection.execute(`
      SELECT role, resource, action, attributes 
      FROM rbac_grants 
      WHERE role = 'float_manager' AND (resource LIKE '%approval%' OR resource LIKE '%rejection%')
      ORDER BY resource, action
    `);
    
    logger.success(`✓ float_manager has ${floatManagerGrants.length} approval/rejection grants:`);
    floatManagerGrants.forEach(grant => {
      logger.info(`  - ${grant.resource} -> ${grant.action} (${grant.attributes})`);
    });
    
    // Step 5: Commit changes
    logger.info('=== STEP 5: Committing changes ===');
    await connection.commit();
    
    logger.success(`✅ Successfully fixed ${fixedCount} RBAC grants!`);
    
    // Step 6: Test the RBAC service
    logger.info('=== STEP 6: Testing RBAC service ===');
    
    try {
      // Add the project root to the require path
      const path = require('path');
      const projectRoot = path.resolve(__dirname, '..');
      process.env.NODE_PATH = projectRoot;
      require('module').Module._initPaths();
      
      const rbacService = require('../src/services/rbac.service');
      const ac = await rbacService.getAccessControl();
      
      const roles = Object.keys(ac.getGrants());
      logger.success(`✅ RBAC service loaded successfully with ${roles.length} roles!`);
      
      // Test float_manager permissions
      if (roles.includes('float_manager')) {
        logger.success('✅ float_manager role found in AccessControl!');
        
        try {
          const approvalPerm = ac.can('float_manager').createAny('banking_approval');
          logger.success(`✅ banking_approval permission: ${approvalPerm.granted ? 'GRANTED' : 'DENIED'}`);
          
          const rejectionPerm = ac.can('float_manager').createAny('banking_rejection');
          logger.success(`✅ banking_rejection permission: ${rejectionPerm.granted ? 'GRANTED' : 'DENIED'}`);
          
          if (approvalPerm.granted && rejectionPerm.granted) {
            logger.success('🎉 float_manager banking permissions are working!');
          } else {
            logger.error('❌ float_manager banking permissions not working');
          }
          
        } catch (permError) {
          logger.error(`Permission test failed: ${permError.message}`);
        }
        
      } else {
        logger.error('❌ float_manager role still not found in AccessControl');
        logger.info('Available roles:');
        roles.forEach(role => {
          logger.info(`  - ${role}`);
        });
      }
      
    } catch (rbacError) {
      logger.error(`RBAC service test failed: ${rbacError.message}`);
      logger.warn('The grants have been fixed in the database, but the RBAC service may need a restart');
    }
    
    logger.info('=== SUMMARY ===');
    logger.success(`✅ Fixed ${fixedCount} RBAC grants`);
    logger.success('✅ All read:own grants now use wildcard attributes');
    logger.success('✅ Empty/null attributes have been fixed');
    logger.success('✅ float_manager approval grants verified');
    
    logger.info('\n🔄 NEXT STEPS:');
    logger.info('1. Restart the application to reload RBAC grants');
    logger.info('2. Test banking approval with float_manager role');
    logger.info('3. Verify that AccessControl no longer throws attribute errors');
    
  } catch (error) {
    logger.error(`Error fixing RBAC attributes: ${error.message}`);
    if (connection) {
      logger.info('Rolling back transaction...');
      await connection.rollback();
    }
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info('Database connection closed.');
    }
  }
}

// Execute the script
if (require.main === module) {
  fixRbacReadOwnAttributes()
    .then(() => {
      logger.success('🎉 RBAC attribute fix completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`❌ RBAC attribute fix failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { fixRbacReadOwnAttributes };
