const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config();

// Add the project root to the require path
const projectRoot = path.resolve(__dirname, '..');
process.env.NODE_PATH = projectRoot;
require('module').Module._initPaths();

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${new Date().toISOString()} - ${msg}`)
};

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dukalink_api',
  port: process.env.DB_PORT || 3306
};

async function testRbacRoles() {
  let connection;
  
  try {
    logger.info('🔍 Testing RBAC roles and permissions...');
    
    // Connect to database
    logger.info('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Step 1: Check database grants for float_manager
    logger.info('=== STEP 1: Checking database grants ===');
    const [dbGrants] = await connection.execute(`
      SELECT role, resource, action, attributes 
      FROM rbac_grants 
      WHERE role = 'float_manager' 
      ORDER BY resource, action
    `);
    
    logger.info(`✓ Found ${dbGrants.length} grants in database for float_manager:`);
    dbGrants.forEach(grant => {
      logger.info(`  - ${grant.role} -> ${grant.action} ${grant.resource}`);
    });
    
    // Step 2: Load RBAC service and check AccessControl instance
    logger.info('=== STEP 2: Loading RBAC service ===');
    
    try {
      const rbacService = require('../src/services/rbac.service');
      const ac = await rbacService.getAccessControl();
      
      // Get all grants from AccessControl
      const acGrants = ac.getGrants();
      const roles = Object.keys(acGrants);
      
      logger.info(`✓ AccessControl instance loaded with ${roles.length} roles:`);
      roles.forEach(role => {
        logger.info(`  - ${role}`);
      });
      
      // Check if float_manager exists in AccessControl
      if (acGrants['float_manager']) {
        logger.success('✅ float_manager role found in AccessControl!');
        
        const floatManagerGrants = acGrants['float_manager'];
        logger.info('float_manager permissions in AccessControl:');
        
        Object.keys(floatManagerGrants).forEach(resource => {
          Object.keys(floatManagerGrants[resource]).forEach(action => {
            logger.info(`  - ${resource} -> ${action}`);
          });
        });
        
        // Test specific permissions
        logger.info('=== STEP 3: Testing specific permissions ===');
        
        try {
          const approvalPerm = ac.can('float_manager').createAny('banking_approval');
          logger.success(`✓ banking_approval permission: ${approvalPerm.granted ? 'GRANTED ✅' : 'DENIED ❌'}`);
          
          const rejectionPerm = ac.can('float_manager').createAny('banking_rejection');
          logger.success(`✓ banking_rejection permission: ${rejectionPerm.granted ? 'GRANTED ✅' : 'DENIED ❌'}`);
          
          if (approvalPerm.granted && rejectionPerm.granted) {
            logger.success('🎉 float_manager has all required banking permissions!');
            
            // Test the actual approval logic simulation
            logger.info('=== STEP 4: Simulating approval logic ===');
            
            // Get a test transaction
            const [transactions] = await connection.execute(`
              SELECT id, amount, banking_method, status 
              FROM banking_transactions 
              WHERE status = 'pending' AND deleted_at IS NULL 
              LIMIT 1
            `);
            
            if (transactions.length > 0) {
              const testTx = transactions[0];
              logger.info(`📝 Would approve transaction ${testTx.id} (${testTx.amount} via ${testTx.banking_method})`);
              logger.success('✅ Approval simulation would succeed!');
            } else {
              logger.info('📝 No pending transactions found, but approval logic would work');
              logger.success('✅ Approval simulation logic is ready!');
            }
            
          } else {
            logger.error('❌ float_manager missing required permissions');
          }
          
        } catch (permError) {
          logger.error(`Permission test failed: ${permError.message}`);
        }
        
      } else {
        logger.error('❌ float_manager role NOT found in AccessControl instance');
        logger.info('Available roles in AccessControl:');
        roles.forEach(role => {
          logger.info(`  - ${role}`);
        });
        
        // Check if the grants are being loaded correctly
        logger.info('=== Debugging: Checking grant loading ===');
        
        const floatManagerDbGrants = dbGrants.filter(g => g.role === 'float_manager');
        logger.info(`Database has ${floatManagerDbGrants.length} grants for float_manager`);
        
        if (floatManagerDbGrants.length > 0) {
          logger.warn('⚠️  Grants exist in database but not in AccessControl');
          logger.warn('This suggests an issue with the RBAC service grant loading');
        }
      }
      
    } catch (rbacError) {
      logger.error(`RBAC service error: ${rbacError.message}`);
      throw rbacError;
    }
    
    // Step 3: Test with company_admin (should work)
    logger.info('=== STEP 5: Testing with company_admin (control test) ===');
    
    try {
      const rbacService = require('../src/services/rbac.service');
      const ac = await rbacService.getAccessControl();
      
      const companyAdminApproval = ac.can('company_admin').createAny('banking_approval');
      logger.success(`✓ company_admin banking_approval: ${companyAdminApproval.granted ? 'GRANTED ✅' : 'DENIED ❌'}`);
      
      const companyAdminRejection = ac.can('company_admin').createAny('banking_rejection');
      logger.success(`✓ company_admin banking_rejection: ${companyAdminRejection.granted ? 'GRANTED ✅' : 'DENIED ❌'}`);
      
    } catch (error) {
      logger.error(`company_admin test failed: ${error.message}`);
    }
    
    logger.info('=== TEST SUMMARY ===');
    logger.info('✓ Database grants checked');
    logger.info('✓ RBAC service loaded');
    logger.info('✓ AccessControl instance analyzed');
    logger.info('✓ Permission tests completed');
    
  } catch (error) {
    logger.error(`Test failed: ${error.message}`);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info('Database connection closed.');
    }
  }
}

// Execute the test
if (require.main === module) {
  testRbacRoles()
    .then(() => {
      logger.success('🎉 RBAC role test completed!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`❌ RBAC role test failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { testRbacRoles };
