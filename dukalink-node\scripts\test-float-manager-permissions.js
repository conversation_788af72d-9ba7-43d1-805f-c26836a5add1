const mysql = require('mysql2/promise');
require('dotenv').config();

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`)
};

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dukalink_api',
  port: process.env.DB_PORT || 3306
};

async function testFloatManagerPermissions() {
  let connection;
  
  try {
    logger.info('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Test 1: Check if float_manager role exists
    logger.info('=== TEST 1: Checking if float_manager role exists ===');
    const [roles] = await connection.execute(
      'SELECT * FROM roles WHERE name = ?',
      ['float_manager']
    );
    
    if (roles.length === 0) {
      logger.error('float_manager role not found in roles table!');
      return;
    }
    
    logger.info(`✓ float_manager role found: ID ${roles[0].id}`);
    const floatManagerRoleId = roles[0].id;
    
    // Test 2: Check if users with float_manager role exist
    logger.info('=== TEST 2: Checking for users with float_manager role ===');
    const [users] = await connection.execute(
      'SELECT id, name, email FROM users WHERE role_id = ? AND deleted_at IS NULL',
      [floatManagerRoleId]
    );
    
    if (users.length === 0) {
      logger.warn('No users found with float_manager role');
    } else {
      logger.info(`✓ Found ${users.length} users with float_manager role:`);
      users.forEach(user => {
        logger.info(`  - ${user.name} (${user.email}) - ID: ${user.id}`);
      });
    }
    
    // Test 3: Check RBAC permissions for float_manager
    logger.info('=== TEST 3: Checking RBAC permissions for float_manager ===');
    const [permissions] = await connection.execute(
      'SELECT resource, action, attributes FROM rbac_grants WHERE role = ? ORDER BY resource, action',
      ['float_manager']
    );
    
    logger.info(`✓ Found ${permissions.length} RBAC permissions for float_manager:`);
    
    const approvalPermissions = permissions.filter(p => 
      p.resource.includes('approval') || p.resource.includes('rejection')
    );
    
    logger.info(`✓ Found ${approvalPermissions.length} approval/rejection permissions:`);
    approvalPermissions.forEach(perm => {
      logger.info(`  - ${perm.resource} -> ${perm.action}`);
    });
    
    // Test 4: Check specific banking approval permissions
    logger.info('=== TEST 4: Checking specific banking approval permissions ===');
    const requiredPermissions = [
      'banking_approval',
      'banking_rejection',
      'mpesa_float_approval',
      'mpesa_float_rejection',
      'cash_float_approval',
      'cash_float_rejection'
    ];
    
    const missingPermissions = [];
    for (const requiredPerm of requiredPermissions) {
      const found = permissions.find(p => p.resource === requiredPerm && p.action === 'create:any');
      if (!found) {
        missingPermissions.push(requiredPerm);
      } else {
        logger.info(`✓ ${requiredPerm} permission found`);
      }
    }
    
    if (missingPermissions.length > 0) {
      logger.error(`✗ Missing permissions: ${missingPermissions.join(', ')}`);
    } else {
      logger.info('✓ All required banking approval permissions found!');
    }
    
    // Test 5: Check if there are any pending banking transactions to test with
    logger.info('=== TEST 5: Checking for pending banking transactions ===');
    const [pendingTransactions] = await connection.execute(
      'SELECT id, amount, banking_method, status, created_at FROM banking_transactions WHERE status = ? AND deleted_at IS NULL LIMIT 5',
      ['pending']
    );
    
    if (pendingTransactions.length === 0) {
      logger.warn('No pending banking transactions found for testing');
    } else {
      logger.info(`✓ Found ${pendingTransactions.length} pending banking transactions for testing:`);
      pendingTransactions.forEach(tx => {
        logger.info(`  - ID: ${tx.id}, Amount: ${tx.amount}, Method: ${tx.banking_method}, Created: ${tx.created_at}`);
      });
    }
    
    // Test 6: Summary and recommendations
    logger.info('=== TEST SUMMARY ===');
    logger.info('✓ float_manager role exists');
    logger.info(`✓ ${users.length} users have float_manager role`);
    logger.info(`✓ ${permissions.length} total RBAC permissions granted`);
    logger.info(`✓ ${approvalPermissions.length} approval/rejection permissions`);
    
    if (missingPermissions.length === 0) {
      logger.info('✅ All required permissions are in place!');
    } else {
      logger.error(`❌ Missing ${missingPermissions.length} required permissions`);
    }
    
    logger.info('\n=== TESTING RECOMMENDATIONS ===');
    if (users.length === 0) {
      logger.info('1. Create a test user with float_manager role');
    } else {
      logger.info(`1. Use existing float_manager user: ${users[0].name} (ID: ${users[0].id})`);
    }
    
    if (pendingTransactions.length === 0) {
      logger.info('2. Create a test banking transaction to approve/reject');
    } else {
      logger.info(`2. Test approval with transaction ID: ${pendingTransactions[0].id}`);
    }
    
    logger.info('3. Test API endpoints:');
    logger.info('   - POST /api/v1/banking/:id/approve');
    logger.info('   - POST /api/v1/banking/:id/reject');
    logger.info('4. Verify that float_manager can approve/reject transactions');
    logger.info('5. Verify that other roles without permission cannot approve/reject');
    
  } catch (error) {
    logger.error(`Error testing permissions: ${error.message}`);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info('Database connection closed.');
    }
  }
}

// Execute the script
if (require.main === module) {
  testFloatManagerPermissions()
    .then(() => {
      logger.info('Permission test completed!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`Permission test failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { testFloatManagerPermissions };
