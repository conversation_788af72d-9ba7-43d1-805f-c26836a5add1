(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/features/auth/hooks/use-permissions.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACTIONS": (()=>ACTIONS),
    "RESOURCES": (()=>RESOURCES),
    "SCOPE": (()=>SCOPE),
    "usePermissions": (()=>usePermissions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/auth/hooks/use-auth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$permission$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/auth/context/permission-context.tsx [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
;
const RESOURCES = {
    USERS: "users",
    ROLES: "roles",
    PERMISSIONS: "permissions",
    PRODUCTS: "products",
    CATEGORIES: "categories",
    BRANDS: "brands",
    INVENTORY: "inventory",
    STOCK_ITEMS: "stock_items",
    BRANCHES: "branches",
    EMPLOYEES: "employees",
    BANKING: "banking",
    EXPENSES: "expenses",
    EXPENSE_FIRST_APPROVAL: "expense_first_approval",
    EXPENSE_SECOND_APPROVAL: "expense_second_approval",
    SALES: "sales",
    POS_SESSIONS: "pos_sessions",
    CUSTOMERS: "customers",
    PROFILE: "profile",
    STOCK_LOCATIONS: "stock_locations"
};
const ACTIONS = {
    CREATE: "create",
    READ: "read",
    UPDATE: "update",
    DELETE: "delete"
};
const SCOPE = {
    ANY: "any",
    OWN: "own"
};
function usePermissions() {
    _s();
    const { data: user, isLoading: isUserLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCurrentUser"])();
    const { permissions, isLoading: isPermissionsLoading, hasPermission, refreshPermissions, logAvailableGrants } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$permission$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePermissionContext"])();
    // Check if the user is a company admin
    const isCompanyAdmin = ()=>{
        if (!user) return false;
        // Check if the user has the company_admin role
        return user.role_name === "company_admin" || user.role_name === "tenant_admin" || user.role_name === "super_admin";
    };
    // Check if the user is a branch manager
    const isBranchManager = ()=>{
        if (!user) return false;
        // Check if the user has the branch_manager role
        return user.role_name === "branch_manager";
    };
    // Check if the user can manage stock locations (create, update, delete)
    const canManageStockLocations = ()=>{
        return hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) || hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) || hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) || isCompanyAdmin();
    };
    // Check if the user can view stock locations
    const canViewStockLocations = ()=>{
        // Check for explicit permission or fallback to any authenticated user
        return hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) || !!user;
    };
    // Check if the user can perform an action on a resource
    const can = (resource, action, scope = "any")=>{
        return hasPermission(resource, action, scope);
    };
    // Check if the user has any of the specified permissions
    const hasAnyPermission = (permissions)=>{
        return permissions.some(({ resource, action, scope = "any" })=>hasPermission(resource, action, scope));
    };
    // Check if the user has all of the specified permissions
    const hasAllPermissions = (permissions)=>{
        return permissions.every(({ resource, action, scope = "any" })=>hasPermission(resource, action, scope));
    };
    return {
        isCompanyAdmin,
        isBranchManager,
        canManageStockLocations,
        canViewStockLocations,
        can,
        hasPermission,
        hasAnyPermission,
        hasAllPermissions,
        refreshPermissions,
        logAvailableGrants,
        permissions,
        isLoading: isUserLoading || isPermissionsLoading,
        RESOURCES,
        ACTIONS,
        SCOPE
    };
}
_s(usePermissions, "dMIi2ScV3JgYjz9NvosgK3qJeY0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCurrentUser"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$permission$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePermissionContext"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/inventory/api/inventory-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "inventoryService": (()=>inventoryService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
const inventoryService = {
    getBranchInventory: async (branchId, params)=>{
        try {
            // Special case: branchId = -1 means "All Branches"
            if (branchId === -1) {
                console.log("Viewing all branches inventory");
                try {
                    const result = await inventoryService.getAllBranchesInventory(params);
                    console.log("All branches inventory result:", result);
                    return result;
                } catch (error) {
                    console.error("Error fetching all branches inventory:", error);
                    throw error;
                }
            }
            // If no branch ID is provided, try to get the user's branch ID
            if (!branchId) {
                try {
                    // Get the current user to determine their branch
                    const currentUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/auth/me");
                    console.log("Current user for inventory:", currentUser);
                    // Type assertion to access branch_id
                    const userWithBranch = currentUser;
                    if (userWithBranch?.branch_id) {
                        console.log("Using user's branch_id:", userWithBranch.branch_id);
                        branchId = userWithBranch.branch_id;
                    } else {
                        console.log("No branch ID available, returning empty data");
                        return {
                            data: [],
                            pagination: {
                                total: 0,
                                page: 1,
                                limit: 0,
                                totalPages: 1
                            }
                        };
                    }
                } catch (error) {
                    console.error("Error getting user data:", error);
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            totalPages: 1
                        }
                    };
                }
            }
            // Use stock-items endpoint with branch_id for branch-specific inventory
            const queryParams = {
                ...params,
                branch_id: branchId
            };
            // Log search parameters for debugging
            if (params?.search || params?.product_name || params?.['product.name']) {
                console.log(`Searching inventory by product name:`, params?.product_name || params?.['product.name'] || params?.search);
                console.log(`Note: This searches for stock items where the associated product name matches the search term`);
            }
            console.log(`Calling API: /stock-items with params:`, queryParams);
            // Use the correct endpoint based on API structure
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/stock-items`, {
                    params: queryParams
                });
                console.log("Raw API response:", response);
                // Check if response is undefined or null
                if (!response) {
                    console.log("API response is undefined or null");
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            totalPages: 1
                        }
                    };
                }
                // The API returns a paginated response with data and pagination properties
                if (response && typeof response === "object" && "data" in response) {
                    console.log("Response is a paginated object with data property");
                    // Extract the data array and pagination info
                    const { data, pagination } = response;
                    // Ensure we have valid pagination values
                    const total = pagination.total || 0;
                    const page = pagination.page || 1;
                    const limit = pagination.limit || 10;
                    // Calculate pages based on total and limit
                    const calculatedPages = Math.ceil(total / limit) || 1;
                    // Force at least 2 pages if we have more than 10 items total
                    const pages = total > 10 ? Math.max(2, calculatedPages) : calculatedPages;
                    console.log("Calculated pagination:", {
                        apiTotal: pagination.total,
                        apiPage: pagination.page,
                        apiLimit: pagination.limit,
                        apiPages: pagination.pages,
                        calculatedPages,
                        forcedPages: pages,
                        shouldShowPagination: pages > 1
                    });
                    return {
                        data: data,
                        pagination: {
                            total: total,
                            page: page,
                            limit: limit,
                            pages: pages
                        }
                    };
                } else if (Array.isArray(response)) {
                    console.log("Response is an array, converting to paginated format");
                    const limit = params?.limit ? parseInt(params.limit, 10) : 10;
                    const page = params?.page ? parseInt(params.page, 10) : 1;
                    const total = response.length;
                    // Always calculate pages based on total and limit
                    const calculatedPages = Math.ceil(total / limit) || 1;
                    // Make sure we have at least 1 page
                    const pages = calculatedPages;
                    console.log("Pages calculation (fallback):", {
                        calculatedPages,
                        finalPages: pages,
                        total,
                        limit,
                        shouldShowPagination: pages > 1
                    });
                    return {
                        data: response,
                        pagination: {
                            total: total,
                            page: page,
                            limit: limit,
                            pages: pages
                        }
                    };
                } else {
                    console.log("Unexpected response format:", response);
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 10,
                            pages: 1
                        }
                    };
                }
            // This code is unreachable due to the if-else above, but TypeScript doesn't know that
            // We'll keep it for safety
            } catch (error) {
                console.error("Error in API call to stock-items:", error);
                throw error;
            }
        } catch (error) {
            console.error("Error in getBranchInventory:", error);
            // Return empty data on error
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 10,
                    pages: 1
                }
            };
        }
    },
    getProductInventory: async (productId, params)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/products/${productId}/stock-items`, {
            params
        });
    },
    getInventoryTransactions: async (params)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/stock-movements", {
            params
        });
    },
    getInventoryTransactionById: async (id)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/stock-movements/${id}`);
    },
    createInventoryTransaction: async (transaction)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/stock-movements", transaction);
    },
    createStockItem: async (data)=>{
        // Ensure we're sending the required fields to the API
        const apiRequest = {
            // Required fields
            product_id: data.product_id,
            branch_id: data.branch_id,
            quantity: data.quantity,
            // Serial numbers for serialized products
            serial_numbers: data.serial_numbers,
            // Optional fields with backward compatibility
            quantity_reserved: data.quantity_reserved,
            buying_price: data.buying_price,
            default_buying_price: data.default_buying_price || data.buying_price,
            default_selling_price: data.default_selling_price,
            default_wholesale_price: data.default_wholesale_price,
            // VAT-related fields
            buying_price_including_vat: data.buying_price_including_vat,
            buying_price_excluding_vat: data.buying_price_excluding_vat,
            buying_vat_amount: data.buying_vat_amount,
            buying_vat_rate: data.buying_vat_rate,
            // Batch/lot tracking
            batch_number: data.batch_number,
            expiry_date: data.expiry_date,
            manufacturing_date: data.manufacturing_date,
            // Inventory management
            reorder_level: data.reorder_level,
            reorder_quantity: data.reorder_quantity,
            valuation_method: data.valuation_method,
            // Tenant information
            tenant_id: data.tenant_id
        };
        console.log('Creating stock item with data:', apiRequest);
        // For serialized products, we need to use a different endpoint
        if (data.serial_numbers && data.serial_numbers.length > 0) {
            console.log('Creating inventory items with serial numbers');
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/inventory-items", {
                stock_item_id: null,
                product_id: data.product_id,
                quantity: data.quantity,
                serial_numbers: data.serial_numbers,
                batch_number: data.batch_number,
                expiry_date: data.expiry_date
            });
        }
        // For non-serialized products, use the regular endpoint
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/stock-items", apiRequest);
    },
    updateStockItem: async (id, data)=>{
        // Ensure we're sending the required fields to the API
        const apiRequest = {
            quantity: data.quantity,
            buying_price: data.buying_price,
            selling_price: data.selling_price,
            status: data.status,
            is_in_transit: data.is_in_transit
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/stock-items/${id}`, apiRequest);
    },
    updateStockItemStatus: async (id, status, isInTransit = false)=>{
        // Update just the status of a stock item
        const apiRequest = {
            status: status,
            is_in_transit: isInTransit
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/stock-items/${id}/status`, apiRequest);
    },
    getInventoryAdjustments: async (params)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/stock-adjustments", {
            params
        });
    },
    getInventoryAdjustmentById: async (id)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/stock-adjustments/${id}`);
    },
    getStockAdjustmentTypes: async ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/stock-adjustments/types");
    },
    getStockItemsByBranch: async (branch_id)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/stock-items", {
            params: {
                branch_id
            }
        });
    },
    createInventoryAdjustment: async (adjustment)=>{
        // Ensure we're sending the required fields to the API
        const apiRequest = {
            reference_number: `ADJ-${Date.now()}`,
            location_id: adjustment.branch_id,
            adjustment_type_id: adjustment.adjustment_type_id,
            status: "pending",
            notes: adjustment.notes || "",
            requested_by: 1,
            items: adjustment.items.map((item)=>({
                    stock_item_id: item.stock_item_id,
                    quantity: item.quantity,
                    unit_cost: 0,
                    reason: item.reason
                }))
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/stock-adjustments", apiRequest);
    },
    updateInventoryAdjustmentStatus: async (id, status)=>{
        // Ensure we're sending the required fields to the API
        const apiRequest = {
            status: status.status,
            notes: status.notes || "",
            processed_by: status.processed_by || 1
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/stock-adjustments/${id}/process`, apiRequest);
    },
    getAllBranchesInventory: async (params)=>{
        try {
            console.log("Fetching all branches first");
            // First, get all branches
            const branchesResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/branches");
            const branches = Array.isArray(branchesResponse) ? branchesResponse : [];
            console.log(`Found ${branches.length} branches`);
            if (branches.length === 0) {
                return {
                    data: [],
                    pagination: {
                        total: 0,
                        page: 1,
                        limit: 0,
                        totalPages: 1
                    }
                };
            }
            // Fetch all inventory items at once without branch filter
            console.log("Fetching all inventory items");
            // Log search parameters for debugging
            if (params?.search || params?.product_name || params?.['product.name']) {
                console.log(`Searching all branches inventory by product name:`, params?.product_name || params?.['product.name'] || params?.search);
                console.log(`Note: This searches for stock items where the associated product name matches the search term`);
            }
            try {
                // Pass search parameters to the API
                const allInventoryResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/stock-items`, {
                    params: {
                        // Include all possible search parameters to ensure compatibility with different backend implementations
                        search: params?.search,
                        product_name: params?.product_name,
                        'product.name': params?.['product.name'],
                        // Include other parameters like pagination
                        page: params?.page,
                        limit: params?.limit
                    }
                });
                // Check if the response has the expected structure
                if (!allInventoryResponse || typeof allInventoryResponse !== "object" || !("data" in allInventoryResponse)) {
                    console.log("Unexpected response format:", allInventoryResponse);
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            totalPages: 1
                        }
                    };
                }
                // Extract the data array from the response
                const inventoryItems = allInventoryResponse.data || [];
                console.log(`Received ${inventoryItems.length} inventory items`);
                // Add branch information to each inventory item
                const inventoryWithBranchInfo = inventoryItems.map((item)=>{
                    // Find the branch for this item
                    const branch = branches.find((b)=>b.id === item.branch_id);
                    return {
                        ...item,
                        Branch: branch || {
                            id: item.branch_id,
                            name: "Unknown Branch",
                            location: "Unknown"
                        }
                    };
                });
                // Apply pagination manually
                const page = parseInt(params?.page || "1", 10);
                const limit = parseInt(params?.limit || "10", 10);
                const startIndex = (page - 1) * limit;
                const endIndex = startIndex + limit;
                const paginatedInventory = inventoryWithBranchInfo.slice(startIndex, endIndex);
                return {
                    data: paginatedInventory,
                    pagination: {
                        total: inventoryWithBranchInfo.length,
                        page,
                        limit,
                        pages: Math.ceil(inventoryWithBranchInfo.length / limit) || 1
                    }
                };
            } catch (apiError) {
                console.error("Error fetching inventory items:", apiError);
                return {
                    data: [],
                    pagination: {
                        total: 0,
                        page: 1,
                        limit: 0,
                        pages: 1
                    }
                };
            }
        } catch (error) {
            console.error("Error fetching inventory for all branches:", error);
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    pages: 1
                }
            };
        }
    },
    // Helper function to process HQ inventory response
    processHQInventoryResponse: (response)=>{
        console.log("Processing HQ inventory response");
        // Process array response
        if (Array.isArray(response)) {
            console.log("Response is an array with", response.length, "items");
            const mappedItems = response.map((item)=>{
                // Make sure Product is properly handled
                const product = item.Product || {};
                return {
                    id: item.id,
                    branch_id: 0,
                    product_id: item.product_id,
                    quantity: item.quantity,
                    buying_price: item.buying_price,
                    selling_price: item.selling_price,
                    // Add default price fields that the InventoryTable component looks for
                    default_selling_price: item.default_selling_price || item.selling_price,
                    default_buying_price: item.default_buying_price || item.buying_price,
                    default_wholesale_price: item.default_wholesale_price || item.wholesale_price,
                    created_at: item.created_at,
                    updated_at: item.updated_at,
                    deleted_at: item.deleted_at,
                    Branch: {
                        id: 0,
                        name: "Headquarters",
                        location: "HQ"
                    },
                    Product: product,
                    // Add reorder_level for consistency with branch inventory
                    reorder_level: product.reorder_level || 0
                };
            });
            console.log(`Mapped ${mappedItems.length} HQ stock items to branch inventory format`);
            return {
                data: mappedItems,
                pagination: {
                    total: mappedItems.length,
                    page: 1,
                    limit: mappedItems.length,
                    pages: 1
                }
            };
        }
        // Handle paginated response
        if (response && typeof response === "object" && "data" in response) {
            console.log("Response is a paginated object with data property");
            const { data, pagination } = response;
            const mappedItems = data.map((item)=>{
                // Make sure Product is properly handled
                const product = item.Product || {};
                return {
                    id: item.id,
                    branch_id: 0,
                    product_id: item.product_id,
                    quantity: item.quantity,
                    buying_price: item.buying_price,
                    selling_price: item.selling_price,
                    // Add default price fields that the InventoryTable component looks for
                    default_selling_price: item.default_selling_price || item.selling_price,
                    default_buying_price: item.default_buying_price || item.buying_price,
                    default_wholesale_price: item.default_wholesale_price || item.wholesale_price,
                    created_at: item.created_at,
                    updated_at: item.updated_at,
                    deleted_at: item.deleted_at,
                    Branch: {
                        id: 0,
                        name: "Headquarters",
                        location: "HQ"
                    },
                    Product: product,
                    // Add reorder_level for consistency with branch inventory
                    reorder_level: product.reorder_level || 0
                };
            });
            console.log(`Mapped ${mappedItems.length} HQ stock items from paginated response`);
            return {
                data: mappedItems,
                pagination: {
                    total: pagination.total,
                    page: pagination.page,
                    limit: pagination.limit,
                    pages: pagination.pages || 1
                }
            };
        }
        // Default fallback
        console.log("No HQ stock items found or unexpected response format");
        return {
            data: [],
            pagination: {
                total: 0,
                page: 1,
                limit: 0,
                pages: 1
            }
        };
    },
    getHQInventory: async (params)=>{
        try {
            console.log("Fetching HQ inventory with params:", params);
            // Use stock-items endpoint with branch_id=1 for headquarters stock
            const queryParams = {
                ...params,
                branch_id: 1
            };
            // Log search parameters for debugging
            if (params?.search || params?.product_name || params?.['product.name']) {
                console.log(`Searching HQ inventory by product name:`, params?.product_name || params?.['product.name'] || params?.search);
                console.log(`Note: This searches for stock items where the associated product name matches the search term`);
            }
            console.log("Calling API: stock-items with params:", queryParams);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/stock-items", {
                params: queryParams
            });
            console.log("Stock items API call successful");
            // Process the response
            return inventoryService.processHQInventoryResponse(response);
        } catch (error) {
            console.error("Error fetching HQ inventory:", error);
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    pages: 1
                }
            };
        }
    },
    getInventoryReportSummary: async (filters)=>{
        try {
            console.log("Calling API: /reports/inventory/summary with filters:", filters);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/inventory/summary", {
                params: filters
            });
            console.log("Raw inventory report API response:", JSON.stringify(response, null, 2));
            // Return the response directly
            return response;
        } catch (error) {
            console.error("Error in getInventoryReportSummary:", error);
            // Return default data structure on error
            return {
                total_products: 0,
                total_value: 0,
                low_stock_count: 0,
                out_of_stock_count: 0,
                top_products: [],
                by_category: [],
                by_branch: []
            };
        }
    },
    getStockValuationReport: async (filters)=>{
        try {
            console.log("Calling API: /reports/inventory/valuation with filters:", filters);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/inventory/valuation", {
                params: filters
            });
            console.log("Raw stock valuation report API response:", JSON.stringify(response, null, 2));
            // Return the response directly
            return response;
        } catch (error) {
            console.error("Error in getStockValuationReport:", error);
            // Return default data structure on error
            return {
                summary: {
                    total_items: 0,
                    total_quantity: 0,
                    total_value: 0,
                    valuation_methods: {
                        FIFO: {
                            count: 0,
                            value: 0
                        },
                        LIFO: {
                            count: 0,
                            value: 0
                        },
                        WEIGHTED_AVERAGE: {
                            count: 0,
                            value: 0
                        }
                    }
                },
                items: [],
                by_category: [],
                by_branch: [],
                by_valuation_method: []
            };
        }
    },
    // Get all branches
    getBranches: async ()=>{
        try {
            console.log("Fetching all branches");
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/branches");
            // Check if the response is an array (direct branches list)
            if (Array.isArray(response)) {
                console.log(`Received ${response.length} branches directly`);
                return {
                    data: response,
                    total: response.length
                };
            }
            // Check if the response has a data property (paginated response)
            if (response && typeof response === "object" && "data" in response) {
                console.log(`Received ${response.data.length} branches in paginated format`);
                const { data, pagination } = response;
                return {
                    data,
                    total: pagination?.total || data.length
                };
            }
            // Default fallback
            console.log("Unexpected branches response format, returning empty array");
            return {
                data: [],
                total: 0
            };
        } catch (error) {
            console.error("Error fetching branches:", error);
            return {
                data: [],
                total: 0
            };
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/inventory/hooks/use-inventory.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useBranchInventory": (()=>useBranchInventory),
    "useCreateInventoryAdjustment": (()=>useCreateInventoryAdjustment),
    "useCreateInventoryTransaction": (()=>useCreateInventoryTransaction),
    "useHQInventory": (()=>useHQInventory),
    "useInventoryAdjustment": (()=>useInventoryAdjustment),
    "useInventoryAdjustments": (()=>useInventoryAdjustments),
    "useInventoryReportSummary": (()=>useInventoryReportSummary),
    "useInventoryTransaction": (()=>useInventoryTransaction),
    "useInventoryTransactions": (()=>useInventoryTransactions),
    "useProductInventory": (()=>useProductInventory),
    "useStockAdjustmentTypes": (()=>useStockAdjustmentTypes),
    "useStockItemsByBranch": (()=>useStockItemsByBranch),
    "useStockValuationReport": (()=>useStockValuationReport),
    "useUpdateInventoryAdjustmentStatus": (()=>useUpdateInventoryAdjustmentStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/inventory/api/inventory-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature(), _s9 = __turbopack_context__.k.signature(), _s10 = __turbopack_context__.k.signature(), _s11 = __turbopack_context__.k.signature(), _s12 = __turbopack_context__.k.signature(), _s13 = __turbopack_context__.k.signature();
"use client";
;
;
;
function useHQInventory(params, isEnabled) {
    _s();
    console.log("useHQInventory called with:", {
        params,
        isEnabled
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "hqInventory",
            params
        ],
        queryFn: {
            "useHQInventory.useQuery": async ()=>{
                try {
                    console.log("Fetching HQ inventory");
                    console.log("With params:", params);
                    // Extract pagination parameters
                    const page = params?.page || 1;
                    const limit = params?.limit || 10;
                    console.log(`Pagination: page=${page}, limit=${limit}`);
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getHQInventory(params);
                    // Ensure pagination data is properly structured
                    if (!result.pagination) {
                        console.log("Adding pagination data to result");
                        result.pagination = {
                            total: result.data.length,
                            page: parseInt(page, 10),
                            limit: parseInt(limit, 10),
                            pages: Math.ceil(result.data.length / parseInt(limit, 10))
                        };
                    } else {
                        // Make sure we're using the correct page from the API response
                        console.log(`API returned page: ${result.pagination.page}, requested page: ${page}`);
                        // Always calculate pages based on total and limit
                        const calculatedPages = Math.ceil(result.pagination.total / parseInt(limit, 10)) || 1;
                        // Force at least 2 pages if we have more than 10 items total
                        const forcedPages = result.pagination.total > 10 ? Math.max(2, calculatedPages) : calculatedPages;
                        console.log(`Calculated pagination:`, {
                            apiPages: result.pagination.pages,
                            calculatedPages,
                            forcedPages,
                            total: result.pagination.total,
                            limit
                        });
                        // Always use our calculated pages value
                        result.pagination.pages = forcedPages;
                        // Ensure the page value matches what was requested
                        if (result.pagination.page !== parseInt(page, 10)) {
                            console.log(`Fixing page: ${result.pagination.page} -> ${page}`);
                            result.pagination.page = parseInt(page, 10);
                        }
                    }
                    console.log("HQ Inventory API response:", result);
                    console.log("Pagination data:", result.pagination);
                    return result;
                } catch (error) {
                    console.error("Error fetching HQ inventory:", error);
                    throw error;
                }
            }
        }["useHQInventory.useQuery"],
        enabled: isEnabled !== undefined ? isEnabled : true,
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false,
        select: {
            "useHQInventory.useQuery": (data)=>{
                if (!data) {
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            pages: 1
                        }
                    };
                }
                return data;
            }
        }["useHQInventory.useQuery"]
    });
}
_s(useHQInventory, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useBranchInventory(branchId, params, isEnabled) {
    _s1();
    console.log("useBranchInventory called with:", {
        branchId,
        params,
        isEnabled
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "branchInventory",
            branchId,
            params
        ],
        // Log when the query is triggered
        onFetch: {
            "useBranchInventory.useQuery": ()=>{
                console.log("Branch inventory query triggered:", {
                    branchId,
                    params
                });
            }
        }["useBranchInventory.useQuery"],
        queryFn: {
            "useBranchInventory.useQuery": async ()=>{
                try {
                    console.log("Fetching inventory for branch:", branchId);
                    console.log("With params:", params);
                    // This hook is now only for specific branches, not HQ
                    if (branchId === -1) {
                        console.log("This is a request for ALL branches inventory");
                    } else {
                        console.log(`This is a request for branch ${branchId} inventory`);
                    }
                    // Extract pagination parameters
                    const page = params?.page || 1;
                    const limit = params?.limit || 10;
                    console.log(`Pagination: page=${page}, limit=${limit}`);
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getBranchInventory(branchId, params);
                    // Ensure pagination data is properly structured
                    if (!result.pagination) {
                        console.log("Adding pagination data to result");
                        result.pagination = {
                            total: result.data.length,
                            page: parseInt(page, 10),
                            limit: parseInt(limit, 10),
                            pages: Math.ceil(result.data.length / parseInt(limit, 10))
                        };
                    } else {
                        // Make sure we're using the correct page from the API response
                        console.log(`API returned page: ${result.pagination.page}, requested page: ${page}`);
                        // Always calculate pages based on total and limit
                        const calculatedPages = Math.ceil(result.pagination.total / parseInt(limit, 10)) || 1;
                        // Force at least 2 pages if we have more than 10 items total
                        const forcedPages = result.pagination.total > 10 ? Math.max(2, calculatedPages) : calculatedPages;
                        console.log(`Calculated pagination:`, {
                            apiPages: result.pagination.pages,
                            calculatedPages,
                            forcedPages,
                            total: result.pagination.total,
                            limit
                        });
                        // Always use our calculated pages value
                        result.pagination.pages = forcedPages;
                        // Ensure the page value matches what was requested
                        if (result.pagination.page !== parseInt(page, 10)) {
                            console.log(`Fixing page: ${result.pagination.page} -> ${page}`);
                            result.pagination.page = parseInt(page, 10);
                        }
                        // For backward compatibility, set totalPages to pages
                        result.pagination.totalPages = result.pagination.pages;
                    }
                    console.log("Branch Inventory API response:", result);
                    console.log("Pagination data:", result.pagination);
                    return result;
                } catch (error) {
                    console.error("Error fetching branch inventory:", error);
                    throw error;
                }
            }
        }["useBranchInventory.useQuery"],
        enabled: ({
            "useBranchInventory.useQuery": ()=>{
                // Always enable the query if isEnabled is explicitly set
                if (isEnabled !== undefined) return isEnabled;
                // Otherwise, enable for any valid branchId (including -1 for All Branches)
                return branchId !== undefined;
            }
        })["useBranchInventory.useQuery"](),
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false,
        select: {
            "useBranchInventory.useQuery": (data)=>{
                if (!data) {
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            pages: 1
                        }
                    };
                }
                return data;
            }
        }["useBranchInventory.useQuery"]
    });
}
_s1(useBranchInventory, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useProductInventory(productId, params) {
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "productInventory",
            productId,
            params
        ],
        queryFn: {
            "useProductInventory.useQuery": async ()=>{
                if (!productId) {
                    console.log("Invalid productId provided to useProductInventory:", productId);
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            pages: 1
                        }
                    };
                }
                try {
                    console.log("Fetching inventory for product:", productId);
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getProductInventory(productId, params);
                    console.log("Product inventory API response:", result);
                    return result;
                } catch (error) {
                    console.error("Error fetching product inventory:", error);
                    throw error;
                }
            }
        }["useProductInventory.useQuery"],
        enabled: !!productId,
        // In v5, this is the equivalent of keepPreviousData
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false,
        // Provide a default value for data to prevent undefined errors
        select: {
            "useProductInventory.useQuery": (data)=>{
                if (!data) {
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            pages: 1
                        }
                    };
                }
                return data;
            }
        }["useProductInventory.useQuery"]
    });
}
_s2(useProductInventory, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useInventoryTransactions(params) {
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "inventoryTransactions",
            params
        ],
        queryFn: {
            "useInventoryTransactions.useQuery": async ()=>{
                try {
                    console.log("Fetching inventory transactions with params:", params);
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getInventoryTransactions(params);
                    console.log("Inventory transactions API response:", result);
                    return result;
                } catch (error) {
                    console.error("Error fetching inventory transactions:", error);
                    throw error;
                }
            }
        }["useInventoryTransactions.useQuery"],
        // In v5, this is the equivalent of keepPreviousData
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false,
        // Provide a default value for data to prevent undefined errors
        select: {
            "useInventoryTransactions.useQuery": (data)=>{
                if (!data) {
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            pages: 1
                        }
                    };
                }
                return data;
            }
        }["useInventoryTransactions.useQuery"]
    });
}
_s3(useInventoryTransactions, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useInventoryTransaction(id) {
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "inventoryTransaction",
            id
        ],
        queryFn: {
            "useInventoryTransaction.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getInventoryTransactionById(id)
        }["useInventoryTransaction.useQuery"],
        enabled: !!id
    });
}
_s4(useInventoryTransaction, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useCreateInventoryTransaction() {
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateInventoryTransaction.useMutation": (transaction)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].createInventoryTransaction(transaction)
        }["useCreateInventoryTransaction.useMutation"],
        onSuccess: {
            "useCreateInventoryTransaction.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "inventoryTransactions"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "branchInventory"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "productInventory"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Transaction created", {
                    description: "The inventory transaction has been created successfully."
                });
            }
        }["useCreateInventoryTransaction.useMutation"],
        onError: {
            "useCreateInventoryTransaction.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error creating transaction", {
                    description: error.message || "An error occurred while creating the transaction."
                });
            }
        }["useCreateInventoryTransaction.useMutation"]
    });
}
_s5(useCreateInventoryTransaction, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useInventoryAdjustments(params) {
    _s6();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "inventoryAdjustments",
            params
        ],
        queryFn: {
            "useInventoryAdjustments.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getInventoryAdjustments(params)
        }["useInventoryAdjustments.useQuery"],
        // In v5, this is the equivalent of keepPreviousData
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false
    });
}
_s6(useInventoryAdjustments, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useInventoryAdjustment(id) {
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "inventoryAdjustment",
            id
        ],
        queryFn: {
            "useInventoryAdjustment.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getInventoryAdjustmentById(id)
        }["useInventoryAdjustment.useQuery"],
        enabled: !!id
    });
}
_s7(useInventoryAdjustment, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useCreateInventoryAdjustment() {
    _s8();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateInventoryAdjustment.useMutation": (adjustment)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].createInventoryAdjustment(adjustment)
        }["useCreateInventoryAdjustment.useMutation"],
        onSuccess: {
            "useCreateInventoryAdjustment.useMutation": (data, variables)=>{
                // Invalidate all inventory-related queries to refresh data
                queryClient.invalidateQueries({
                    queryKey: [
                        "inventoryAdjustments"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "inventoryTransactions"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "branchInventory"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "hqInventory"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "productInventory"
                    ]
                });
                // Specifically invalidate stock items for the branch that was adjusted
                if (variables.branch_id) {
                    queryClient.invalidateQueries({
                        queryKey: [
                            "stockItems",
                            variables.branch_id
                        ]
                    });
                }
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Adjustment created", {
                    description: "The inventory adjustment has been created successfully and inventory has been updated."
                });
            }
        }["useCreateInventoryAdjustment.useMutation"],
        onError: {
            "useCreateInventoryAdjustment.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error creating adjustment", {
                    description: error.message || "An error occurred while creating the adjustment."
                });
            }
        }["useCreateInventoryAdjustment.useMutation"]
    });
}
_s8(useCreateInventoryAdjustment, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useUpdateInventoryAdjustmentStatus(id) {
    _s9();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateInventoryAdjustmentStatus.useMutation": (status)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].updateInventoryAdjustmentStatus(id, status)
        }["useUpdateInventoryAdjustmentStatus.useMutation"],
        onSuccess: {
            "useUpdateInventoryAdjustmentStatus.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "inventoryAdjustments"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "inventoryAdjustment",
                        id
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "branchInventory"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "productInventory"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Adjustment status updated", {
                    description: "The inventory adjustment status has been updated successfully."
                });
            }
        }["useUpdateInventoryAdjustmentStatus.useMutation"],
        onError: {
            "useUpdateInventoryAdjustmentStatus.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error updating adjustment status", {
                    description: error.message || "An error occurred while updating the adjustment status."
                });
            }
        }["useUpdateInventoryAdjustmentStatus.useMutation"]
    });
}
_s9(useUpdateInventoryAdjustmentStatus, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useInventoryReportSummary(filters) {
    _s10();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "inventoryReportSummary",
            filters
        ],
        queryFn: {
            "useInventoryReportSummary.useQuery": async ()=>{
                try {
                    console.log("Fetching inventory report summary with filters:", filters);
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getInventoryReportSummary(filters);
                    console.log("Inventory report summary API response:", JSON.stringify(result, null, 2));
                    return result;
                } catch (error) {
                    console.error("Error fetching inventory report summary:", error);
                    // Return default data structure on error
                    return {
                        total_products: 0,
                        total_value: 0,
                        low_stock_count: 0,
                        out_of_stock_count: 0,
                        top_products: [],
                        by_category: [],
                        by_branch: []
                    };
                }
            }
        }["useInventoryReportSummary.useQuery"],
        retry: 1,
        refetchOnWindowFocus: false
    });
}
_s10(useInventoryReportSummary, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useStockValuationReport(filters) {
    _s11();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "stockValuationReport",
            filters
        ],
        queryFn: {
            "useStockValuationReport.useQuery": async ()=>{
                try {
                    console.log("Fetching stock valuation report with filters:", filters);
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getStockValuationReport(filters);
                    console.log("Stock valuation report API response:", JSON.stringify(result, null, 2));
                    return result;
                } catch (error) {
                    console.error("Error fetching stock valuation report:", error);
                    // Return default data structure on error
                    return {
                        summary: {
                            total_items: 0,
                            total_quantity: 0,
                            total_value: 0,
                            valuation_methods: {
                                FIFO: {
                                    count: 0,
                                    value: 0
                                },
                                LIFO: {
                                    count: 0,
                                    value: 0
                                },
                                WEIGHTED_AVERAGE: {
                                    count: 0,
                                    value: 0
                                }
                            }
                        },
                        items: [],
                        by_category: [],
                        by_branch: [],
                        by_valuation_method: []
                    };
                }
            }
        }["useStockValuationReport.useQuery"],
        retry: 1,
        refetchOnWindowFocus: false
    });
}
_s11(useStockValuationReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useStockAdjustmentTypes() {
    _s12();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "stockAdjustmentTypes"
        ],
        queryFn: {
            "useStockAdjustmentTypes.useQuery": async ()=>{
                try {
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getStockAdjustmentTypes();
                    return result;
                } catch (error) {
                    console.error("Error fetching stock adjustment types:", error);
                    throw error;
                }
            }
        }["useStockAdjustmentTypes.useQuery"],
        retry: 1,
        refetchOnWindowFocus: false
    });
}
_s12(useStockAdjustmentTypes, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useStockItemsByBranch(branch_id) {
    _s13();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "stockItems",
            branch_id
        ],
        queryFn: {
            "useStockItemsByBranch.useQuery": async ()=>{
                if (!branch_id) return [];
                try {
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$api$2f$inventory$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["inventoryService"].getStockItemsByBranch(branch_id);
                    return Array.isArray(result) ? result : result.data || [];
                } catch (error) {
                    console.error("Error fetching stock items:", error);
                    return [];
                }
            }
        }["useStockItemsByBranch.useQuery"],
        enabled: !!branch_id,
        retry: 1,
        refetchOnWindowFocus: false
    });
}
_s13(useStockItemsByBranch, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/components/report-chart.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReportChart": (()=>ReportChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Bar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/Bar.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$BarChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/chart/BarChart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$CartesianGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/CartesianGrid.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$component$2f$Cell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/component/Cell.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/Line.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$LineChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/chart/LineChart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$polar$2f$Pie$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/polar/Pie.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$PieChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/chart/PieChart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$XAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/XAxis.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$YAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/YAxis.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/chart.tsx [app-client] (ecmascript)");
"use client";
;
;
;
;
;
function ReportChart({ title, description, data, chartTypes = [
    "line",
    "bar"
], defaultChartType = "line", height = 250, showLegend = true, showGrid = true, showTooltip = true, className, trendInfo }) {
    // Generate chart config for Shadcn UI chart
    const chartConfig = {};
    // Define colors for datasets
    const colors = [
        {
            light: "#3b82f6",
            dark: "#60a5fa"
        },
        {
            light: "#10b981",
            dark: "#34d399"
        },
        {
            light: "#f59e0b",
            dark: "#fbbf24"
        },
        {
            light: "#ef4444",
            dark: "#f87171"
        },
        {
            light: "#8b5cf6",
            dark: "#a78bfa"
        },
        {
            light: "#ec4899",
            dark: "#f472b6"
        },
        {
            light: "#06b6d4",
            dark: "#22d3ee"
        }
    ];
    // Create chart config for each dataset
    data.datasets.forEach((dataset, index)=>{
        chartConfig[dataset.label] = {
            label: dataset.label,
            theme: {
                light: dataset.borderColor || colors[index % colors.length].light,
                dark: colors[index % colors.length].dark
            }
        };
        // For pie charts, add colors for each label
        if (chartTypes.includes("pie")) {
            data.labels.forEach((label, labelIndex)=>{
                const labelKey = label.toString().replace(/\s+/g, "-").toLowerCase();
                if (!chartConfig[labelKey]) {
                    chartConfig[labelKey] = {
                        label: label.toString(),
                        theme: {
                            light: colors[labelIndex % colors.length].light,
                            dark: colors[labelIndex % colors.length].dark
                        }
                    };
                }
            });
        }
    });
    // Prepare data for charts
    const chartData = data.labels.map((label, i)=>({
            name: label,
            ...data.datasets.reduce((acc, dataset)=>({
                    ...acc,
                    [dataset.label]: dataset.data[i]
                }), {})
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                        lineNumber: 116,
                        columnNumber: 9
                    }, this),
                    description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-muted-foreground",
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                        lineNumber: 118,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                lineNumber: 115,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                    defaultValue: defaultChartType,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                            className: "mb-4",
                            children: [
                                chartTypes.includes("line") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                    value: "line",
                                    children: "Line"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 125,
                                    columnNumber: 15
                                }, this),
                                chartTypes.includes("bar") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                    value: "bar",
                                    children: "Bar"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 128,
                                    columnNumber: 15
                                }, this),
                                chartTypes.includes("pie") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                    value: "pie",
                                    children: "Pie"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 131,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                            lineNumber: 123,
                            columnNumber: 11
                        }, this),
                        chartTypes.includes("line") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                            value: "line",
                            className: "space-y-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartContainer"], {
                                config: chartConfig,
                                className: "h-[250px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$LineChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LineChart"], {
                                    accessibilityLayer: true,
                                    data: chartData,
                                    margin: {
                                        top: 10,
                                        right: 30,
                                        left: 20,
                                        bottom: 20
                                    },
                                    children: [
                                        showGrid && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$CartesianGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CartesianGrid"], {
                                            strokeDasharray: "3 3",
                                            vertical: false
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 147,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$XAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["XAxis"], {
                                            dataKey: "name",
                                            tickLine: false,
                                            axisLine: false,
                                            tickMargin: 10,
                                            tickFormatter: (value)=>typeof value === "string" && value.length > 10 ? `${value.slice(0, 10)}...` : value
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 149,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$YAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["YAxis"], {
                                            tickLine: false,
                                            axisLine: false,
                                            tickMargin: 10
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 160,
                                            columnNumber: 19
                                        }, this),
                                        showTooltip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltip"], {
                                            cursor: false,
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltipContent"], {
                                                hideLabel: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 164,
                                                columnNumber: 32
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 162,
                                            columnNumber: 21
                                        }, this),
                                        showLegend && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegend"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegendContent"], {}, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 169,
                                                columnNumber: 32
                                            }, void 0),
                                            verticalAlign: "top",
                                            height: 36
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 168,
                                            columnNumber: 21
                                        }, this),
                                        data.datasets.map((dataset)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Line"], {
                                                type: "natural",
                                                dataKey: dataset.label,
                                                stroke: `var(--color-${dataset.label})`,
                                                strokeWidth: 2,
                                                dot: {
                                                    fill: `var(--color-${dataset.label})`,
                                                    r: 4
                                                },
                                                activeDot: {
                                                    r: 6,
                                                    strokeWidth: 2
                                                }
                                            }, dataset.label, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 175,
                                                columnNumber: 21
                                            }, this))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 141,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 137,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                            lineNumber: 136,
                            columnNumber: 13
                        }, this),
                        chartTypes.includes("bar") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                            value: "bar",
                            className: "space-y-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartContainer"], {
                                config: chartConfig,
                                className: "h-[250px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$BarChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BarChart"], {
                                    accessibilityLayer: true,
                                    data: chartData,
                                    margin: {
                                        top: 10,
                                        right: 30,
                                        left: 20,
                                        bottom: 20
                                    },
                                    children: [
                                        showGrid && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$CartesianGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CartesianGrid"], {
                                            strokeDasharray: "3 3",
                                            vertical: false
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 208,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$XAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["XAxis"], {
                                            dataKey: "name",
                                            tickLine: false,
                                            axisLine: false,
                                            tickMargin: 10,
                                            tickFormatter: (value)=>typeof value === "string" && value.length > 10 ? `${value.slice(0, 10)}...` : value
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 210,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$YAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["YAxis"], {
                                            tickLine: false,
                                            axisLine: false,
                                            tickMargin: 10
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 221,
                                            columnNumber: 19
                                        }, this),
                                        showTooltip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltip"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltipContent"], {
                                                hideLabel: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 223,
                                                columnNumber: 44
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 223,
                                            columnNumber: 21
                                        }, this),
                                        showLegend && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegend"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegendContent"], {}, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 227,
                                                columnNumber: 32
                                            }, void 0),
                                            verticalAlign: "top",
                                            height: 36
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 226,
                                            columnNumber: 21
                                        }, this),
                                        data.datasets.map((dataset)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Bar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Bar"], {
                                                dataKey: dataset.label,
                                                fill: `var(--color-${dataset.label})`,
                                                radius: [
                                                    4,
                                                    4,
                                                    4,
                                                    4
                                                ],
                                                maxBarSize: 60
                                            }, dataset.label, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 233,
                                                columnNumber: 21
                                            }, this))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 202,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 198,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                            lineNumber: 197,
                            columnNumber: 13
                        }, this),
                        chartTypes.includes("pie") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                            value: "pie",
                            className: "space-y-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartContainer"], {
                                config: chartConfig,
                                className: "h-[250px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$PieChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PieChart"], {
                                    margin: {
                                        top: 10,
                                        right: 30,
                                        left: 20,
                                        bottom: 20
                                    },
                                    children: [
                                        data.datasets.map((dataset)=>{
                                            const pieData = data.labels.map((label, i)=>({
                                                    name: label,
                                                    value: dataset.data[i],
                                                    dataKey: dataset.label,
                                                    fill: `var(--color-${label.toString().replace(/\s+/g, "-").toLowerCase()})`
                                                }));
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$polar$2f$Pie$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Pie"], {
                                                data: pieData,
                                                cx: "50%",
                                                cy: "50%",
                                                outerRadius: 80,
                                                innerRadius: 30,
                                                paddingAngle: 2,
                                                dataKey: "value",
                                                nameKey: "name",
                                                label: ({ name, percent })=>percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : "",
                                                labelLine: false,
                                                className: "[&_.recharts-pie-label-text]:fill-foreground",
                                                children: pieData.map((entry, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$component$2f$Cell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Cell"], {
                                                        fill: entry.fill
                                                    }, `cell-${index}`, false, {
                                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                        lineNumber: 284,
                                                        columnNumber: 27
                                                    }, this))
                                            }, dataset.label, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 265,
                                                columnNumber: 23
                                            }, this);
                                        }),
                                        showTooltip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltip"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltipContent"], {
                                                hideLabel: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 290,
                                                columnNumber: 44
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 290,
                                            columnNumber: 21
                                        }, this),
                                        showLegend && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegend"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegendContent"], {}, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 294,
                                                columnNumber: 32
                                            }, void 0),
                                            verticalAlign: "bottom",
                                            height: 36,
                                            iconSize: 10,
                                            iconType: "circle"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 293,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 252,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 248,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                            lineNumber: 247,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                    lineNumber: 122,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                lineNumber: 121,
                columnNumber: 7
            }, this),
            trendInfo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t px-6 py-4 flex-col items-start gap-2 text-sm",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-2 font-medium leading-none",
                    children: trendInfo.isPositive ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            "Trending up by ",
                            Math.abs(trendInfo.value).toFixed(1),
                            "%",
                            " ",
                            trendInfo.label || "",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                xmlns: "http://www.w3.org/2000/svg",
                                viewBox: "0 0 24 24",
                                fill: "none",
                                stroke: "currentColor",
                                strokeWidth: "2",
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                className: "h-4 w-4 text-emerald-500",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "m6 9 6-6 6 6"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 324,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 12h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 325,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 15h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 326,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 18h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 327,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 314,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            "Trending down by ",
                            Math.abs(trendInfo.value).toFixed(1),
                            "%",
                            " ",
                            trendInfo.label || "",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                xmlns: "http://www.w3.org/2000/svg",
                                viewBox: "0 0 24 24",
                                fill: "none",
                                stroke: "currentColor",
                                strokeWidth: "2",
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                className: "h-4 w-4 text-red-500",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "m6 15 6 6 6-6"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 344,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 6h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 345,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 9h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 346,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 12h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 347,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 334,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                    lineNumber: 309,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                lineNumber: 308,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/reports/components/report-chart.tsx",
        lineNumber: 114,
        columnNumber: 5
    }, this);
}
_c = ReportChart;
var _c;
__turbopack_context__.k.register(_c, "ReportChart");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/components/report-data-table.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReportDataTable": (()=>ReportDataTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$data$2d$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/data-pagination.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$search$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/search-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/select.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/table.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$export$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/export-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-table/build/lib/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/table-core/build/lib/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-down.js [app-client] (ecmascript) <export default as ArrowDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-up.js [app-client] (ecmascript) <export default as ArrowUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
function ReportDataTable({ columns, data, title, description, searchPlaceholder = "Search...", searchColumn, exportFilename = "export", showExport = true, showSearch = true, showPagination = true, rowClassName, mobileBreakpoint = 768, enableMobileCards = true, pagination, isLoading = false }) {
    _s();
    const [sorting, setSorting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [columnFilters, setColumnFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Check for mobile view
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ReportDataTable.useEffect": ()=>{
            const checkMobile = {
                "ReportDataTable.useEffect.checkMobile": ()=>{
                    setIsMobile(window.innerWidth < mobileBreakpoint);
                }
            }["ReportDataTable.useEffect.checkMobile"];
            checkMobile();
            window.addEventListener("resize", checkMobile);
            return ({
                "ReportDataTable.useEffect": ()=>window.removeEventListener("resize", checkMobile)
            })["ReportDataTable.useEffect"];
        }
    }["ReportDataTable.useEffect"], [
        mobileBreakpoint
    ]);
    const table = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useReactTable"])({
        data,
        columns,
        getCoreRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCoreRowModel"])(),
        // Use server-side pagination if pagination prop is provided
        ...pagination ? {
            manualPagination: true,
            pageCount: pagination.totalPages
        } : {
            getPaginationRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPaginationRowModel"])()
        },
        onSortingChange: setSorting,
        getSortedRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSortedRowModel"])(),
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFilteredRowModel"])(),
        state: {
            sorting,
            columnFilters,
            ...pagination ? {
                pagination: {
                    pageIndex: pagination.currentPage - 1,
                    pageSize: pagination.pageSize || 10
                }
            } : {}
        }
    });
    const handleExport = ()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$export$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["downloadTableAsExcel"])(table, exportFilename);
    };
    // Render mobile card for a row
    const renderMobileCard = (row, index)=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
            className: "mb-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-2",
                    children: table.getAllColumns().map((column)=>{
                        const cell = row.getVisibleCells().find((c)=>c.column.id === column.id);
                        if (!cell) return null;
                        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(cell.column.columnDef.cell, cell.getContext());
                        // Skip empty values
                        if (!value || value === "-" || value === "") return null;
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-between items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm font-medium text-muted-foreground",
                                    children: [
                                        typeof column.columnDef.header === "string" ? column.columnDef.header : column.id,
                                        ":"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 164,
                                    columnNumber: 19
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm",
                                    children: value
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 170,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, column.id, true, {
                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                            lineNumber: 160,
                            columnNumber: 17
                        }, this);
                    })
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 144,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                lineNumber: 143,
                columnNumber: 9
            }, this)
        }, `mobile-card-${index}`, false, {
            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
            lineNumber: 142,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            (title || description || showSearch || showExport) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                className: "space-y-1",
                children: [
                    title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 184,
                        columnNumber: 21
                    }, this),
                    description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardDescription"], {
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 185,
                        columnNumber: 27
                    }, this),
                    (showSearch || showExport) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            showSearch && searchColumn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$search$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SearchInput"], {
                                placeholder: searchPlaceholder,
                                value: table.getColumn(searchColumn)?.getFilterValue() ?? "",
                                onSearch: (value)=>table.getColumn(searchColumn)?.setFilterValue(value),
                                onClear: ()=>table.getColumn(searchColumn)?.setFilterValue(""),
                                mode: "realtime",
                                className: "max-w-sm"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 189,
                                columnNumber: 17
                            }, this),
                            showExport && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: handleExport,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 208,
                                        columnNumber: 19
                                    }, this),
                                    "Export"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 207,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 187,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                lineNumber: 183,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                children: isMobile && enableMobileCards ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: isLoading || pagination?.isLoading ? // Show skeleton cards when loading
                    Array.from({
                        length: pagination?.pageSize || 10
                    }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                            className: "mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                className: "p-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 bg-muted animate-pulse rounded w-3/4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 227,
                                            columnNumber: 25
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 bg-muted animate-pulse rounded w-1/2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 228,
                                            columnNumber: 25
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 bg-muted animate-pulse rounded w-2/3"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 229,
                                            columnNumber: 25
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 226,
                                    columnNumber: 23
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 225,
                                columnNumber: 21
                            }, this)
                        }, `skeleton-card-${index}`, false, {
                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                            lineNumber: 224,
                            columnNumber: 19
                        }, this)) : table.getRowModel().rows?.length ? table.getRowModel().rows.map((row, index)=>renderMobileCard(row, index)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-8 text-muted-foreground",
                        children: "No data available"
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 240,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 219,
                    columnNumber: 11
                }, this) : /* Desktop Table Layout */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "rounded-md border overflow-x-auto",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Table"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHeader"], {
                                children: table.getHeaderGroups().map((headerGroup)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        children: headerGroup.headers.map((header)=>{
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                className: "whitespace-nowrap",
                                                children: header.isPlaceholder ? null : header.column.getCanSort() ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center space-x-1 cursor-pointer",
                                                    onClick: header.column.getToggleSortingHandler(),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(header.column.columnDef.header, header.getContext())
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                            lineNumber: 263,
                                                            columnNumber: 31
                                                        }, this),
                                                        header.column.getIsSorted() === "asc" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUp$3e$__["ArrowUp"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                            lineNumber: 270,
                                                            columnNumber: 33
                                                        }, this) : header.column.getIsSorted() === "desc" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowDown$3e$__["ArrowDown"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                            lineNumber: 272,
                                                            columnNumber: 33
                                                        }, this) : null
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                    lineNumber: 259,
                                                    columnNumber: 29
                                                }, this) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(header.column.columnDef.header, header.getContext())
                                            }, header.id, false, {
                                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                lineNumber: 254,
                                                columnNumber: 25
                                            }, this);
                                        })
                                    }, headerGroup.id, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 251,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 249,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableBody"], {
                                children: isLoading || pagination?.isLoading ? // Show skeleton rows when loading
                                Array.from({
                                    length: pagination?.pageSize || 10
                                }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        children: columns.map((_, colIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "h-4 bg-muted animate-pulse rounded"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                    lineNumber: 295,
                                                    columnNumber: 29
                                                }, this)
                                            }, `skeleton-cell-${colIndex}`, false, {
                                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                lineNumber: 294,
                                                columnNumber: 27
                                            }, this))
                                    }, `skeleton-${index}`, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 292,
                                        columnNumber: 23
                                    }, this)) : table.getRowModel().rows?.length ? table.getRowModel().rows.map((row)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        "data-state": row.getIsSelected() && "selected",
                                        className: rowClassName ? rowClassName(row) : "",
                                        children: row.getVisibleCells().map((cell)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(cell.column.columnDef.cell, cell.getContext())
                                            }, cell.id, false, {
                                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                lineNumber: 309,
                                                columnNumber: 25
                                            }, this))
                                    }, row.id, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 303,
                                        columnNumber: 21
                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                        colSpan: columns.length,
                                        className: "h-24 text-center",
                                        children: "No results."
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 320,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 319,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 287,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 248,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 247,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                lineNumber: 216,
                columnNumber: 7
            }, this),
            showPagination && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardFooter"], {
                className: "py-4",
                children: pagination ? // Server-side pagination
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$data$2d$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DataPagination"], {
                    currentPage: pagination.currentPage,
                    totalPages: pagination.totalPages,
                    onPageChange: pagination.onPageChange,
                    pageSize: pagination.pageSize || 10,
                    onPageSizeChange: pagination.onPageSizeChange,
                    totalItems: pagination.totalItems || 0,
                    isLoading: pagination.isLoading || isLoading,
                    showPageSizeSelector: true,
                    showItemsInfo: true,
                    showFirstLastButtons: true
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 337,
                    columnNumber: 13
                }, this) : // Client-side pagination (fallback)
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between space-x-2 w-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 text-sm text-muted-foreground",
                            children: [
                                "Showing",
                                " ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: [
                                        table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1,
                                        "-",
                                        Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, table.getFilteredRowModel().rows.length)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 354,
                                    columnNumber: 17
                                }, this),
                                " ",
                                "of ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: table.getFilteredRowModel().rows.length
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 365,
                                    columnNumber: 20
                                }, this),
                                " ",
                                "results"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                            lineNumber: 352,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    size: "sm",
                                    onClick: ()=>table.previousPage(),
                                    disabled: !table.getCanPreviousPage(),
                                    children: "Previous"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 369,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    size: "sm",
                                    onClick: ()=>table.nextPage(),
                                    disabled: !table.getCanNextPage(),
                                    children: "Next"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 377,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                    value: table.getState().pagination.pageSize.toString(),
                                    onValueChange: (value)=>{
                                        table.setPageSize(Number(value));
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                            className: "h-8 w-[70px]",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                placeholder: table.getState().pagination.pageSize
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                lineNumber: 392,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 391,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                            side: "top",
                                            children: [
                                                10,
                                                20,
                                                30,
                                                40,
                                                50
                                            ].map((pageSize)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                    value: pageSize.toString(),
                                                    children: pageSize
                                                }, pageSize, false, {
                                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                    lineNumber: 398,
                                                    columnNumber: 23
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 396,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 385,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                            lineNumber: 368,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 351,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                lineNumber: 334,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
        lineNumber: 181,
        columnNumber: 5
    }, this);
}
_s(ReportDataTable, "sjlrSubY4gphho2tCPIgdKOQuxk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useReactTable"]
    ];
});
_c = ReportDataTable;
var _c;
__turbopack_context__.k.register(_c, "ReportDataTable");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/branches/api/branch-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "branchService": (()=>branchService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
const branchService = {
    getBranches: async (params)=>{
        try {
            console.log("Calling API: /branches with params:", params);
            let response;
            try {
                // Try the Next.js API route first
                response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/branches", {
                    params
                });
            } catch (error) {
                console.warn("Error fetching from Next.js API route, trying direct backend:", error);
                // If that fails, try the backend directly
                const API_URL = ("TURBOPACK compile-time value", "http://localhost:3000/api/v1");
                const token = localStorage.getItem("token") || localStorage.getItem("accessToken");
                if (!token) {
                    throw new Error("No authentication token available");
                }
                const backendResponse = await fetch(`${API_URL}/branches${params ? `?${new URLSearchParams(params)}` : ""}`, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        "Content-Type": "application/json"
                    }
                });
                if (!backendResponse.ok) {
                    throw new Error(`Backend API error: ${backendResponse.status}`);
                }
                response = await backendResponse.json();
            }
            console.log("Raw branches API response:", response);
            // Map API response to our Branch type
            const mapApiBranchToBranch = (apiBranch)=>({
                    ...apiBranch
                });
            // If response is an array, convert to paginated format with mapped branches
            if (Array.isArray(response)) {
                console.log("Branches response is an array, converting to paginated format");
                const mappedBranches = response.map(mapApiBranchToBranch);
                return {
                    data: mappedBranches,
                    pagination: {
                        total: mappedBranches.length,
                        page: 1,
                        limit: mappedBranches.length,
                        totalPages: 1
                    }
                };
            }
            // Check if response is already in paginated format
            if (response && response.data && Array.isArray(response.data)) {
                console.log("Branches response is already in paginated format");
                const mappedBranches = response.data.map(mapApiBranchToBranch);
                return {
                    data: mappedBranches,
                    pagination: response.pagination || {
                        total: mappedBranches.length,
                        page: 1,
                        limit: mappedBranches.length,
                        totalPages: 1
                    }
                };
            }
            // If response has data property but it's not an array, wrap it
            if (response && response.data && !Array.isArray(response.data)) {
                console.log("Branches response has data property but it's not an array, wrapping it");
                return {
                    data: [
                        mapApiBranchToBranch(response.data)
                    ],
                    pagination: response.pagination || {
                        total: 1,
                        page: 1,
                        limit: 1,
                        totalPages: 1
                    }
                };
            }
            // If response itself is not an array but contains branches directly
            if (response && !Array.isArray(response) && !response.data) {
                console.log("Response contains branches directly, converting to array");
                // Try to extract branches from the response
                const branches = Object.values(response).filter((item)=>typeof item === "object" && item !== null && "id" in item && "name" in item);
                if (branches.length > 0) {
                    const mappedBranches = branches.map(mapApiBranchToBranch);
                    return {
                        data: mappedBranches,
                        pagination: {
                            total: mappedBranches.length,
                            page: 1,
                            limit: mappedBranches.length,
                            totalPages: 1
                        }
                    };
                }
            }
            // Default fallback
            console.log("Using default fallback for branches response");
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    totalPages: 1
                }
            };
        } catch (error) {
            console.error("Error in getBranches:", error);
            // Return empty data on error
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    totalPages: 1
                }
            };
        }
    },
    getBranchById: async (id)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/branches/${id}`);
    },
    createBranch: async (branch)=>{
        // Send exactly what the API expects according to the guide
        const apiRequest = {
            tenant_id: branch.tenant_id,
            name: branch.name,
            location: branch.location,
            region_id: branch.region_id,
            is_hq: false,
            status: "active"
        };
        // Add optional fields if they exist
        if (branch.phone) {
            apiRequest.phone_number = branch.phone;
        }
        if (branch.email) {
            apiRequest.email = branch.email;
        }
        console.log("Creating branch with data:", apiRequest);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/branches", apiRequest);
    },
    updateBranch: async (id, branch)=>{
        // Ensure we're sending the correct fields to the API
        const apiRequest = {
            name: branch.name,
            location: branch.location,
            region_id: branch.region_id,
            level: branch.level
        };
        console.log("Updating branch with data:", apiRequest);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/branches/${id}`, apiRequest);
    },
    deleteBranch: async (id)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/branches/${id}`);
    },
    updateBranchStatus: async (id, status)=>{
        // Ensure we're sending the required fields to the API
        const apiRequest = {
            status: status.status
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/branches/${id}/status`, apiRequest);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/branches/hooks/use-branches.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useBranch": (()=>useBranch),
    "useBranches": (()=>useBranches),
    "useCreateBranch": (()=>useCreateBranch),
    "useDeleteBranch": (()=>useDeleteBranch),
    "useUpdateBranch": (()=>useUpdateBranch),
    "useUpdateBranchStatus": (()=>useUpdateBranchStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$api$2f$branch$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/branches/api/branch-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-auth-tokens.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
;
;
;
;
function useBranches(params, options) {
    _s();
    const { accessToken, isInitialized } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "branches",
            params
        ],
        queryFn: {
            "useBranches.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$api$2f$branch$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["branchService"].getBranches(params)
        }["useBranches.useQuery"],
        // Only run query when authentication is ready and enabled option is true (if provided)
        enabled: isInitialized && !!accessToken && options?.enabled !== false,
        // In v5, this is the equivalent of keepPreviousData
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false,
        // Provide a default value for data to prevent undefined errors
        select: {
            "useBranches.useQuery": (data)=>{
                if (!data) {
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            totalPages: 1
                        }
                    };
                }
                return data;
            }
        }["useBranches.useQuery"]
    });
}
_s(useBranches, "VavGruHiaFDtqjVllWvyIUUtlyY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useBranch(id) {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "branches",
            id
        ],
        queryFn: {
            "useBranch.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$api$2f$branch$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["branchService"].getBranchById(id)
        }["useBranch.useQuery"],
        enabled: !!id
    });
}
_s1(useBranch, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useCreateBranch() {
    _s2();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateBranch.useMutation": (branch)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$api$2f$branch$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["branchService"].createBranch(branch)
        }["useCreateBranch.useMutation"],
        onSuccess: {
            "useCreateBranch.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "branches"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Branch created", {
                    description: "The branch has been created successfully."
                });
            }
        }["useCreateBranch.useMutation"],
        onError: {
            "useCreateBranch.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error creating branch", {
                    description: error.message || "An error occurred while creating the branch."
                });
            }
        }["useCreateBranch.useMutation"]
    });
}
_s2(useCreateBranch, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useUpdateBranch(id) {
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateBranch.useMutation": (branch)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$api$2f$branch$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["branchService"].updateBranch(id, branch)
        }["useUpdateBranch.useMutation"],
        onSuccess: {
            "useUpdateBranch.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "branches",
                        id
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "branches"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Branch updated", {
                    description: "The branch has been updated successfully."
                });
            }
        }["useUpdateBranch.useMutation"],
        onError: {
            "useUpdateBranch.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error updating branch", {
                    description: error.message || "An error occurred while updating the branch."
                });
            }
        }["useUpdateBranch.useMutation"]
    });
}
_s3(useUpdateBranch, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useDeleteBranch() {
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteBranch.useMutation": (id)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$api$2f$branch$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["branchService"].deleteBranch(id)
        }["useDeleteBranch.useMutation"],
        onSuccess: {
            "useDeleteBranch.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "branches"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Branch deleted", {
                    description: "The branch has been deleted successfully."
                });
            }
        }["useDeleteBranch.useMutation"],
        onError: {
            "useDeleteBranch.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error deleting branch", {
                    description: error.message || "An error occurred while deleting the branch."
                });
            }
        }["useDeleteBranch.useMutation"]
    });
}
_s4(useDeleteBranch, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useUpdateBranchStatus(id) {
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateBranchStatus.useMutation": (status)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$api$2f$branch$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["branchService"].updateBranchStatus(id, status)
        }["useUpdateBranchStatus.useMutation"],
        onSuccess: {
            "useUpdateBranchStatus.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "branches",
                        id
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "branches"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Branch status updated", {
                    description: "The branch status has been updated successfully."
                });
            }
        }["useUpdateBranchStatus.useMutation"],
        onError: {
            "useUpdateBranchStatus.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error updating branch status", {
                    description: error.message || "An error occurred while updating the branch status."
                });
            }
        }["useUpdateBranchStatus.useMutation"]
    });
}
_s5(useUpdateBranchStatus, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/employees/api/employee-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "employeeService": (()=>employeeService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
const employeeService = {
    getEmployees: async (params)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/employees", {
                params
            });
            // Check if the response is an array (as per Swagger)
            if (Array.isArray(response)) {
                // Convert array to paginated response format
                return {
                    data: response,
                    pagination: {
                        total: response.length,
                        page: 1,
                        limit: response.length,
                        totalPages: 1
                    }
                };
            }
            // If it's already in paginated format with data and meta properties
            if (response && response.data && response.meta) {
                return {
                    data: response.data,
                    pagination: {
                        total: response.meta.total,
                        page: response.meta.page,
                        limit: response.meta.limit,
                        totalPages: response.meta.totalPages
                    }
                };
            }
            // Default fallback
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    totalPages: 1
                }
            };
        } catch (error) {
            console.error("Error fetching employees:", error);
            // Return empty data on error
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    totalPages: 1
                }
            };
        }
    },
    getEmployeeById: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/employees/${id}`);
            return response;
        } catch (error) {
            console.error(`Error fetching employee with ID ${id}:`, error);
            throw error;
        }
    },
    createEmployee: async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/employees", data);
            return response;
        } catch (error) {
            console.error("Error creating employee:", error);
            throw error;
        }
    },
    updateEmployee: async (id, data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/employees/${id}`, data);
            return response;
        } catch (error) {
            console.error(`Error updating employee with ID ${id}:`, error);
            throw error;
        }
    },
    deleteEmployee: async (id)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/employees/${id}`);
        } catch (error) {
            console.error(`Error deleting employee with ID ${id}:`, error);
            throw error;
        }
    },
    getEmployeesByBranch: async (branchId)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/employees/branch/${branchId}`);
            return response;
        } catch (error) {
            console.error(`Error fetching employees for branch ${branchId}:`, error);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/employees/hooks/use-employees.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCreateEmployee": (()=>useCreateEmployee),
    "useDeleteEmployee": (()=>useDeleteEmployee),
    "useEmployee": (()=>useEmployee),
    "useEmployees": (()=>useEmployees),
    "useEmployeesByBranch": (()=>useEmployeesByBranch),
    "useUpdateEmployee": (()=>useUpdateEmployee)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$api$2f$employee$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/employees/api/employee-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
;
;
;
// Define query keys for better cache management
const employeeKeys = {
    all: [
        "employees"
    ],
    lists: ()=>[
            ...employeeKeys.all,
            "list"
        ],
    list: (filters)=>[
            ...employeeKeys.lists(),
            filters
        ],
    details: ()=>[
            ...employeeKeys.all,
            "detail"
        ],
    detail: (id)=>[
            ...employeeKeys.details(),
            id
        ],
    branches: ()=>[
            ...employeeKeys.all,
            "branches"
        ],
    branch: (branchId)=>[
            ...employeeKeys.branches(),
            branchId
        ]
};
const useEmployees = (params)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: employeeKeys.list(params || {}),
        queryFn: {
            "useEmployees.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$api$2f$employee$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeService"].getEmployees(params)
        }["useEmployees.useQuery"],
        // In v5, this is the equivalent of keepPreviousData
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false
    });
};
_s(useEmployees, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useEmployee = (id)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: employeeKeys.detail(id),
        queryFn: {
            "useEmployee.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$api$2f$employee$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeService"].getEmployeeById(id)
        }["useEmployee.useQuery"],
        enabled: !!id,
        retry: 1
    });
};
_s1(useEmployee, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useEmployeesByBranch = (branchId)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: employeeKeys.branch(branchId),
        queryFn: {
            "useEmployeesByBranch.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$api$2f$employee$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeService"].getEmployeesByBranch(branchId)
        }["useEmployeesByBranch.useQuery"],
        enabled: !!branchId,
        retry: 1
    });
};
_s2(useEmployeesByBranch, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useCreateEmployee = ()=>{
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateEmployee.useMutation": (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$api$2f$employee$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeService"].createEmployee(data)
        }["useCreateEmployee.useMutation"],
        onSuccess: {
            "useCreateEmployee.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: employeeKeys.lists()
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Employee created successfully");
            }
        }["useCreateEmployee.useMutation"],
        onError: {
            "useCreateEmployee.useMutation": (error)=>{
                const errorMessage = error.response?.data?.message || error.message || "Failed to create employee";
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
            }
        }["useCreateEmployee.useMutation"]
    });
};
_s3(useCreateEmployee, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdateEmployee = (id)=>{
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateEmployee.useMutation": (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$api$2f$employee$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeService"].updateEmployee(id, data)
        }["useUpdateEmployee.useMutation"],
        onSuccess: {
            "useUpdateEmployee.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: employeeKeys.lists()
                });
                queryClient.invalidateQueries({
                    queryKey: employeeKeys.detail(id)
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Employee updated successfully");
            }
        }["useUpdateEmployee.useMutation"],
        onError: {
            "useUpdateEmployee.useMutation": (error)=>{
                const errorMessage = error.response?.data?.message || error.message || "Failed to update employee";
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
            }
        }["useUpdateEmployee.useMutation"]
    });
};
_s4(useUpdateEmployee, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useDeleteEmployee = ()=>{
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteEmployee.useMutation": (id)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$api$2f$employee$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeService"].deleteEmployee(id)
        }["useDeleteEmployee.useMutation"],
        onSuccess: {
            "useDeleteEmployee.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: employeeKeys.lists()
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Employee deleted successfully");
            }
        }["useDeleteEmployee.useMutation"],
        onError: {
            "useDeleteEmployee.useMutation": (error)=>{
                const errorMessage = error.response?.data?.message || error.message || "Failed to delete employee";
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
            }
        }["useDeleteEmployee.useMutation"]
    });
};
_s5(useDeleteEmployee, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/employees/components/employee-selector.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmployeeSelector": (()=>EmployeeSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$hooks$2f$use$2d$employees$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/employees/hooks/use-employees.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/command.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/popover.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevrons$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronsUpDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevrons-up-down.js [app-client] (ecmascript) <export default as ChevronsUpDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
function EmployeeSelector({ value, onValueChange, placeholder = "Select an employee", includeAllOption = false, branchId }) {
    _s();
    const [open, setOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // Fetch employees with optional branch filter
    const params = {
        search: searchTerm,
        status: 'active'
    };
    if (branchId) {
        params.branch_id = branchId;
    }
    const { data: employeesResponse, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$hooks$2f$use$2d$employees$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEmployees"])(params);
    const employees = employeesResponse?.data || [];
    // Find the selected employee
    const selectedEmployee = value ? employees.find((employee)=>employee.id === value) : undefined;
    const handleSelect = (value)=>{
        if (value === 'all') {
            onValueChange(undefined);
        } else {
            onValueChange(parseInt(value, 10));
        }
        setOpen(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Popover"], {
        open: open,
        onOpenChange: setOpen,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverTrigger"], {
                asChild: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    variant: "outline",
                    role: "combobox",
                    "aria-expanded": open,
                    className: "w-full justify-between",
                    children: [
                        value && selectedEmployee ? selectedEmployee.name : placeholder,
                        isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                            className: "ml-2 h-4 w-4 animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                            lineNumber: 81,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevrons$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronsUpDown$3e$__["ChevronsUpDown"], {
                            className: "ml-2 h-4 w-4 shrink-0 opacity-50"
                        }, void 0, false, {
                            fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                            lineNumber: 83,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                    lineNumber: 71,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                lineNumber: 70,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverContent"], {
                className: "w-[300px] p-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandInput"], {
                            placeholder: "Search employees...",
                            value: searchTerm,
                            onValueChange: setSearchTerm
                        }, void 0, false, {
                            fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                            lineNumber: 89,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandList"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandEmpty"], {
                                    children: "No employees found."
                                }, void 0, false, {
                                    fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                                    lineNumber: 95,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandGroup"], {
                                    children: [
                                        includeAllOption && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandItem"], {
                                            value: "all",
                                            onSelect: handleSelect,
                                            className: "cursor-pointer",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mr-2 h-4 w-4", !value ? "opacity-100" : "opacity-0")
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                                                    lineNumber: 103,
                                                    columnNumber: 19
                                                }, this),
                                                "All Employees"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                                            lineNumber: 98,
                                            columnNumber: 17
                                        }, this),
                                        employees.map((employee)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandItem"], {
                                                value: employee.id.toString(),
                                                onSelect: handleSelect,
                                                className: "cursor-pointer",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mr-2 h-4 w-4", value === employee.id ? "opacity-100" : "opacity-0")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                                                        lineNumber: 119,
                                                        columnNumber: 19
                                                    }, this),
                                                    employee.name
                                                ]
                                            }, employee.id, true, {
                                                fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                                                lineNumber: 113,
                                                columnNumber: 17
                                            }, this))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                                    lineNumber: 96,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                            lineNumber: 94,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                    lineNumber: 88,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/employees/components/employee-selector.tsx",
                lineNumber: 87,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/employees/components/employee-selector.tsx",
        lineNumber: 69,
        columnNumber: 5
    }, this);
}
_s(EmployeeSelector, "Qj4bEh8VcZSmR31JQalQxT3ywIc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$hooks$2f$use$2d$employees$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEmployees"]
    ];
});
_c = EmployeeSelector;
var _c;
__turbopack_context__.k.register(_c, "EmployeeSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/payment-methods/api/payment-method-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
/**
 * Payment Method Service
 * Handles API calls for payment methods
 */ const paymentMethodService = {
    /**
   * Get all payment methods with optional filters
   */ getPaymentMethods: async (filters)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/payment-methods", {
                params: filters
            });
            // Handle case where API returns an array directly instead of paginated response
            if (Array.isArray(response)) {
                return {
                    data: response,
                    meta: {
                        total: response.length,
                        page: 1,
                        limit: response.length,
                        offset: 0,
                        pages: 1
                    }
                };
            }
            return response;
        } catch (error) {
            // Silent error handling, will be handled by the component
            throw error;
        }
    },
    /**
   * Get a payment method by ID
   */ getPaymentMethodById: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/payment-methods/${id}`);
        return response;
    },
    /**
   * Create a new payment method
   */ createPaymentMethod: async (paymentMethod)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/payment-methods", paymentMethod);
        return response;
    },
    /**
   * Update a payment method
   */ updatePaymentMethod: async (id, paymentMethod)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/payment-methods/${id}`, paymentMethod);
        return response;
    },
    /**
   * Delete a payment method
   */ deletePaymentMethod: async (id)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/payment-methods/${id}`);
    },
    /**
   * Update payment method status
   */ updatePaymentMethodStatus: async (id, isActive)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/payment-methods/${id}/status`, {
            is_active: isActive
        });
        return response;
    }
};
const __TURBOPACK__default__export__ = paymentMethodService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/payment-methods/hooks/use-payment-methods.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCreatePaymentMethod": (()=>useCreatePaymentMethod),
    "useDeletePaymentMethod": (()=>useDeletePaymentMethod),
    "usePaymentMethod": (()=>usePaymentMethod),
    "usePaymentMethods": (()=>usePaymentMethods),
    "useUpdatePaymentMethod": (()=>useUpdatePaymentMethod),
    "useUpdatePaymentMethodStatus": (()=>useUpdatePaymentMethodStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$api$2f$payment$2d$method$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/payment-methods/api/payment-method-service.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
;
;
;
const usePaymentMethods = (filters)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "payment-methods",
            filters
        ],
        queryFn: {
            "usePaymentMethods.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$api$2f$payment$2d$method$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getPaymentMethods(filters)
        }["usePaymentMethods.useQuery"],
        staleTime: 5 * 60 * 1000,
        retry: 3,
        refetchOnWindowFocus: false
    });
};
_s(usePaymentMethods, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const usePaymentMethod = (id)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "payment-method",
            id
        ],
        queryFn: {
            "usePaymentMethod.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$api$2f$payment$2d$method$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getPaymentMethodById(id)
        }["usePaymentMethod.useQuery"],
        enabled: !!id
    });
};
_s1(usePaymentMethod, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useCreatePaymentMethod = ()=>{
    _s2();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreatePaymentMethod.useMutation": (paymentMethod)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$api$2f$payment$2d$method$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createPaymentMethod(paymentMethod)
        }["useCreatePaymentMethod.useMutation"],
        onSuccess: {
            "useCreatePaymentMethod.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "payment-methods"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Payment method created successfully");
            }
        }["useCreatePaymentMethod.useMutation"],
        onError: {
            "useCreatePaymentMethod.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message || "An error occurred while creating the payment method");
            }
        }["useCreatePaymentMethod.useMutation"]
    });
};
_s2(useCreatePaymentMethod, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdatePaymentMethod = (id)=>{
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdatePaymentMethod.useMutation": (paymentMethod)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$api$2f$payment$2d$method$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].updatePaymentMethod(id, paymentMethod)
        }["useUpdatePaymentMethod.useMutation"],
        onSuccess: {
            "useUpdatePaymentMethod.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "payment-methods"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "payment-method",
                        id
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Payment method updated successfully");
            }
        }["useUpdatePaymentMethod.useMutation"],
        onError: {
            "useUpdatePaymentMethod.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message || "An error occurred while updating the payment method");
            }
        }["useUpdatePaymentMethod.useMutation"]
    });
};
_s3(useUpdatePaymentMethod, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useDeletePaymentMethod = ()=>{
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeletePaymentMethod.useMutation": (id)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$api$2f$payment$2d$method$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].deletePaymentMethod(id)
        }["useDeletePaymentMethod.useMutation"],
        onSuccess: {
            "useDeletePaymentMethod.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "payment-methods"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Payment method deleted successfully");
            }
        }["useDeletePaymentMethod.useMutation"],
        onError: {
            "useDeletePaymentMethod.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message || "An error occurred while deleting the payment method");
            }
        }["useDeletePaymentMethod.useMutation"]
    });
};
_s4(useDeletePaymentMethod, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdatePaymentMethodStatus = (id)=>{
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdatePaymentMethodStatus.useMutation": (isActive)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$api$2f$payment$2d$method$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].updatePaymentMethodStatus(id, isActive)
        }["useUpdatePaymentMethodStatus.useMutation"],
        onSuccess: {
            "useUpdatePaymentMethodStatus.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "payment-methods"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "payment-method",
                        id
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Payment method status updated successfully");
            }
        }["useUpdatePaymentMethodStatus.useMutation"],
        onError: {
            "useUpdatePaymentMethodStatus.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message || "An error occurred while updating the payment method status");
            }
        }["useUpdatePaymentMethodStatus.useMutation"]
    });
};
_s5(useUpdatePaymentMethodStatus, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/payment-methods/components/payment-method-selector.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PaymentMethodSelector": (()=>PaymentMethodSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$hooks$2f$use$2d$payment$2d$methods$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/payment-methods/hooks/use-payment-methods.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/command.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/popover.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevrons$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronsUpDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevrons-up-down.js [app-client] (ecmascript) <export default as ChevronsUpDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
function PaymentMethodSelector({ value, onValueChange, placeholder = "Select payment method...", includeAllOption = true }) {
    _s();
    const [open, setOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // State for fallback payment methods
    const [fallbackMethods, setFallbackMethods] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isFallbackLoading, setIsFallbackLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Fetch payment methods
    const { data: paymentMethodsResponse, isLoading: isApiLoading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$hooks$2f$use$2d$payment$2d$methods$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaymentMethods"])({
        search: searchTerm,
        is_active: true // Only fetch active payment methods
    });
    // No debug logs in production
    // Fallback to direct API call if the hook fails
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PaymentMethodSelector.useEffect": ()=>{
            const fetchFallbackMethods = {
                "PaymentMethodSelector.useEffect.fetchFallbackMethods": async ()=>{
                    if (error && !fallbackMethods.length) {
                        try {
                            setIsFallbackLoading(true);
                            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/payment-methods');
                            if (Array.isArray(response)) {
                                setFallbackMethods(response);
                            } else if (response && response.data) {
                                setFallbackMethods(response.data);
                            }
                        } catch (err) {
                        // Silent error handling, will use hardcoded methods
                        } finally{
                            setIsFallbackLoading(false);
                        }
                    }
                }
            }["PaymentMethodSelector.useEffect.fetchFallbackMethods"];
            fetchFallbackMethods();
        }
    }["PaymentMethodSelector.useEffect"], [
        error
    ]);
    // Hardcoded fallback payment methods as a last resort
    const hardcodedMethods = [
        {
            id: 1,
            name: "Cash",
            code: "CASH",
            description: "Cash payment",
            requires_reference: false,
            is_active: true,
            created_at: "",
            updated_at: "",
            deleted_at: null,
            tenant_id: null,
            Tenant: null
        },
        {
            id: 2,
            name: "M-Pesa",
            code: "MPESA",
            description: "M-Pesa payment",
            requires_reference: true,
            is_active: true,
            created_at: "",
            updated_at: "",
            deleted_at: null,
            tenant_id: null,
            Tenant: null
        },
        {
            id: 3,
            name: "Credit Card",
            code: "CARD",
            description: "Credit card payment",
            requires_reference: true,
            is_active: true,
            created_at: "",
            updated_at: "",
            deleted_at: null,
            tenant_id: null,
            Tenant: null
        },
        {
            id: 4,
            name: "Bank Transfer",
            code: "BANK",
            description: "Bank transfer payment",
            requires_reference: true,
            is_active: true,
            created_at: "",
            updated_at: "",
            deleted_at: null,
            tenant_id: null,
            Tenant: null
        },
        {
            id: 5,
            name: "Credit",
            code: "CREDIT",
            description: "Credit payment",
            requires_reference: true,
            is_active: true,
            created_at: "",
            updated_at: "",
            deleted_at: null,
            tenant_id: null,
            Tenant: null
        }
    ];
    const isLoading = isApiLoading || isFallbackLoading;
    const paymentMethods = paymentMethodsResponse?.data?.length ? paymentMethodsResponse.data : fallbackMethods.length ? fallbackMethods : hardcodedMethods;
    // Find the selected payment method
    const selectedPaymentMethod = value ? paymentMethods.find((method)=>method.id === value) : undefined;
    const handleSelect = (value)=>{
        if (value === 'all') {
            onValueChange(undefined);
        } else {
            onValueChange(parseInt(value, 10));
        }
        setOpen(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Popover"], {
        open: open,
        onOpenChange: setOpen,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverTrigger"], {
                asChild: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    variant: "outline",
                    role: "combobox",
                    "aria-expanded": open,
                    className: "w-full justify-between",
                    children: [
                        selectedPaymentMethod ? selectedPaymentMethod.name : value === undefined && includeAllOption ? "All Payment Methods" : placeholder,
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevrons$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronsUpDown$3e$__["ChevronsUpDown"], {
                            className: "ml-2 h-4 w-4 shrink-0 opacity-50"
                        }, void 0, false, {
                            fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                            lineNumber: 117,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                    lineNumber: 106,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                lineNumber: 105,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverContent"], {
                className: "w-[300px] p-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandInput"], {
                            placeholder: "Search payment methods...",
                            value: searchTerm,
                            onValueChange: setSearchTerm
                        }, void 0, false, {
                            fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                            lineNumber: 122,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandList"], {
                            children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-center p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                        className: "h-4 w-4 animate-spin mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                        lineNumber: 130,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Loading payment methods..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                        lineNumber: 131,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                lineNumber: 129,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandEmpty"], {
                                        children: "No payment methods found."
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                        lineNumber: 135,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandGroup"], {
                                        children: [
                                            includeAllOption && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandItem"], {
                                                value: "all",
                                                onSelect: ()=>handleSelect('all'),
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mr-2 h-4 w-4", value === undefined ? "opacity-100" : "opacity-0")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                                        lineNumber: 143,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: "All Payment Methods"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                                        lineNumber: 149,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, "all", true, {
                                                fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                                lineNumber: 138,
                                                columnNumber: 21
                                            }, this),
                                            paymentMethods.map((method)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandItem"], {
                                                    value: method.id.toString(),
                                                    onSelect: ()=>handleSelect(method.id.toString()),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mr-2 h-4 w-4", value === method.id ? "opacity-100" : "opacity-0")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                                            lineNumber: 158,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: method.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                                            lineNumber: 164,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, method.id, true, {
                                                    fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                                    lineNumber: 153,
                                                    columnNumber: 21
                                                }, this))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                                        lineNumber: 136,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                            lineNumber: 127,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                    lineNumber: 121,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
                lineNumber: 120,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/payment-methods/components/payment-method-selector.tsx",
        lineNumber: 104,
        columnNumber: 5
    }, this);
}
_s(PaymentMethodSelector, "R7oXszGJBOdcJttqYtcDKSkRpgE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$hooks$2f$use$2d$payment$2d$methods$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaymentMethods"]
    ];
});
_c = PaymentMethodSelector;
var _c;
__turbopack_context__.k.register(_c, "PaymentMethodSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/pos/api/pos-session-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
/**
 * Helper function to fetch DSA sales data for a session
 */ async function fetchDsaSalesData(sessionId, branchId) {
    try {
        // Fetch users with DSA role in this branch
        const dsaUsers = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/users", {
            params: {
                branch_id: branchId,
                role_name: "dsa"
            }
        });
        // If no DSA users, return empty data
        if (!Array.isArray(dsaUsers) || dsaUsers.length === 0) {
            return {
                dsaSalesByUser: [],
                totalDsaSales: 0
            };
        }
        // For each DSA user, fetch their sales during this session
        const dsaSalesByUser = await Promise.all(dsaUsers.map(async (user)=>{
            let userSales;
            try {
                // First try with pos_session_id
                userSales = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/sales", {
                    params: {
                        user_id: user.id,
                        pos_session_id: sessionId,
                        branch_id: branchId
                    }
                });
            } catch (error) {
                // If the API doesn't support filtering by pos_session_id, return empty data
                console.warn("Error fetching DSA sales with pos_session_id, returning empty data");
                userSales = [];
            }
            // Calculate total sales for this user
            let total = 0;
            const sales = [];
            if (Array.isArray(userSales)) {
                for (const sale of userSales){
                    const amount = parseFloat(sale.total_amount) || 0;
                    total += amount;
                    sales.push({
                        id: sale.id,
                        amount,
                        date: sale.created_at,
                        paymentMethod: sale.payment_method || "Unknown"
                    });
                }
            }
            return {
                id: user.id,
                name: user.name || `DSA #${user.id}`,
                total,
                sales
            };
        }));
        // Filter out users with no sales
        const dsaUsersWithSales = dsaSalesByUser.filter((user)=>user.total > 0);
        // Calculate total DSA sales
        const totalDsaSales = dsaUsersWithSales.reduce((sum, user)=>sum + user.total, 0);
        return {
            dsaSalesByUser: dsaUsersWithSales,
            totalDsaSales
        };
    } catch (error) {
        console.error("Error fetching DSA sales data:", error);
        return {
            dsaSalesByUser: [],
            totalDsaSales: 0
        };
    }
}
/**
 * POS Session Service
 * Handles API calls for POS sessions
 */ const posSessionService = {
    /**
   * Get shift closing data for a session
   */ getShiftClosingData: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/pos-sessions/${id}/shift-closing-data`);
            console.log("Shift closing data response:", response);
            return response;
        } catch (error) {
            console.error("Error fetching shift closing data:", error);
            throw error;
        }
    },
    /**
   * Get all POS sessions with optional filters
   */ getSessions: async (filters)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/pos-sessions", {
            params: filters
        });
        return response;
    },
    /**
   * Get a POS session by ID
   */ getSessionById: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/pos-sessions/${id}`);
        return response;
    },
    /**
   * Create a new POS session
   */ createSession: async (data)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/pos-sessions", data);
        return response;
    },
    /**
   * Close a POS session
   */ closeSession: async (id, data)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/pos-sessions/${id}/close`, data);
        return response;
    },
    /**
   * Get detailed breakdown data for a session
   */ getSessionBreakdown: async (id)=>{
        try {
            // First get the session details to get the time range
            const session = await posSessionService.getSessionById(id);
            if (!session) {
                throw new Error(`Session with ID ${id} not found`);
            }
            // Prepare date filters for API calls
            const startTime = session.start_time;
            const endTime = session.end_time || new Date().toISOString();
            const branchId = session.branch_id;
            // Fetch sales data for this session
            const salesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/sales", {
                params: {
                    pos_session_id: id,
                    include_items: true
                }
            });
            // Fetch MPESA transactions for this session
            let mpesaData;
            try {
                // First try with pos_session_id
                mpesaData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/mpesa-transactions", {
                    params: {
                        pos_session_id: id,
                        branch_id: branchId,
                        limit: 1000
                    }
                });
            } catch (error) {
                // Fallback to time-based filtering if pos_session_id is not supported
                console.warn("Falling back to time-based filtering for MPESA transactions");
                mpesaData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/mpesa-transactions", {
                    params: {
                        branch_id: branchId,
                        start_date: startTime,
                        end_date: endTime,
                        limit: 1000
                    }
                });
            }
            // Fetch expenses for this session
            let expensesData;
            try {
                // First try with pos_session_id
                expensesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/expenses", {
                    params: {
                        pos_session_id: id,
                        branch_id: branchId,
                        status: "approved",
                        limit: 1000
                    }
                });
            } catch (error) {
                // Fallback to time-based filtering if pos_session_id is not supported
                console.warn("Falling back to time-based filtering for expenses");
                expensesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/expenses", {
                    params: {
                        branch_id: branchId,
                        start_date: startTime,
                        end_date: endTime,
                        status: "approved",
                        limit: 1000
                    }
                });
            }
            // Process sales data to get sales by payment method
            const salesByPaymentMethod = {};
            let totalSales = 0;
            let totalCashSales = 0;
            let totalNonCashSales = 0;
            // Group sales by payment method
            if (Array.isArray(salesData)) {
                salesData.forEach((sale)=>{
                    const paymentMethod = sale.payment_method || "Unknown";
                    const amount = parseFloat(sale.total_amount) || 0;
                    salesByPaymentMethod[paymentMethod] = (salesByPaymentMethod[paymentMethod] || 0) + amount;
                    totalSales += amount;
                    if (paymentMethod.toLowerCase() === "cash") {
                        totalCashSales += amount;
                    } else {
                        totalNonCashSales += amount;
                    }
                });
            }
            // Process MPESA transactions
            const mpesaServices = {
                deposits: 0,
                withdrawals: 0
            };
            const mobileMoneyServices = {
                deposits: 0,
                withdrawals: 0,
                float_in: 0,
                float_out: 0
            };
            if (mpesaData && mpesaData.data && Array.isArray(mpesaData.data)) {
                mpesaData.data.forEach((transaction)=>{
                    const amount = parseFloat(transaction.amount) || 0;
                    if (transaction.transaction_type === "deposit") {
                        mpesaServices.deposits += amount;
                    } else if (transaction.transaction_type === "withdrawal") {
                        mpesaServices.withdrawals += amount;
                    } else if (transaction.transaction_type === "float_in") {
                        mobileMoneyServices.float_in += amount;
                    } else if (transaction.transaction_type === "float_out") {
                        mobileMoneyServices.float_out += amount;
                    }
                    // Mobile money services
                    if (transaction.service_type === "mobile_money") {
                        if (transaction.transaction_type === "deposit") {
                            mobileMoneyServices.deposits += amount;
                        } else if (transaction.transaction_type === "withdrawal") {
                            mobileMoneyServices.withdrawals += amount;
                        }
                    }
                });
            }
            // Process expenses
            let totalExpenses = 0;
            if (expensesData && expensesData.expenses && Array.isArray(expensesData.expenses)) {
                expensesData.expenses.forEach((expense)=>{
                    totalExpenses += parseFloat(expense.approved_amount || expense.amount) || 0;
                });
            }
            // Fetch DSA sales data
            const dsaSalesData = await fetchDsaSalesData(id, branchId);
            // Extract session data
            const sessionData = {
                id: session.id,
                user_id: session.user_id,
                branch_id: session.branch_id,
                start_time: session.start_time,
                end_time: session.end_time,
                opening_cash_balance: session.opening_cash_balance,
                opening_mpesa_balance: session.opening_mpesa_balance || "0",
                opening_mpesa_float: session.opening_mpesa_float || "0",
                cash_paid_in: session.cash_paid_in,
                cash_paid_out: session.cash_paid_out,
                status: session.status,
                user_name: session.User?.name || "Unknown",
                branch_name: session.Branch?.name || "Unknown"
            };
            // Extract reconciliation data if available
            const reconciliationData = session.PosSessionReconciliation ? {
                id: session.PosSessionReconciliation.id,
                closing_cash_balance: session.PosSessionReconciliation.closing_cash_balance,
                closing_mpesa_balance: session.PosSessionReconciliation.closing_mpesa_balance,
                closing_mpesa_float: session.PosSessionReconciliation.closing_mpesa_float || "0",
                cash_payments: session.PosSessionReconciliation.cash_payments,
                mpesa_payments: session.PosSessionReconciliation.mpesa_payments,
                total_sales: session.PosSessionReconciliation.total_sales,
                discrepancies: session.PosSessionReconciliation.discrepancies,
                total_variance: session.PosSessionReconciliation.total_variance,
                notes: session.PosSessionReconciliation.notes,
                created_at: session.PosSessionReconciliation.created_at
            } : undefined;
            return {
                session: sessionData,
                reconciliation: reconciliationData,
                salesByPaymentMethod,
                totalSales,
                totalCashSales,
                totalNonCashSales,
                mpesaServices,
                mobileMoneyServices,
                bankingTransactions: [],
                totalExpenses,
                dsaSales: dsaSalesData
            };
        } catch (error) {
            console.error("Error fetching session breakdown:", error);
            // Return empty data structure instead of mock data
            return {
                session: {
                    id: 0,
                    user_id: 0,
                    branch_id: 0,
                    start_time: "",
                    end_time: null,
                    opening_cash_balance: "0",
                    opening_mpesa_balance: "0",
                    opening_mpesa_float: "0",
                    cash_paid_in: "0",
                    cash_paid_out: "0",
                    status: "open",
                    user_name: "Unknown",
                    branch_name: "Unknown"
                },
                reconciliation: undefined,
                salesByPaymentMethod: {},
                totalSales: 0,
                totalCashSales: 0,
                totalNonCashSales: 0,
                mpesaServices: {
                    deposits: 0,
                    withdrawals: 0
                },
                mobileMoneyServices: {
                    deposits: 0,
                    withdrawals: 0,
                    float_in: 0,
                    float_out: 0
                },
                bankingTransactions: [],
                totalExpenses: 0,
                dsaSales: {
                    dsaSalesByUser: [],
                    totalDsaSales: 0
                }
            };
        }
    }
};
const __TURBOPACK__default__export__ = posSessionService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/pos/hooks/use-pos-sessions.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useClosePosSession": (()=>useClosePosSession),
    "useCreatePosSession": (()=>useCreatePosSession),
    "usePosSession": (()=>usePosSession),
    "usePosSessions": (()=>usePosSessions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$api$2f$pos$2d$session$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/pos/api/pos-session-service.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
;
const usePosSessions = (filters)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "pos-sessions",
            filters
        ],
        queryFn: {
            "usePosSessions.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$api$2f$pos$2d$session$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSessions(filters)
        }["usePosSessions.useQuery"]
    });
};
_s(usePosSessions, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const usePosSession = (id)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "pos-session",
            id
        ],
        queryFn: {
            "usePosSession.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$api$2f$pos$2d$session$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSessionById(id)
        }["usePosSession.useQuery"],
        enabled: !!id
    });
};
_s1(usePosSession, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useCreatePosSession = ()=>{
    _s2();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreatePosSession.useMutation": (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$api$2f$pos$2d$session$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createSession(data)
        }["useCreatePosSession.useMutation"],
        onSuccess: {
            "useCreatePosSession.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "pos-sessions"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("POS session created successfully");
            }
        }["useCreatePosSession.useMutation"],
        onError: {
            "useCreatePosSession.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message || "Failed to create POS session. Please try again.");
            }
        }["useCreatePosSession.useMutation"]
    });
};
_s2(useCreatePosSession, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useClosePosSession = (id)=>{
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useClosePosSession.useMutation": (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$api$2f$pos$2d$session$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].closeSession(id, data)
        }["useClosePosSession.useMutation"],
        onSuccess: {
            "useClosePosSession.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "pos-sessions"
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "pos-session",
                        id
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("POS session closed successfully");
            }
        }["useClosePosSession.useMutation"],
        onError: {
            "useClosePosSession.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message || "Failed to close POS session. Please try again.");
            }
        }["useClosePosSession.useMutation"]
    });
};
_s3(useClosePosSession, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/regions/api/region-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "regionService": (()=>regionService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
const regionService = {
    getRegions: async (params)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/regions", {
                params
            });
            // If response is an array, convert to paginated format
            if (Array.isArray(response)) {
                return {
                    data: response,
                    pagination: {
                        total: response.length,
                        page: 1,
                        limit: response.length,
                        totalPages: 1
                    }
                };
            }
            // Check if response is already in paginated format
            if (response && response.data && Array.isArray(response.data)) {
                return {
                    data: response.data,
                    pagination: response.pagination || {
                        total: response.data.length,
                        page: 1,
                        limit: response.data.length,
                        totalPages: 1
                    }
                };
            }
            // Default fallback
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    totalPages: 1
                }
            };
        } catch (error) {
            console.error("Error in getRegions:", error);
            // Return empty data on error
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    totalPages: 1
                }
            };
        }
    },
    getRegionById: async (id)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/regions/${id}`);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/regions/hooks/use-regions.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useRegion": (()=>useRegion),
    "useRegions": (()=>useRegions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$regions$2f$api$2f$region$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/regions/api/region-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-auth-tokens.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
;
;
;
function useRegions(params) {
    _s();
    const { accessToken, isInitialized } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "regions",
            params
        ],
        queryFn: {
            "useRegions.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$regions$2f$api$2f$region$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["regionService"].getRegions(params)
        }["useRegions.useQuery"],
        // Only run query when authentication is ready
        enabled: isInitialized && !!accessToken,
        // In v5, this is the equivalent of keepPreviousData
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false,
        // Provide a default value for data to prevent undefined errors
        select: {
            "useRegions.useQuery": (data)=>{
                if (!data) {
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            totalPages: 1
                        }
                    };
                }
                // If data exists but data.data is undefined, provide a default
                if (!data.data) {
                    return {
                        ...data,
                        data: [],
                        pagination: data.pagination || {
                            total: 0,
                            page: 1,
                            limit: 0,
                            totalPages: 1
                        }
                    };
                }
                return data;
            }
        }["useRegions.useQuery"]
    });
}
_s(useRegions, "VavGruHiaFDtqjVllWvyIUUtlyY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useRegion(id) {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "regions",
            id
        ],
        queryFn: {
            "useRegion.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$regions$2f$api$2f$region$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["regionService"].getRegionById(id)
        }["useRegion.useQuery"],
        enabled: !!id
    });
}
_s1(useRegion, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/products/api/product-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "productService": (()=>productService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
;
const productService = {
    /**
   * Get products without stock items at headquarters
   * @param params Query parameters
   * @returns Paginated list of products without stock items at headquarters
   */ getProductsWithoutHQStock: async (params)=>{
        try {
            // Use the regular products endpoint with a filter for products without HQ stock
            // This is a workaround until the backend endpoint is fixed
            const allProducts = await productService.getProducts({
                ...params,
                limit: 1000
            });
            // Get all stock items for headquarters
            let stockItems = [];
            try {
                const stockItemsResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/stock-items", {
                    params: {
                        branch_id: 1,
                        limit: 1000
                    }
                });
                if (stockItemsResponse && stockItemsResponse.data) {
                    stockItems = stockItemsResponse.data;
                }
            } catch (stockError) {
                console.error("Error fetching stock items:", stockError);
            // Continue with empty stock items
            }
            // Create a set of product IDs that already have stock items at headquarters
            const productsWithHQStock = new Set(stockItems.map((item)=>item.product_id));
            // Filter out products that already have stock items at headquarters
            const filteredProducts = allProducts.data.filter((product)=>!productsWithHQStock.has(product.id));
            // Return the filtered products with pagination
            return {
                data: filteredProducts,
                pagination: {
                    total: filteredProducts.length,
                    page: parseInt(params?.page || "1", 10),
                    limit: parseInt(params?.limit || "10", 10),
                    totalPages: Math.ceil(filteredProducts.length / parseInt(params?.limit || "10", 10))
                }
            };
        } catch (error) {
            console.error("Error fetching products without HQ stock:", error);
            // Show a toast error message
            try {
                // @ts-ignore - toast is imported elsewhere
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error loading products", {
                    description: "Could not load products without headquarters stock. Please try again later."
                });
            } catch (toastError) {
            // Ignore toast errors
            }
            // Return empty data on error
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    totalPages: 1
                }
            };
        }
    },
    getProducts: async (params)=>{
        try {
            // Extract branch_procurable parameter for client-side filtering
            const branchProcurable = params?.branch_procurable;
            // Create a new params object without branch_procurable
            const apiParams = {
                ...params
            };
            if (apiParams.branch_procurable !== undefined) {
                delete apiParams.branch_procurable;
            }
            console.log("Fetching products with params:", apiParams);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/products", {
                params: apiParams
            });
            console.log("API response for products:", response);
            // Map API response to our Product type
            const mapApiProductToProduct = (apiProduct)=>{
                // Convert string prices to numbers for UI
                const sellingPrice = apiProduct.suggested_selling_price ? parseFloat(apiProduct.suggested_selling_price) : 0;
                // Calculate stock quantity from stock array if available
                const stockQuantity = apiProduct.stock?.reduce((total, item)=>total + item.quantity, 0) || 0;
                // Ensure category hierarchy is properly handled
                let categoryHierarchy = apiProduct.categoryHierarchy || [];
                // If categoryHierarchy is not available but ProductCategory is, create a simple hierarchy
                if ((!categoryHierarchy || categoryHierarchy.length === 0) && apiProduct.ProductCategory) {
                    const category = apiProduct.ProductCategory;
                    // If the category has a parent, include it in the hierarchy
                    if (category.Parent) {
                        categoryHierarchy = [
                            {
                                id: category.Parent.id,
                                name: category.Parent.name,
                                description: category.Parent.description,
                                level: category.Parent.level || 0
                            },
                            {
                                id: category.id,
                                name: category.name,
                                description: category.description,
                                level: category.level || 1
                            }
                        ];
                    } else {
                        // If no parent, just include the category itself
                        categoryHierarchy = [
                            {
                                id: category.id,
                                name: category.name,
                                description: category.description,
                                level: category.level || 0
                            }
                        ];
                    }
                }
                return {
                    ...apiProduct,
                    // Map API fields to UI fields
                    price: sellingPrice,
                    stock_quantity: stockQuantity,
                    status: apiProduct.is_active ? "active" : "inactive",
                    has_variants: apiProduct.has_serial,
                    categoryHierarchy: categoryHierarchy
                };
            };
            let mappedProducts = [];
            // If response is an array, convert to paginated format with mapped products
            if (Array.isArray(response)) {
                mappedProducts = response.map(mapApiProductToProduct);
            } else if (response && response.data && Array.isArray(response.data)) {
                mappedProducts = response.data.map(mapApiProductToProduct);
            }
            // Apply client-side filtering for branch_procurable if needed
            if (branchProcurable === true) {
                console.log("Filtering products for branch_procurable=true");
                mappedProducts = mappedProducts.filter((product)=>product.branch_procurable === true);
            }
            console.log("Filtered products:", mappedProducts);
            // Return the filtered products with pagination
            return {
                data: mappedProducts,
                pagination: response.pagination || {
                    total: mappedProducts.length,
                    page: 1,
                    limit: mappedProducts.length,
                    totalPages: 1
                }
            };
        } catch (error) {
            console.error("Error fetching products:", error);
            // Return empty data on error
            return {
                data: [],
                pagination: {
                    total: 0,
                    page: 1,
                    limit: 0,
                    totalPages: 1
                }
            };
        }
    },
    getProductById: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/products/${id}`);
            return response;
        } catch (error) {
            console.error(`Error fetching product with ID ${id}:`, error);
            // If the error is a 404, return null instead of throwing
            if (error.response && error.response.status === 404) {
                console.warn(`Product with ID ${id} not found`);
                return null;
            }
            // For other errors, rethrow
            throw error;
        }
    },
    createProduct: async (product)=>{
        // Create a properly structured API request with only the required fields
        const apiRequest = {
            // Required fields
            tenant_id: product.tenant_id,
            name: product.name,
            sku: product.sku,
            has_serial: product.has_serial,
            suggested_buying_price: product.suggested_buying_price,
            suggested_selling_price: product.suggested_selling_price
        };
        // Optional fields
        if (product.category_id !== undefined) apiRequest.category_id = product.category_id;
        if (product.brand_id !== undefined) apiRequest.brand_id = product.brand_id;
        if (product.brand_type_id !== undefined) apiRequest.brand_type_id = product.brand_type_id;
        if (product.description !== undefined) apiRequest.description = product.description;
        if (product.barcode !== undefined) apiRequest.barcode = product.barcode;
        if (product.default_wholesale_price !== undefined) apiRequest.default_wholesale_price = product.default_wholesale_price;
        if (product.warranty_period !== undefined) apiRequest.warranty_period = product.warranty_period;
        if (product.can_assign_to_dsa !== undefined) apiRequest.can_assign_to_dsa = product.can_assign_to_dsa;
        console.log("Creating product with API request:", apiRequest);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/products", apiRequest);
    },
    updateProduct: async (id, product)=>{
        // Create a properly structured API request
        const apiRequest = {};
        // Basic information
        if (product.name !== undefined) apiRequest.name = product.name;
        if (product.sku !== undefined) apiRequest.sku = product.sku;
        if (product.description !== undefined) apiRequest.description = product.description;
        if (product.barcode !== undefined) apiRequest.barcode = product.barcode;
        if (product.status !== undefined) apiRequest.is_active = product.status === 'active';
        if (product.is_active !== undefined) apiRequest.is_active = product.is_active;
        // Categorization
        if (product.category_id !== undefined) apiRequest.category_id = product.category_id;
        if (product.brand_id !== undefined) apiRequest.brand_id = product.brand_id;
        if (product.brand_type_id !== undefined) apiRequest.brand_type_id = product.brand_type_id;
        // Inventory tracking
        if (product.has_serial !== undefined) {
            apiRequest.has_serial = product.has_serial;
        } else if (product.has_variants !== undefined) {
            apiRequest.has_serial = product.has_variants;
        }
        if (product.has_variants !== undefined) apiRequest.has_variants = product.has_variants;
        if (product.is_parent !== undefined) apiRequest.is_parent = product.is_parent;
        if (product.parent_id !== undefined) apiRequest.parent_id = product.parent_id;
        // Pricing fields
        if (product.suggested_buying_price !== undefined) {
            apiRequest.suggested_buying_price = product.suggested_buying_price;
        } else if (product.buying_price !== undefined) {
            apiRequest.suggested_buying_price = product.buying_price;
        }
        if (product.suggested_selling_price !== undefined) {
            apiRequest.suggested_selling_price = product.suggested_selling_price;
        } else if (product.selling_price !== undefined) {
            apiRequest.suggested_selling_price = product.selling_price;
        } else if (product.price !== undefined) {
            apiRequest.suggested_selling_price = product.price;
        }
        if (product.default_wholesale_price !== undefined) {
            apiRequest.default_wholesale_price = product.default_wholesale_price;
        }
        // VAT-related fields
        if (product.vat_rate_id !== undefined) apiRequest.vat_rate_id = product.vat_rate_id;
        if (product.is_vat_inclusive !== undefined) apiRequest.is_vat_inclusive = product.is_vat_inclusive;
        if (product.is_vat_exempt !== undefined) apiRequest.is_vat_exempt = product.is_vat_exempt;
        if (product.vat_exemption_reason !== undefined) apiRequest.vat_exemption_reason = product.vat_exemption_reason;
        // Inventory management
        if (product.reorder_level !== undefined) apiRequest.reorder_level = product.reorder_level;
        if (product.reorder_quantity !== undefined) apiRequest.reorder_quantity = product.reorder_quantity;
        // Other fields
        if (product.warranty_period !== undefined) apiRequest.warranty_period = product.warranty_period;
        // DSA-related fields
        if (product.can_assign_to_dsa !== undefined) apiRequest.can_assign_to_dsa = product.can_assign_to_dsa;
        console.log("Updating product with API request:", apiRequest);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/products/${id}`, apiRequest);
    },
    deleteProduct: async (id)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/products/${id}`);
    },
    updateProductStatus: async (id, status)=>{
        // Ensure we're sending the required fields to the API
        const apiRequest = {
            status: status.status
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/products/${id}/status`, apiRequest);
    },
    uploadProductImage: async (id, file)=>{
        const formData = new FormData();
        formData.append("image", file);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/products/${id}/image`, formData, {
            headers: {
                "Content-Type": "multipart/form-data"
            }
        });
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/products/hooks/use-products.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCreateProduct": (()=>useCreateProduct),
    "useDeleteProduct": (()=>useDeleteProduct),
    "useProduct": (()=>useProduct),
    "useProducts": (()=>useProducts),
    "useProductsWithoutHQStock": (()=>useProductsWithoutHQStock),
    "useUpdateProduct": (()=>useUpdateProduct),
    "useUpdateProductStatus": (()=>useUpdateProductStatus),
    "useUploadProductImage": (()=>useUploadProductImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$api$2f$product$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/products/api/product-service.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature();
"use client";
;
;
;
function useProducts(params) {
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "products",
            params
        ],
        queryFn: {
            "useProducts.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$api$2f$product$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productService"].getProducts(params)
        }["useProducts.useQuery"],
        // In v5, this is the equivalent of keepPreviousData
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false,
        // Provide a default value for data to prevent undefined errors
        select: {
            "useProducts.useQuery": (data)=>{
                if (!data) {
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            totalPages: 1
                        }
                    };
                }
                return data;
            }
        }["useProducts.useQuery"]
    });
}
_s(useProducts, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useProductsWithoutHQStock(params) {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "products-without-hq-stock",
            params
        ],
        queryFn: {
            "useProductsWithoutHQStock.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$api$2f$product$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productService"].getProductsWithoutHQStock(params)
        }["useProductsWithoutHQStock.useQuery"],
        // In v5, this is the equivalent of keepPreviousData
        placeholderData: "keep",
        retry: 1,
        refetchOnWindowFocus: false,
        // Provide a default value for data to prevent undefined errors
        select: {
            "useProductsWithoutHQStock.useQuery": (data)=>{
                if (!data) {
                    return {
                        data: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            limit: 0,
                            totalPages: 1
                        }
                    };
                }
                return data;
            }
        }["useProductsWithoutHQStock.useQuery"]
    });
}
_s1(useProductsWithoutHQStock, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useProduct(id, options) {
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "products",
            id
        ],
        queryFn: {
            "useProduct.useQuery": async ()=>{
                try {
                    return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$api$2f$product$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productService"].getProductById(id);
                } catch (error) {
                    console.error(`Error fetching product with ID ${id}:`, error);
                    // Return null instead of throwing an error
                    return null;
                }
            }
        }["useProduct.useQuery"],
        enabled: !!id,
        ...options
    });
}
_s2(useProduct, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useCreateProduct() {
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateProduct.useMutation": (product)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$api$2f$product$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productService"].createProduct(product)
        }["useCreateProduct.useMutation"],
        onSuccess: {
            "useCreateProduct.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "products"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Product created", {
                    description: "The product has been created successfully."
                });
            }
        }["useCreateProduct.useMutation"],
        onError: {
            "useCreateProduct.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error creating product", {
                    description: error.message || "An error occurred while creating the product."
                });
            }
        }["useCreateProduct.useMutation"]
    });
}
_s3(useCreateProduct, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useUpdateProduct(id) {
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateProduct.useMutation": (product)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$api$2f$product$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productService"].updateProduct(id, product)
        }["useUpdateProduct.useMutation"],
        onSuccess: {
            "useUpdateProduct.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "products",
                        id
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "products"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Product updated", {
                    description: "The product has been updated successfully."
                });
            }
        }["useUpdateProduct.useMutation"],
        onError: {
            "useUpdateProduct.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error updating product", {
                    description: error.message || "An error occurred while updating the product."
                });
            }
        }["useUpdateProduct.useMutation"]
    });
}
_s4(useUpdateProduct, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useDeleteProduct() {
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteProduct.useMutation": (id)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$api$2f$product$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productService"].deleteProduct(id)
        }["useDeleteProduct.useMutation"],
        onSuccess: {
            "useDeleteProduct.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "products"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Product deleted", {
                    description: "The product has been deleted successfully."
                });
            }
        }["useDeleteProduct.useMutation"],
        onError: {
            "useDeleteProduct.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error deleting product", {
                    description: error.message || "An error occurred while deleting the product."
                });
            }
        }["useDeleteProduct.useMutation"]
    });
}
_s5(useDeleteProduct, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useUpdateProductStatus(id) {
    _s6();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateProductStatus.useMutation": (status)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$api$2f$product$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productService"].updateProductStatus(id, status)
        }["useUpdateProductStatus.useMutation"],
        onSuccess: {
            "useUpdateProductStatus.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "products",
                        id
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "products"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Product status updated", {
                    description: "The product status has been updated successfully."
                });
            }
        }["useUpdateProductStatus.useMutation"],
        onError: {
            "useUpdateProductStatus.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error updating product status", {
                    description: error.message || "An error occurred while updating the product status."
                });
            }
        }["useUpdateProductStatus.useMutation"]
    });
}
_s6(useUpdateProductStatus, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useUploadProductImage(id) {
    _s7();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUploadProductImage.useMutation": (file)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$api$2f$product$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productService"].uploadProductImage(id, file)
        }["useUploadProductImage.useMutation"],
        onSuccess: {
            "useUploadProductImage.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "products",
                        id
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        "products"
                    ]
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Image uploaded", {
                    description: "The product image has been uploaded successfully."
                });
            }
        }["useUploadProductImage.useMutation"],
        onError: {
            "useUploadProductImage.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error uploading image", {
                    description: error.message || "An error occurred while uploading the image."
                });
            }
        }["useUploadProductImage.useMutation"]
    });
}
_s7(useUploadProductImage, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/components/product-selector.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductSelector": (()=>ProductSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevrons$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronsUpDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevrons-up-down.js [app-client] (ecmascript) <export default as ChevronsUpDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/command.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/popover.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$hooks$2f$use$2d$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/products/hooks/use-products.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
function ProductSelector({ value, onValueChange, placeholder = "Select product...", disabled = false, includeAllOption = true }) {
    _s();
    const [open, setOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const { data: productsResponse, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$hooks$2f$use$2d$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProducts"])({
        search: searchTerm,
        page: 1,
        limit: 50
    });
    const products = productsResponse?.data || [];
    const selectedProduct = products.find((p)=>p.id === value);
    const handleSelect = (productId)=>{
        onValueChange(productId);
        setOpen(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Popover"], {
        open: open,
        onOpenChange: setOpen,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverTrigger"], {
                asChild: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    variant: "outline",
                    role: "combobox",
                    "aria-expanded": open,
                    className: "w-full justify-between",
                    disabled: disabled,
                    children: [
                        selectedProduct ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "truncate",
                            children: selectedProduct.name
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 65,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-muted-foreground",
                            children: placeholder
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 69,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevrons$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronsUpDown$3e$__["ChevronsUpDown"], {
                            className: "ml-2 h-4 w-4 shrink-0 opacity-50"
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 71,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                    lineNumber: 57,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/product-selector.tsx",
                lineNumber: 56,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverContent"], {
                className: "w-[300px] p-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandInput"], {
                            placeholder: "Search products...",
                            value: searchTerm,
                            onValueChange: setSearchTerm
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 76,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandList"], {
                            children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-center p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                        className: "h-4 w-4 animate-spin mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                        lineNumber: 84,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Loading products..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                        lineNumber: 85,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                lineNumber: 83,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandEmpty"], {
                                        children: "No products found."
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                        lineNumber: 89,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandGroup"], {
                                        children: [
                                            includeAllOption && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandItem"], {
                                                value: "all-products",
                                                onSelect: ()=>handleSelect(undefined),
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mr-2 h-4 w-4", !value ? "opacity-100" : "opacity-0")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                        lineNumber: 96,
                                                        columnNumber: 23
                                                    }, this),
                                                    "All Products"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                lineNumber: 92,
                                                columnNumber: 21
                                            }, this),
                                            products.map((product)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandItem"], {
                                                    value: product.id.toString(),
                                                    onSelect: ()=>handleSelect(product.id),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mr-2 h-4 w-4", value === product.id ? "opacity-100" : "opacity-0")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                            lineNumber: 111,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex flex-col",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "truncate",
                                                                    children: product.name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                                    lineNumber: 118,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-xs text-muted-foreground truncate",
                                                                    children: [
                                                                        "SKU: ",
                                                                        product.sku
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                                    lineNumber: 119,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                            lineNumber: 117,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, product.id, true, {
                                                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                    lineNumber: 106,
                                                    columnNumber: 21
                                                }, this))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                        lineNumber: 90,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                    lineNumber: 75,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/product-selector.tsx",
                lineNumber: 74,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/reports/components/product-selector.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
}
_s(ProductSelector, "QgL6wRr2++6hUWqS9ROcU8k0mIs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$hooks$2f$use$2d$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProducts"]
    ];
});
_c = ProductSelector;
var _c;
__turbopack_context__.k.register(_c, "ProductSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/components/report-filters.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReportFilters": (()=>ReportFilters)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$date$2d$range$2d$picker$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/date-range-picker.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/popover.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/select.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$hooks$2f$use$2d$branches$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/branches/hooks/use-branches.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$components$2f$employee$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/employees/components/employee-selector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$components$2f$payment$2d$method$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/payment-methods/components/payment-method-selector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$hooks$2f$use$2d$payment$2d$methods$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/payment-methods/hooks/use-payment-methods.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$hooks$2f$use$2d$pos$2d$sessions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/pos/hooks/use-pos-sessions.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$regions$2f$hooks$2f$use$2d$regions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/regions/hooks/use-regions.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$components$2f$product$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/reports/components/product-selector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/reports.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/endOfDay.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/startOfDay.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/subDays.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FilterIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-client] (ecmascript) <export default as FilterIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function ReportFilters({ filters, onFilterChange, showTimeFilter = true, showSessionFilter = false, showUserFilter = true, showBranchFilter = true, showRegionFilter = false, showPaymentMethodFilter = true, showStatusFilter = false, showDsaFilter = false, showProductFilter = false, showCategoryFilter = false, showLocationFilter = false, showBankingMethodFilter = false, showTransactionTypeFilter = false }) {
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [dateRange, setDateRange] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        from: filters.start_date ? new Date(filters.start_date) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 7),
        to: filters.end_date ? new Date(filters.end_date) : new Date()
    });
    const [selectedDateRange, setSelectedDateRange] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("this_week");
    // Time filter removed
    const { data: branchesResponse } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$hooks$2f$use$2d$branches$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBranches"])();
    const { data: paymentMethodsResponse } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$hooks$2f$use$2d$payment$2d$methods$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaymentMethods"])();
    const { data: posSessionsResponse } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$hooks$2f$use$2d$pos$2d$sessions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePosSessions"])();
    const { data: regionsResponse } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$regions$2f$hooks$2f$use$2d$regions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRegions"])();
    // Filter branches by selected region
    const filteredBranches = branchesResponse?.data?.filter((branch)=>{
        if (!filters.region_id) return true; // Show all branches if no region selected
        return branch.region_id === filters.region_id;
    }) || [];
    const handleDateRangeChange = (range)=>{
        setDateRange(range);
        if (range?.from && range?.to) {
            onFilterChange({
                ...filters,
                start_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(range.from, "yyyy-MM-dd"),
                end_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(range.to, "yyyy-MM-dd")
            });
        }
    };
    const handlePredefinedDateRange = (value)=>{
        setSelectedDateRange(value);
        let from;
        let to;
        const today = new Date();
        switch(value){
            case "today":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(today);
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(today);
                break;
            case "yesterday":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, 1));
                break;
            case "this_week":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, today.getDay()));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(today);
                break;
            case "last_week":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, today.getDay() + 7));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, today.getDay() + 1));
                break;
            case "this_month":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(new Date(today.getFullYear(), today.getMonth(), 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(today);
                break;
            case "last_month":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(new Date(today.getFullYear(), today.getMonth() - 1, 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(new Date(today.getFullYear(), today.getMonth(), 0));
                break;
            case "this_year":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(new Date(today.getFullYear(), 0, 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(today);
                break;
            case "last_year":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(new Date(today.getFullYear() - 1, 0, 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(new Date(today.getFullYear() - 1, 11, 31));
                break;
            case "custom":
                // Keep the current date range
                return;
            default:
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, 7);
                to = today;
        }
        setDateRange({
            from,
            to
        });
        if (from && to) {
            onFilterChange({
                ...filters,
                start_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(from, "yyyy-MM-dd"),
                end_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(to, "yyyy-MM-dd")
            });
        }
    };
    // Time filter removed
    const handleBranchChange = (value)=>{
        onFilterChange({
            ...filters,
            branch_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleRegionChange = (value)=>{
        const regionId = value === "all" ? undefined : parseInt(value, 10);
        onFilterChange({
            ...filters,
            region_id: regionId,
            // Reset branch filter when region changes
            branch_id: undefined
        });
    };
    const handleEmployeeChange = (value)=>{
        onFilterChange({
            ...filters,
            employee_id: value
        });
    };
    const handlePaymentMethodChange = (value)=>{
        onFilterChange({
            ...filters,
            payment_method_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleSessionChange = (value)=>{
        onFilterChange({
            ...filters,
            pos_session_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleStatusChange = (value)=>{
        onFilterChange({
            ...filters,
            status: value === "all" ? undefined : value
        });
    };
    const handleDsaChange = (value)=>{
        onFilterChange({
            ...filters,
            is_dsa: value === "all" ? undefined : value === "true"
        });
    };
    const handleProductChange = (value)=>{
        onFilterChange({
            ...filters,
            product_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleCategoryChange = (value)=>{
        onFilterChange({
            ...filters,
            category_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleLocationChange = (value)=>{
        onFilterChange({
            ...filters,
            location_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleBankingMethodChange = (value)=>{
        onFilterChange({
            ...filters,
            banking_method: value === "all" ? undefined : value
        });
    };
    const handleTransactionTypeChange = (value)=>{
        onFilterChange({
            ...filters,
            transaction_type: value === "all" ? undefined : value
        });
    };
    const handleReset = ()=>{
        setSelectedDateRange("this_week");
        const today = new Date();
        const from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, 7);
        const to = today;
        setDateRange({
            from,
            to
        });
        // Reset all filters to default values
        onFilterChange({
            start_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(from, "yyyy-MM-dd"),
            end_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(to, "yyyy-MM-dd"),
            branch_id: undefined,
            region_id: undefined,
            payment_method_id: undefined,
            employee_id: undefined,
            pos_session_id: undefined,
            status: undefined,
            is_dsa: undefined
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        className: "mb-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
            className: "p-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col space-y-4 md:flex-row md:flex-wrap md:items-end md:gap-4 md:space-y-0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Date Range"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 282,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                        value: selectedDateRange,
                                        onValueChange: handlePredefinedDateRange,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                className: "w-[180px]",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                    placeholder: "Select date range"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 289,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                lineNumber: 288,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DATE_RANGE_OPTIONS"].map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                        value: option.value,
                                                        children: option.label
                                                    }, option.value, false, {
                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                        lineNumber: 293,
                                                        columnNumber: 21
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                lineNumber: 291,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 284,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$date$2d$range$2d$picker$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DateRangePicker"], {
                                        value: dateRange,
                                        onChange: handleDateRangeChange
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 300,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 283,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 281,
                        columnNumber: 11
                    }, this),
                    showBranchFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Branch"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 310,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                value: filters.branch_id ? filters.branch_id.toString() : "all",
                                onValueChange: handleBranchChange,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                        className: "w-[180px]",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                            placeholder: "All Branches"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                            lineNumber: 316,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 315,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                value: "all",
                                                children: "All Branches"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                lineNumber: 319,
                                                columnNumber: 19
                                            }, this),
                                            filteredBranches.map((branch)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                    value: branch.id.toString(),
                                                    children: branch.name
                                                }, branch.id, false, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 321,
                                                    columnNumber: 21
                                                }, this))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 318,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 311,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 309,
                        columnNumber: 13
                    }, this),
                    showPaymentMethodFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Payment Method"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 333,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-[220px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$components$2f$payment$2d$method$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaymentMethodSelector"], {
                                    value: filters.payment_method_id,
                                    onValueChange: (value)=>handlePaymentMethodChange(value !== undefined ? value.toString() : "all"),
                                    placeholder: "All Payment Methods",
                                    includeAllOption: true
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                    lineNumber: 335,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 334,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 332,
                        columnNumber: 13
                    }, this),
                    showProductFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Product"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 352,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-[220px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$components$2f$product$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProductSelector"], {
                                    value: filters.product_id,
                                    onValueChange: (value)=>handleProductChange(value?.toString() || "all"),
                                    placeholder: "All Products",
                                    includeAllOption: true
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                    lineNumber: 354,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 353,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 351,
                        columnNumber: 13
                    }, this),
                    showRegionFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Region"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 369,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                value: filters.region_id ? filters.region_id.toString() : "all",
                                onValueChange: handleRegionChange,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                        className: "w-[180px]",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                            placeholder: "All Regions"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                            lineNumber: 375,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 374,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                value: "all",
                                                children: "All Regions"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                lineNumber: 378,
                                                columnNumber: 19
                                            }, this),
                                            regionsResponse?.data?.map((region)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                    value: region.id.toString(),
                                                    children: region.name
                                                }, region.id, false, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 380,
                                                    columnNumber: 21
                                                }, this))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 377,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 370,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 368,
                        columnNumber: 13
                    }, this),
                    showUserFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Employee"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 392,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-[220px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$components$2f$employee$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EmployeeSelector"], {
                                    value: filters.employee_id,
                                    onValueChange: handleEmployeeChange,
                                    placeholder: "All Employees",
                                    includeAllOption: true,
                                    branchId: filters.branch_id
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                    lineNumber: 394,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 393,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 391,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Popover"], {
                                open: isOpen,
                                onOpenChange: setIsOpen,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverTrigger"], {
                                        asChild: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "outline",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FilterIcon$3e$__["FilterIcon"], {
                                                    className: "mr-2 h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 412,
                                                    columnNumber: 19
                                                }, this),
                                                "More Filters"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                            lineNumber: 411,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 410,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverContent"], {
                                        className: "w-80 p-4",
                                        align: "end",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid gap-4",
                                            children: [
                                                showSessionFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "POS Session"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 424,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.pos_session_id ? filters.pos_session_id.toString() : "all",
                                                            onValueChange: handleSessionChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Sessions"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 434,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 433,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Sessions"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 437,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        posSessionsResponse?.map((session)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                                value: session.id.toString(),
                                                                                children: [
                                                                                    "Session #",
                                                                                    session.id
                                                                                ]
                                                                            }, session.id, true, {
                                                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                                lineNumber: 439,
                                                                                columnNumber: 29
                                                                            }, this))
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 436,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 425,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 423,
                                                    columnNumber: 21
                                                }, this),
                                                showStatusFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "Status"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 454,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.status || "all",
                                                            onValueChange: handleStatusChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Statuses"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 460,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 459,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Statuses"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 463,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "completed",
                                                                            children: "Completed"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 464,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "pending",
                                                                            children: "Pending"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 465,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "cancelled",
                                                                            children: "Cancelled"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 466,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 462,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 455,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 453,
                                                    columnNumber: 21
                                                }, this),
                                                showDsaFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "Sale Type"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 475,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.is_dsa !== undefined ? filters.is_dsa.toString() : "all",
                                                            onValueChange: handleDsaChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Sales"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 485,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 484,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Sales"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 488,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "true",
                                                                            children: "DSA Sales"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 489,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "false",
                                                                            children: "Regular Sales"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 490,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 487,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 476,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 474,
                                                    columnNumber: 21
                                                }, this),
                                                showBankingMethodFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "Banking Method"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 499,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.banking_method || "all",
                                                            onValueChange: handleBankingMethodChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Methods"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 505,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 504,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Methods"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 508,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "bank",
                                                                            children: "Bank"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 509,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "agent",
                                                                            children: "Agent"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 510,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "mpesa",
                                                                            children: "M-Pesa"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 511,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 507,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 500,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 498,
                                                    columnNumber: 21
                                                }, this),
                                                showTransactionTypeFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "Transaction Type"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 520,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.transaction_type || "all",
                                                            onValueChange: handleTransactionTypeChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Types"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 526,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 525,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Types"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 529,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "deposit",
                                                                            children: "Deposit"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 530,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "withdrawal",
                                                                            children: "Withdrawal"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 531,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "transfer",
                                                                            children: "Transfer"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 532,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 528,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 521,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 519,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                            lineNumber: 417,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 416,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 409,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: handleReset,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 542,
                                        columnNumber: 15
                                    }, this),
                                    "Reset"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 541,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 408,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                lineNumber: 279,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/reports/components/report-filters.tsx",
            lineNumber: 278,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/features/reports/components/report-filters.tsx",
        lineNumber: 277,
        columnNumber: 5
    }, this);
}
_s(ReportFilters, "9ust2ovXvqUSRp13iYKDnYCgWGA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$hooks$2f$use$2d$branches$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBranches"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$hooks$2f$use$2d$payment$2d$methods$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaymentMethods"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$hooks$2f$use$2d$pos$2d$sessions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePosSessions"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$regions$2f$hooks$2f$use$2d$regions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRegions"]
    ];
});
_c = ReportFilters;
var _c;
__turbopack_context__.k.register(_c, "ReportFilters");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/api/stock-levels-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Stock Levels API Service
 * Handles API calls for the comprehensive stock levels endpoint
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
/**
 * Stock Levels API Service
 * Provides methods to interact with the /api/v1/reports/stock-levels endpoint
 */ const stockLevelsService = {
    /**
   * Get comprehensive stock levels data
   * Endpoint: GET /api/v1/reports/stock-levels
   * Permission: stock_reports:read
   */ getStockLevels: async (params)=>{
        try {
            const config = {
                params
            };
            // If format is excel, expect blob response
            if (params?.format === 'excel') {
                config.responseType = 'blob';
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/stock-levels", config);
                return response;
            }
            // For JSON response, validate and return typed data
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/stock-levels", config);
            // Validate response structure
            if (!response || typeof response !== 'object') {
                throw new Error('Invalid response format from stock levels endpoint');
            }
            if (response.status !== 'success') {
                throw new Error(response.message || 'Stock levels request failed');
            }
            return response;
        } catch (error) {
            console.error('Error fetching stock levels:', error);
            // Enhanced error handling with specific error types
            if (error.response?.status === 401) {
                throw new Error('Authentication required. Please log in again.');
            }
            if (error.response?.status === 403) {
                throw new Error('You do not have permission to view stock reports.');
            }
            if (error.response?.status === 404) {
                throw new Error('Stock levels endpoint not found. Please contact support.');
            }
            if (error.response?.status >= 500) {
                throw new Error('Server error. Please try again later.');
            }
            // Network or other errors
            if (!error.response) {
                throw new Error('Network error. Please check your connection.');
            }
            throw error;
        }
    },
    /**
   * Export stock levels to Excel
   * Convenience method for Excel export with proper filename generation
   */ exportStockLevels: async (params)=>{
        try {
            const exportParams = {
                ...params,
                format: 'excel'
            };
            const blob = await stockLevelsService.getStockLevels(exportParams);
            if (!(blob instanceof Blob)) {
                throw new Error('Invalid response format for Excel export');
            }
            return blob;
        } catch (error) {
            console.error('Error exporting stock levels to Excel:', error);
            throw error;
        }
    },
    /**
   * Get stock levels with default parameters for dashboard
   * Optimized for dashboard widgets with sensible defaults
   */ getStockLevelsSummary: async (params)=>{
        try {
            const defaultParams = {
                limit: 50,
                include_zero_stock: params?.include_zero_stock ?? false,
                include_inactive: false,
                sort_by: 'quantity',
                sort_direction: 'asc',
                ...params
            };
            const response = await stockLevelsService.getStockLevels(defaultParams);
            return response;
        } catch (error) {
            console.error('Error fetching stock levels summary:', error);
            throw error;
        }
    },
    /**
   * Get low stock alerts only
   * Filtered view for urgent stock management
   */ getLowStockAlerts: async (params)=>{
        try {
            const alertParams = {
                include_zero_stock: true,
                include_inactive: false,
                sort_by: 'quantity',
                sort_direction: 'asc',
                limit: 100,
                ...params
            };
            const response = await stockLevelsService.getStockLevels(alertParams);
            return response;
        } catch (error) {
            console.error('Error fetching low stock alerts:', error);
            throw error;
        }
    },
    /**
   * Search products by name or SKU
   * Optimized for product search functionality
   */ searchProducts: async (searchQuery, params)=>{
        try {
            if (!searchQuery || searchQuery.trim().length === 0) {
                throw new Error('Search query is required');
            }
            const searchParams = {
                search: searchQuery.trim(),
                include_zero_stock: true,
                include_inactive: false,
                sort_by: 'name',
                sort_direction: 'asc',
                limit: params?.limit || 50,
                ...params
            };
            const response = await stockLevelsService.getStockLevels(searchParams);
            return response;
        } catch (error) {
            console.error('Error searching products:', error);
            throw error;
        }
    },
    /**
   * Get stock levels by category
   * Filtered view for category analysis
   */ getStockLevelsByCategory: async (categoryId, params)=>{
        try {
            const categoryParams = {
                category_id: categoryId,
                include_zero_stock: params?.include_zero_stock ?? true,
                include_inactive: false,
                sort_by: 'name',
                sort_direction: 'asc',
                limit: params?.limit || 100,
                ...params
            };
            const response = await stockLevelsService.getStockLevels(categoryParams);
            return response;
        } catch (error) {
            console.error('Error fetching stock levels by category:', error);
            throw error;
        }
    },
    /**
   * Get stock levels by branch
   * Filtered view for branch analysis
   */ getStockLevelsByBranch: async (branchId, params)=>{
        try {
            const branchParams = {
                branch_id: branchId,
                include_zero_stock: params?.include_zero_stock ?? true,
                include_inactive: false,
                sort_by: 'name',
                sort_direction: 'asc',
                limit: params?.limit || 100,
                ...params
            };
            const response = await stockLevelsService.getStockLevels(branchParams);
            return response;
        } catch (error) {
            console.error('Error fetching stock levels by branch:', error);
            throw error;
        }
    },
    /**
   * Validate stock levels parameters
   * Helper method to validate parameters before API call
   */ validateParams: (params)=>{
        const errors = [];
        if (params.page && (params.page < 1 || !Number.isInteger(params.page))) {
            errors.push('Page must be a positive integer');
        }
        if (params.limit && (params.limit < 1 || params.limit > 500 || !Number.isInteger(params.limit))) {
            errors.push('Limit must be between 1 and 500');
        }
        if (params.sort_by && ![
            'name',
            'quantity',
            'value',
            'category'
        ].includes(params.sort_by)) {
            errors.push('Sort field must be one of: name, quantity, value, category');
        }
        if (params.sort_direction && ![
            'asc',
            'desc'
        ].includes(params.sort_direction)) {
            errors.push('Sort direction must be asc or desc');
        }
        if (params.format && ![
            'json',
            'excel'
        ].includes(params.format)) {
            errors.push('Format must be json or excel');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    },
    /**
   * Build query parameters string
   * Helper method for URL construction
   */ buildQueryString: (params)=>{
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value])=>{
            if (value !== undefined && value !== null && value !== '') {
                searchParams.append(key, String(value));
            }
        });
        return searchParams.toString();
    }
};
const __TURBOPACK__default__export__ = stockLevelsService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/hooks/use-stock-levels.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * React Query hooks for Stock Levels endpoint
 * Provides optimized data fetching with caching and error handling
 */ __turbopack_context__.s({
    "useLowStockAlerts": (()=>useLowStockAlerts),
    "usePrefetchStockLevels": (()=>usePrefetchStockLevels),
    "useProductSearch": (()=>useProductSearch),
    "useRefreshStockLevels": (()=>useRefreshStockLevels),
    "useStockLevelsByBranch": (()=>useStockLevelsByBranch),
    "useStockLevelsByCategory": (()=>useStockLevelsByCategory),
    "useStockLevelsExport": (()=>useStockLevelsExport),
    "useStockLevelsReport": (()=>useStockLevelsReport),
    "useStockLevelsSummary": (()=>useStockLevelsSummary)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/reports/api/stock-levels-service.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature();
;
;
;
const useStockLevelsReport = (params, options)=>{
    _s();
    const { enabled = true, staleTime = 2 * 60 * 1000, gcTime = 5 * 60 * 1000, refetchOnWindowFocus = false } = options || {};
    const query = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "stock-levels-report",
            params
        ],
        queryFn: {
            "useStockLevelsReport.useQuery[query]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getStockLevels(params)
        }["useStockLevelsReport.useQuery[query]"],
        enabled,
        staleTime,
        gcTime,
        refetchOnWindowFocus,
        retry: {
            "useStockLevelsReport.useQuery[query]": (failureCount, error)=>{
                // Don't retry on authentication or permission errors
                if (error?.response?.status === 401 || error?.response?.status === 403) {
                    return false;
                }
                // Retry up to 2 times for other errors
                return failureCount < 2;
            }
        }["useStockLevelsReport.useQuery[query]"],
        retryDelay: {
            "useStockLevelsReport.useQuery[query]": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useStockLevelsReport.useQuery[query]"]
    });
    return {
        data: query.data,
        isLoading: query.isLoading,
        error: query.error,
        refetch: query.refetch
    };
};
_s(useStockLevelsReport, "c7fxJWDO4uMGjIdKMJSj1aiS9wg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useStockLevelsSummary = (params, enabled = true)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "stock-levels-summary",
            params
        ],
        queryFn: {
            "useStockLevelsSummary.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getStockLevelsSummary(params)
        }["useStockLevelsSummary.useQuery"],
        enabled,
        staleTime: 1 * 60 * 1000,
        gcTime: 3 * 60 * 1000,
        refetchOnWindowFocus: false,
        retry: 2
    });
};
_s1(useStockLevelsSummary, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useLowStockAlerts = (params, enabled = true)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "low-stock-alerts",
            params
        ],
        queryFn: {
            "useLowStockAlerts.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getLowStockAlerts(params)
        }["useLowStockAlerts.useQuery"],
        enabled,
        staleTime: 30 * 1000,
        gcTime: 2 * 60 * 1000,
        refetchOnWindowFocus: true,
        refetchInterval: 5 * 60 * 1000,
        retry: 1
    });
};
_s2(useLowStockAlerts, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useProductSearch = (searchQuery, params, options)=>{
    _s3();
    const { enabled = true, debounceMs = 300 } = options || {};
    // Only enable query if search query has minimum length
    const shouldSearch = enabled && searchQuery && searchQuery.trim().length >= 2;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "product-search",
            searchQuery,
            params
        ],
        queryFn: {
            "useProductSearch.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].searchProducts(searchQuery, params)
        }["useProductSearch.useQuery"],
        enabled: shouldSearch,
        staleTime: 30 * 1000,
        gcTime: 2 * 60 * 1000,
        refetchOnWindowFocus: false,
        retry: 1
    });
};
_s3(useProductSearch, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useStockLevelsByCategory = (categoryId, params, enabled = true)=>{
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "stock-levels-by-category",
            categoryId,
            params
        ],
        queryFn: {
            "useStockLevelsByCategory.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getStockLevelsByCategory(categoryId, params)
        }["useStockLevelsByCategory.useQuery"],
        enabled: enabled && categoryId > 0,
        staleTime: 2 * 60 * 1000,
        gcTime: 5 * 60 * 1000,
        refetchOnWindowFocus: false,
        retry: 2
    });
};
_s4(useStockLevelsByCategory, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useStockLevelsByBranch = (branchId, params, enabled = true)=>{
    _s5();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "stock-levels-by-branch",
            branchId,
            params
        ],
        queryFn: {
            "useStockLevelsByBranch.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getStockLevelsByBranch(branchId, params)
        }["useStockLevelsByBranch.useQuery"],
        enabled: enabled && branchId > 0,
        staleTime: 2 * 60 * 1000,
        gcTime: 5 * 60 * 1000,
        refetchOnWindowFocus: false,
        retry: 2
    });
};
_s5(useStockLevelsByBranch, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useStockLevelsExport = ()=>{
    _s6();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useStockLevelsExport.useMutation": async (params)=>{
                const blob = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].exportStockLevels(params);
                return blob;
            }
        }["useStockLevelsExport.useMutation"],
        onSuccess: {
            "useStockLevelsExport.useMutation": (blob, variables)=>{
                try {
                    // Create download link
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    // Generate filename with timestamp
                    const timestamp = new Date().toISOString().split('T')[0];
                    const branchFilter = variables.branch_id ? `-branch-${variables.branch_id}` : '';
                    const categoryFilter = variables.category_id ? `-category-${variables.category_id}` : '';
                    link.download = `stock-levels-${timestamp}${branchFilter}${categoryFilter}.xlsx`;
                    // Trigger download
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    // Clean up
                    window.URL.revokeObjectURL(url);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Stock levels report exported successfully!");
                } catch (error) {
                    console.error('Error downloading file:', error);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to download the export file");
                }
            }
        }["useStockLevelsExport.useMutation"],
        onError: {
            "useStockLevelsExport.useMutation": (error)=>{
                console.error('Export error:', error);
                if (error?.response?.status === 403) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("You don't have permission to export stock reports");
                } else if (error?.response?.status === 500) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Server error during export. Please try again later.");
                } else {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to export stock levels report");
                }
            }
        }["useStockLevelsExport.useMutation"]
    });
};
_s6(useStockLevelsExport, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useRefreshStockLevels = ()=>{
    _s7();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const refreshAll = async ()=>{
        await queryClient.invalidateQueries({
            queryKey: [
                "stock-levels-report"
            ]
        });
        await queryClient.invalidateQueries({
            queryKey: [
                "stock-levels-summary"
            ]
        });
        await queryClient.invalidateQueries({
            queryKey: [
                "low-stock-alerts"
            ]
        });
    };
    const refreshByCategory = async (categoryId)=>{
        await queryClient.invalidateQueries({
            queryKey: [
                "stock-levels-by-category",
                categoryId
            ]
        });
    };
    const refreshByBranch = async (branchId)=>{
        await queryClient.invalidateQueries({
            queryKey: [
                "stock-levels-by-branch",
                branchId
            ]
        });
    };
    const refreshSearch = async ()=>{
        await queryClient.invalidateQueries({
            queryKey: [
                "product-search"
            ]
        });
    };
    return {
        refreshAll,
        refreshByCategory,
        refreshByBranch,
        refreshSearch
    };
};
_s7(useRefreshStockLevels, "4R+oYVB2Uc11P7bp1KcuhpkfaTw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
const usePrefetchStockLevels = ()=>{
    _s8();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const prefetchSummary = (params)=>{
        queryClient.prefetchQuery({
            queryKey: [
                "stock-levels-summary",
                params
            ],
            queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getStockLevelsSummary(params),
            staleTime: 1 * 60 * 1000
        });
    };
    const prefetchAlerts = (params)=>{
        queryClient.prefetchQuery({
            queryKey: [
                "low-stock-alerts",
                params
            ],
            queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$stock$2d$levels$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getLowStockAlerts(params),
            staleTime: 30 * 1000
        });
    };
    return {
        prefetchSummary,
        prefetchAlerts
    };
};
_s8(usePrefetchStockLevels, "4R+oYVB2Uc11P7bp1KcuhpkfaTw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_features_f7364f9d._.js.map