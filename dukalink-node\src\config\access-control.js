/**
 * Role-Based Access Control (RBAC) configuration using AccessControl
 *
 * This file defines all roles and their permissions in the system.
 * It uses the AccessControl library to manage role-based permissions.
 */

const { AccessControl } = require("accesscontrol");
const ac = new AccessControl();

// Define roles and permissions
// ===========================

// POS Operator - Basic user with minimal permissions
ac.grant("pos_operator")
  .readOwn("profile")
  .readOwn("sales")
  .createOwn("sales")
  .readOwn("pos_sessions")
  .createOwn("pos_sessions")
  .updateOwn("pos_sessions")
  .readAny("products")
  .readAny("customers")
  .createAny("customers")
  .updateOwn("customers")
  .readAny("users")
  .readAny("credit_partners") // Add credit partners read permission
  // Banking permissions
  .readAny("banking")
  .createAny("banking")
  // MPESA Float permissions
  .readAny("mpesa_float")
  .readAny("mpesa-float")
  .createAny("mpesa_float_assignment")
  .updateAny("mpesa_float_assignment")
  .deleteAny("mpesa_float_assignment")
  .readAny("mpesa-float-balances")
  .readAny("mpesa-float-reconciliations")
  .readAny("mpesa-float-movements")
  .readAny("mpesa_float_movements");

// Shop Attendant - Similar to POS Operator with some additional permissions
ac.grant("shop_attendant")
  .extend("pos_operator")
  .readAny("inventory")
  .readAny("stock_items")
  .createOwn("expenses")
  .readOwn("expenses");

// DSA Agent - Direct Sales Agent
ac.grant("dsa_agent")
  .readOwn("profile")
  .readOwn("sales")
  .createOwn("sales")
  .readOwn("dsa_stock_assignments")
  .readOwn("dsa_stock_reconciliations")
  .createOwn("dsa_stock_reconciliations")
  .readAny("users")
  .readAny("credit_partners"); // Add credit partners read permission

// Branch Manager - Manages a branch
ac.grant("branch_manager")
  .extend("shop_attendant")
  .readAny("profile")
  .readAny("products")
  .readAny("sales")
  .createAny("sales") // Add createAny permission for sales
  .updateAny("sales") // Add updateAny permission for sales
  .readAny("pos_sessions")
  .readAny("expenses")
  .updateAny("expenses")
  .readAny("stock_movement")
  .createAny("stock_movement")
  .updateAny("stock_movement") // Add updateAny permission for stock_movement
  .readAny("stock_request")
  .createAny("stock_request")
  .updateAny("stock_request")
  .readAny("stock_requests")
  .createAny("stock_requests")
  .updateAny("stock_requests")
  .readAny("stock-requests")
  .createAny("stock-requests")
  .updateAny("stock-requests")
  .readAny("stock_reports")
  .readAny("dsa_stock")
  .readAny("dsa_assignments")
  .createAny("dsa_assignments")
  .readAny("dsa_reconciliations")
  .updateAny("dsa_reconciliations")
  .readAny("employees")
  .createAny("employees")
  .updateAny("employees")
  .readAny("inventory")
  .readAny("credit_partners") // Add credit partners read permission
  .readAny("users")
  // Banking permissions
  .readAny("banking")
  .createAny("banking")
  // MPESA Float permissions
  .readAny("mpesa_float")
  .readAny("mpesa-float")
  .createAny("mpesa_float_assignment")
  .updateAny("mpesa_float_assignment")
  .deleteAny("mpesa_float_assignment")
  .readAny("mpesa-float-balances")
  .readAny("mpesa-float-reconciliations")
  .readAny("mpesa-float-movements")
  .readAny("mpesa_float_movements");

// Operations Manager - First level expense approval
ac.grant("operations_manager")
  .extend("branch_manager")
  .updateAny("expenses")
  .createAny("expense_first_approval")
  .readAny("expense_reports")
  .readAny("financial_reports");

// Assistant Operations Manager - Same as Operations Manager
ac.grant("assistant_operations_manager")
  .extend("branch_manager")
  .updateAny("expenses")
  .createAny("expense_first_approval")
  .readAny("expense_reports")
  .readAny("financial_reports");

// Accountant - First level expense approval and financial access
ac.grant("accountant")
  .readAny("profile")
  .readAny("sales")
  .readAny("pos_sessions")
  .readAny("expenses")
  .updateAny("expenses")
  .createAny("expense_first_approval")
  .readAny("stock_movement")
  .readAny("stock_request")
  .readAny("stock_requests")
  .readAny("stock-requests")
  .readAny("stock_reports")
  .readAny("dsa_stock")
  .readAny("dsa_assignments")
  .readAny("dsa_reconciliations")
  .readAny("employees")
  .readAny("inventory")
  .readAny("roles")
  .readAny("permissions")
  .readAny("banking")
  .readAny("financial_reports")
  .readAny("expense_reports")
  .readAny("credit_partners") // Add credit partners read permission
  .readAny("users");

// Company Admin - Full access to company resources
ac.grant("company_admin")
  .extend("accountant")
  // Add explicit permissions for expense approvals
  .createAny("expense_first_approval")
  .createAny("expense_final_approval")
  // Other permissions
  .createAny("roles")
  .updateAny("roles")
  .deleteAny("roles")
  .createAny("permissions")
  .updateAny("permissions")
  .deleteAny("permissions")
  .createAny("employees")
  .updateAny("employees")
  .deleteAny("employees")
  .createAny("branches")
  .updateAny("branches")
  .deleteAny("branches")
  .createAny("products")
  .updateAny("products")
  .deleteAny("products")
  .createAny("categories")
  .updateAny("categories")
  .deleteAny("categories")
  .createAny("brands")
  .updateAny("brands")
  .deleteAny("brands")
  // Credit partners permissions
  .readAny("credit_partners")
  .createAny("credit_partners")
  .updateAny("credit_partners")
  .deleteAny("credit_partners")
  .createAny("inventory")
  .updateAny("inventory")
  .deleteAny("inventory")
  .createAny("stock_items")
  .updateAny("stock_items")
  .deleteAny("stock_items")
  .createAny("banking")
  .updateAny("banking")
  .deleteAny("banking")
  // Banking and float approval permissions
  .createAny("banking_approval")
  .createAny("banking_rejection")
  .createAny("mpesa_float_approval")
  .createAny("mpesa_float_rejection")
  .createAny("cash_float_approval")
  .createAny("cash_float_rejection")
  .createAny("float_reconciliation_approval")
  .createAny("float_reconciliation_rejection")
  .readAny("users")
  .createAny("users")
  .updateAny("users")
  .deleteAny("users");

// HQ Admin - Alias for Company Admin (for backward compatibility)
ac.grant("hq_admin")
  .extend("accountant")
  // Add explicit permissions for expense approvals
  .createAny("expense_first_approval")
  .createAny("expense_final_approval")
  // Other permissions
  .createAny("roles")
  .updateAny("roles")
  .deleteAny("roles")
  .createAny("permissions")
  .updateAny("permissions")
  .deleteAny("permissions")
  .createAny("employees")
  .updateAny("employees")
  .deleteAny("employees")
  .createAny("branches")
  .updateAny("branches")
  .deleteAny("branches")
  .createAny("products")
  .updateAny("products")
  .deleteAny("products")
  .createAny("categories")
  .updateAny("categories")
  .deleteAny("categories")
  .createAny("brands")
  .updateAny("brands")
  .deleteAny("brands")
  // Credit partners permissions
  .readAny("credit_partners")
  .createAny("credit_partners")
  .updateAny("credit_partners")
  .deleteAny("credit_partners")
  .createAny("inventory")
  .updateAny("inventory")
  .deleteAny("inventory")
  .createAny("stock_items")
  .updateAny("stock_items")
  .deleteAny("stock_items")
  .createAny("banking")
  .updateAny("banking")
  .deleteAny("banking")
  .readAny("users")
  .createAny("users")
  .updateAny("users")
  .deleteAny("users");

// Tenant Admin - Alias for Company Admin (for backward compatibility)
ac.grant("tenant_admin")
  .extend("accountant")
  // Add explicit permissions for expense approvals
  .createAny("expense_first_approval")
  .createAny("expense_final_approval")
  // Other permissions
  .createAny("roles")
  .updateAny("roles")
  .deleteAny("roles")
  .createAny("permissions")
  .updateAny("permissions")
  .deleteAny("permissions")
  .createAny("employees")
  .updateAny("employees")
  .deleteAny("employees")
  .createAny("branches")
  .updateAny("branches")
  .deleteAny("branches")
  .createAny("products")
  .updateAny("products")
  .deleteAny("products")
  .createAny("categories")
  .updateAny("categories")
  .deleteAny("categories")
  .createAny("brands")
  .updateAny("brands")
  .deleteAny("brands")
  // Credit partners permissions
  .readAny("credit_partners")
  .createAny("credit_partners")
  .updateAny("credit_partners")
  .deleteAny("credit_partners")
  .createAny("inventory")
  .updateAny("inventory")
  .deleteAny("inventory")
  .createAny("stock_items")
  .updateAny("stock_items")
  .deleteAny("stock_items")
  .createAny("banking")
  .updateAny("banking")
  .deleteAny("banking")
  .readAny("users")
  .createAny("users")
  .updateAny("users")
  .deleteAny("users");

// Stock Admin - Manages inventory, products, and stock operations
ac.grant("stock_admin")
  .readOwn("profile")
  .readAny("users")
  .readAny("products")
  .createAny("products")
  .updateAny("products")
  .readAny("stock_movement")
  .createAny("stock_movement")
  .updateAny("stock_movement")
  .readAny("stock_request")
  .createAny("stock_request")
  .updateAny("stock_request")
  .readAny("stock_reports")
  .readAny("inventory")
  .createAny("inventory")
  .updateAny("inventory")
  .readAny("stock_items")
  .createAny("stock_items")
  .updateAny("stock_items");

// Finance Manager - Manages financial operations and approvals
ac.grant("finance_manager")
  .readOwn("profile")
  .readAny("users")
  .readAny("sales")
  .readAny("pos_sessions")
  .readAny("expenses")
  .updateAny("expenses")
  .createAny("expense_first_approval")
  .createAny("expense_final_approval")
  .readAny("banking")
  .createAny("banking")
  .updateAny("banking")
  .readAny("financial_reports")
  .readAny("expense_reports");

// Auditor - Read-only access for auditing purposes
ac.grant("auditor")
  .readOwn("profile")
  .readAny("users")
  .readAny("sales")
  .readAny("pos_sessions")
  .readAny("expenses")
  .readAny("stock_movement")
  .readAny("stock_request")
  .readAny("stock_reports")
  .readAny("inventory")
  .readAny("banking")
  .readAny("financial_reports")
  .readAny("expense_reports");

// Operations - Basic operations role
ac.grant("operations")
  .readOwn("profile")
  .readAny("users")
  .readAny("sales")
  .readAny("pos_sessions")
  .readAny("expenses")
  .createOwn("expenses")
  .updateOwn("expenses")
  .readAny("stock_movement")
  .readAny("stock_request")
  .readAny("inventory");

// Float Manager - Manages MPESA float, cash float, and banking operations
ac.grant("float_manager")
  .readOwn("profile")
  .readAny("users")
  .readAny("mpesa_float")
  .createAny("mpesa_float")
  .updateAny("mpesa_float")
  .readAny("mpesa-float")
  .createAny("mpesa_float_assignment")
  .updateAny("mpesa_float_assignment")
  .deleteAny("mpesa_float_assignment")
  .readAny("mpesa-float-balances")
  .createAny("mpesa-float-balances")
  .readAny("mpesa-float-reconciliations")
  .createAny("mpesa-float-reconciliations")
  .readAny("mpesa-float-movements")
  .createAny("mpesa-float-movements")
  .readAny("mpesa_float_movements")
  .createAny("mpesa_float_movements")
  .readAny("mpesa-float-topups")
  .readAny("mpesa_float_topups")
  .createAny("mpesa_float_topups")
  .readAny("mpesa_transactions")
  .createAny("mpesa_transactions")
  .readAny("mpesa-transactions")
  .createAny("mpesa-transactions")
  .readAny("banking")
  .createAny("banking")
  .updateAny("banking")
  .createAny("pos-session-reconciliations")
  .readAny("pos-session-reconciliations")
  .readAny("reports")
  .readAny("financial-reports")
  .readAny("analytics")
  .createAny("cash-float")
  .readAny("cash-float")
  .readAny("branches")
  // Banking and float approval permissions
  .createAny("banking_approval")
  .createAny("banking_rejection")
  .createAny("mpesa_float_approval")
  .createAny("mpesa_float_rejection")
  .createAny("cash_float_approval")
  .createAny("cash_float_rejection")
  .createAny("float_reconciliation_approval")
  .createAny("float_reconciliation_rejection");

// Cashier - Basic banking access
ac.grant("cashier").readAny("banking");

// OPM - Operations Manager alias
ac.grant("opm").readAny("banking");

// Assistant OPM - Assistant Operations Manager alias
ac.grant("assistant_opm").readAny("banking");

// Super Admin - Full system access
ac.grant("super_admin")
  // Include all company_admin permissions explicitly
  .extend("accountant")
  // Add explicit permissions for expense approvals
  .createAny("expense_first_approval")
  .createAny("expense_final_approval")
  // Other company_admin permissions
  .createAny("roles")
  .updateAny("roles")
  .deleteAny("roles")
  .createAny("permissions")
  .updateAny("permissions")
  .deleteAny("permissions")
  .createAny("employees")
  .updateAny("employees")
  .deleteAny("employees")
  .createAny("branches")
  .updateAny("branches")
  .deleteAny("branches")
  .createAny("products")
  .updateAny("products")
  .deleteAny("products")
  .createAny("categories")
  .updateAny("categories")
  .deleteAny("categories")
  .createAny("brands")
  .updateAny("brands")
  .deleteAny("brands")
  // Credit partners permissions
  .readAny("credit_partners")
  .createAny("credit_partners")
  .updateAny("credit_partners")
  .deleteAny("credit_partners")
  .createAny("inventory")
  .updateAny("inventory")
  .deleteAny("inventory")
  .createAny("stock_items")
  .updateAny("stock_items")
  .deleteAny("stock_items")
  .createAny("banking")
  .updateAny("banking")
  .deleteAny("banking")
  // Super admin specific permissions
  .createAny("tenants")
  .readAny("tenants")
  .updateAny("tenants")
  .deleteAny("tenants")
  .createAny("system_settings")
  .readAny("system_settings")
  .updateAny("system_settings")
  .deleteAny("system_settings")
  .readAny("users")
  .createAny("users")
  .updateAny("users")
  .deleteAny("users");

// Export the configured AccessControl instance
module.exports = ac;
