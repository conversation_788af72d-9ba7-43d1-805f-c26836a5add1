const mysql = require('mysql2/promise');
const jwt = require('jsonwebtoken');
const axios = require('axios');
require('dotenv').config();

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${new Date().toISOString()} - ${msg}`)
};

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dukalink_api',
  port: process.env.DB_PORT || 3306
};

// API configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const JWT_SECRET = process.env.JWT_SECRET;

async function simulateFloatManagerApproval() {
  let connection;
  
  try {
    logger.info('Starting float_manager approval simulation...');
    
    // Connect to database
    logger.info('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Step 1: Get float_manager user details
    logger.info('=== STEP 1: Getting float_manager user details ===');
    const [users] = await connection.execute(`
      SELECT u.*, r.name as role_name 
      FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE r.name = 'float_manager' AND u.deleted_at IS NULL 
      LIMIT 1
    `);
    
    if (users.length === 0) {
      throw new Error('No float_manager user found');
    }
    
    const floatManagerUser = users[0];
    logger.info(`✓ Found float_manager user: ${floatManagerUser.name} (${floatManagerUser.email})`);
    
    // Step 2: Generate JWT token for the float_manager user
    logger.info('=== STEP 2: Generating JWT token ===');
    if (!JWT_SECRET) {
      throw new Error('JWT_SECRET not found in environment variables');
    }
    
    const tokenPayload = {
      id: floatManagerUser.id,
      email: floatManagerUser.email,
      role_id: floatManagerUser.role_id,
      tenant_id: floatManagerUser.tenant_id,
      branch_id: floatManagerUser.branch_id
    };
    
    const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '1h' });
    logger.info('✓ JWT token generated successfully');
    
    // Step 3: Get pending banking transactions
    logger.info('=== STEP 3: Getting pending banking transactions ===');
    const [transactions] = await connection.execute(`
      SELECT id, amount, banking_method, status, created_at, branch_id
      FROM banking_transactions 
      WHERE status = 'pending' AND deleted_at IS NULL 
      ORDER BY created_at DESC 
      LIMIT 3
    `);
    
    if (transactions.length === 0) {
      logger.warn('No pending banking transactions found. Creating a test transaction...');
      
      // Create a test transaction
      const [result] = await connection.execute(`
        INSERT INTO banking_transactions 
        (tenant_id, branch_id, user_id, amount, banking_method, status, created_by, last_updated_by, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        floatManagerUser.tenant_id,
        floatManagerUser.branch_id,
        floatManagerUser.id,
        5000.00,
        'bank',
        'pending',
        floatManagerUser.id,
        floatManagerUser.id
      ]);
      
      const testTransactionId = result.insertId;
      logger.info(`✓ Created test banking transaction with ID: ${testTransactionId}`);
      
      // Get the created transaction
      const [newTransactions] = await connection.execute(`
        SELECT id, amount, banking_method, status, created_at, branch_id
        FROM banking_transactions 
        WHERE id = ?
      `, [testTransactionId]);
      
      transactions.push(newTransactions[0]);
    }
    
    logger.info(`✓ Found ${transactions.length} pending transactions for testing`);
    
    // Step 4: Test approval functionality
    logger.info('=== STEP 4: Testing approval functionality ===');
    
    const testTransaction = transactions[0];
    logger.info(`Testing with transaction ID: ${testTransaction.id}, Amount: ${testTransaction.amount}`);
    
    // Test 1: Approve a transaction
    logger.info('--- Test 1: Approving banking transaction ---');
    try {
      const approvalResponse = await axios.post(
        `${API_BASE_URL}/api/v1/banking/${testTransaction.id}/approve`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );
      
      if (approvalResponse.status === 200) {
        logger.success('✓ Banking transaction approved successfully!');
        logger.info(`Response status: ${approvalResponse.status}`);
        logger.info(`Transaction status: ${approvalResponse.data.data.status}`);
        logger.info(`Approved by: ${approvalResponse.data.data.approved_by}`);
      } else {
        logger.error(`✗ Unexpected response status: ${approvalResponse.status}`);
      }
    } catch (error) {
      if (error.response) {
        logger.error(`✗ Approval failed: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
        if (error.response.data.details) {
          logger.error(`Details: ${JSON.stringify(error.response.data.details)}`);
        }
      } else if (error.code === 'ECONNREFUSED') {
        logger.error('✗ Cannot connect to API server. Make sure the server is running.');
      } else {
        logger.error(`✗ Approval request failed: ${error.message}`);
      }
    }
    
    // Step 5: Test rejection functionality (if we have another transaction)
    if (transactions.length > 1) {
      logger.info('--- Test 2: Rejecting banking transaction ---');
      const rejectTransaction = transactions[1];
      
      try {
        const rejectionResponse = await axios.post(
          `${API_BASE_URL}/api/v1/banking/${rejectTransaction.id}/reject`,
          {
            rejection_reason: 'Test rejection by float_manager - insufficient documentation'
          },
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            timeout: 10000
          }
        );
        
        if (rejectionResponse.status === 200) {
          logger.success('✓ Banking transaction rejected successfully!');
          logger.info(`Response status: ${rejectionResponse.status}`);
          logger.info(`Transaction status: ${rejectionResponse.data.data.status}`);
          logger.info(`Rejection reason: ${rejectionResponse.data.data.rejection_reason}`);
        } else {
          logger.error(`✗ Unexpected response status: ${rejectionResponse.status}`);
        }
      } catch (error) {
        if (error.response) {
          logger.error(`✗ Rejection failed: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
        } else if (error.code === 'ECONNREFUSED') {
          logger.error('✗ Cannot connect to API server. Make sure the server is running.');
        } else {
          logger.error(`✗ Rejection request failed: ${error.message}`);
        }
      }
    }
    
    // Step 6: Verify database changes
    logger.info('=== STEP 5: Verifying database changes ===');
    const [updatedTransactions] = await connection.execute(`
      SELECT id, status, approved_by, approval_date, rejection_reason
      FROM banking_transactions 
      WHERE id IN (${transactions.map(t => t.id).join(',')})
    `);
    
    logger.info('Updated transaction statuses:');
    updatedTransactions.forEach(tx => {
      logger.info(`  - Transaction ${tx.id}: ${tx.status}${tx.approved_by ? ` (approved by user ${tx.approved_by})` : ''}${tx.rejection_reason ? ` (reason: ${tx.rejection_reason})` : ''}`);
    });
    
    // Step 7: Test with invalid user (should fail)
    logger.info('=== STEP 6: Testing with invalid permissions (should fail) ===');
    
    // Create a token for a user without approval permissions
    const invalidTokenPayload = {
      id: 999,
      email: '<EMAIL>',
      role_id: 1, // Assuming this is a role without approval permissions
      tenant_id: floatManagerUser.tenant_id,
      branch_id: floatManagerUser.branch_id
    };
    
    const invalidToken = jwt.sign(invalidTokenPayload, JWT_SECRET, { expiresIn: '1h' });
    
    if (transactions.length > 2) {
      try {
        const invalidResponse = await axios.post(
          `${API_BASE_URL}/api/v1/banking/${transactions[2].id}/approve`,
          {},
          {
            headers: {
              'Authorization': `Bearer ${invalidToken}`,
              'Content-Type': 'application/json'
            },
            timeout: 10000
          }
        );
        
        logger.error('✗ Invalid user was able to approve transaction (this should not happen!)');
      } catch (error) {
        if (error.response && (error.response.status === 403 || error.response.status === 401)) {
          logger.success('✓ Invalid user correctly denied approval permissions');
        } else {
          logger.warn(`Unexpected error for invalid user: ${error.message}`);
        }
      }
    }
    
    logger.info('=== SIMULATION COMPLETE ===');
    logger.success('Float manager approval simulation completed successfully!');
    
  } catch (error) {
    logger.error(`Simulation failed: ${error.message}`);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info('Database connection closed.');
    }
  }
}

// Execute the simulation
if (require.main === module) {
  simulateFloatManagerApproval()
    .then(() => {
      logger.success('Simulation completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`Simulation failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { simulateFloatManagerApproval };
