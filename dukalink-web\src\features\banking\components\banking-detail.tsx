"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";
import { ArrowLeft, Pencil } from "lucide-react";
import { useRouter } from "next/navigation";
import { useBankingRecord } from "../hooks/use-banking";
import { ReceiptImages } from "./receipt-images";
import { BankingApproval } from "./banking-approval";
import { useCurrentUser } from "@/features/auth/hooks/use-auth";

interface BankingDetailProps {
  bankingId: number;
}

export function BankingDetail({ bankingId }: BankingDetailProps) {
  const router = useRouter();
  const { data: user } = useCurrentUser();
  const {
    data: banking,
    isLoading,
    error,
    refetch,
  } = useBankingRecord(bankingId);

  // Check if user can edit banking records (company_admin, super_admin, or float_manager)
  const canEdit =
    user?.role_name === "company_admin" ||
    user?.role_name === "super_admin" ||
    user?.role_name === "float_manager";

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-64" />
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-24 w-full" />
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !banking) {
    return (
      <div className="rounded-md bg-destructive/10 p-4 text-destructive">
        Error loading banking record. Please try again.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push("/banking")}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Banking
        </Button>
        <div className="flex gap-2">
          {/* Add the BankingApproval component */}
          <BankingApproval banking={banking} onUpdate={() => refetch()} />

          {canEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/banking/${bankingId}/edit`)}
            >
              <Pencil className="mr-2 h-4 w-4" />
              Edit
            </Button>
          )}
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Banking Record #{banking.id}</CardTitle>
              <CardDescription>
                Created on {format(new Date(banking.created_at), "PPP")}
              </CardDescription>
            </div>
            <Badge
              variant={
                banking.status === "completed"
                  ? "default"
                  : banking.status === "pending"
                  ? "outline"
                  : "destructive"
              }
              className="text-sm"
            >
              {banking.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Transaction Date
              </p>
              <p>{format(new Date(banking.transaction_date), "PPP")}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Banking Method
              </p>
              <p className="capitalize">{banking.banking_method}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Reference Number
              </p>
              <p>{banking.reference_number}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Branch ID
              </p>
              <p>{banking.branch_id}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Amount
              </p>
              <p className="text-lg font-semibold">
                {formatCurrency(banking.amount)}
              </p>
            </div>
          </div>

          {banking.notes && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Notes</p>
              <div className="rounded-md bg-muted p-3">
                <p className="text-sm">{banking.notes}</p>
              </div>
            </div>
          )}

          {/* Approval Information */}
          {banking.status !== "pending" && (
            <div className="border-t pt-4 mt-4">
              <h3 className="text-sm font-medium mb-2">Approval Information</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">
                    Status
                  </p>
                  <Badge
                    variant={
                      banking.status === "completed"
                        ? "default"
                        : banking.status === "pending"
                        ? "outline"
                        : "destructive"
                    }
                  >
                    {banking.status === "completed"
                      ? "Approved"
                      : banking.status === "failed"
                      ? "Rejected"
                      : banking.status}
                  </Badge>
                </div>

                {banking.approved_by && (
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">
                      {banking.status === "completed"
                        ? "Approved By"
                        : "Rejected By"}
                    </p>
                    <p>User #{banking.approved_by}</p>
                  </div>
                )}

                {banking.approval_date && (
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">
                      {banking.status === "completed"
                        ? "Approval Date"
                        : "Rejection Date"}
                    </p>
                    <p>{format(new Date(banking.approval_date), "PPP")}</p>
                  </div>
                )}

                {banking.rejection_reason && (
                  <div className="space-y-1 md:col-span-2">
                    <p className="text-sm font-medium text-muted-foreground">
                      Rejection Reason
                    </p>
                    <div className="rounded-md bg-destructive/10 p-3">
                      <p className="text-sm">{banking.rejection_reason}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="border-t bg-muted/50 px-6 py-3">
          <div className="flex w-full items-center justify-between text-xs text-muted-foreground">
            <div>Created by: User #{banking.created_by}</div>
            <div>
              Last updated: {format(new Date(banking.updated_at), "PPp")}
            </div>
          </div>
        </CardFooter>
      </Card>

      {/* Receipt Images */}
      <ReceiptImages banking={banking} onUpdate={() => refetch()} />
    </div>
  );
}
