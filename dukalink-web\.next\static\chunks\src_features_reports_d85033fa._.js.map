{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/api/reports-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { ReportFilterParams, Sale } from \"@/types\";\r\nimport { PhoneRepair, PhoneRepairFilters } from \"@/types/phone-repair\";\r\nimport { MpesaTransaction } from \"@/types/mpesa\";\r\n\r\n/**\r\n * Reports Service\r\n * Handles API calls for reports\r\n */\r\nconst reportsService = {\r\n  /**\r\n   * Get sales data for reports\r\n   */\r\n  getSalesReport: async (filters?: ReportFilterParams): Promise<Sale[]> => {\r\n    const response = await apiClient.get<Sale[]>(\"/sales\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get sales by item data\r\n   */\r\n  getSalesByItemReport: async (\r\n    filters?: ReportFilterParams\r\n  ): Promise<Sale[]> => {\r\n    // This uses the same endpoint as getSalesReport but is separated for clarity\r\n    const response = await apiClient.get<Sale[]>(\"/sales\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get sales by category data\r\n   */\r\n  getSalesByCategoryReport: async (\r\n    filters?: ReportFilterParams\r\n  ): Promise<Sale[]> => {\r\n    // This uses the same endpoint as getSalesReport but is separated for clarity\r\n    const response = await apiClient.get<Sale[]>(\"/sales\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get sales by employee data\r\n   */\r\n  getSalesByEmployeeReport: async (\r\n    filters?: ReportFilterParams\r\n  ): Promise<Sale[]> => {\r\n    // This uses the same endpoint as getSalesReport but is separated for clarity\r\n    const response = await apiClient.get<Sale[]>(\"/sales\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get sales by payment type data\r\n   */\r\n  getSalesByPaymentTypeReport: async (\r\n    filters?: ReportFilterParams\r\n  ): Promise<Sale[]> => {\r\n    // This uses the same endpoint as getSalesReport but is separated for clarity\r\n    const response = await apiClient.get<Sale[]>(\"/sales\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get POS sessions (shifts) data\r\n   */\r\n  getShiftsReport: async (filters?: ReportFilterParams): Promise<any[]> => {\r\n    const response = await apiClient.get<any[]>(\"/pos-sessions\", {\r\n      params: {\r\n        ...filters,\r\n        include_sales_totals: true, // Request the backend to include sales totals\r\n      },\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get phone repairs data for reports\r\n   */\r\n  getPhoneRepairsReport: async (\r\n    filters?: PhoneRepairFilters\r\n  ): Promise<PhoneRepair[]> => {\r\n    const response = await apiClient.get<PhoneRepair[]>(\"/phone-repairs\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get tax report data\r\n   */\r\n  getTaxReport: async (filters?: ReportFilterParams): Promise<Sale[]> => {\r\n    // This uses the same endpoint as getSalesReport but is separated for clarity\r\n    // We need to include SaleItems to get VAT information\r\n    const response = await apiClient.get<Sale[]>(\"/sales\", {\r\n      params: {\r\n        ...filters,\r\n        include_items: true, // Make sure to include sale items for VAT data\r\n      },\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get MPESA transactions data for reports\r\n   */\r\n  getMpesaTransactionsReport: async (filters?: ReportFilterParams): Promise<MpesaTransaction[]> => {\r\n    const response = await apiClient.get<{\r\n      data: MpesaTransaction[];\r\n      meta: {\r\n        total: number;\r\n        page: number;\r\n        limit: number;\r\n        totalPages: number;\r\n      };\r\n    }>(\"/mpesa-transactions\", {\r\n      params: {\r\n        ...filters,\r\n        limit: 1000, // Get a large number of transactions for reporting\r\n      },\r\n    });\r\n    return response.data;\r\n  },\r\n};\r\n\r\nexport default reportsService;\r\n"], "names": [], "mappings": ";;;AAAA;;AAKA;;;CAGC,GACD,MAAM,iBAAiB;IACrB;;GAEC,GACD,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAS,UAAU;YACrD,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,sBAAsB,OACpB;QAEA,6EAA6E;QAC7E,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAS,UAAU;YACrD,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,0BAA0B,OACxB;QAEA,6EAA6E;QAC7E,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAS,UAAU;YACrD,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,0BAA0B,OACxB;QAEA,6EAA6E;QAC7E,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAS,UAAU;YACrD,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,6BAA6B,OAC3B;QAEA,6EAA6E;QAC7E,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAS,UAAU;YACrD,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,iBAAiB,OAAO;QACtB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAQ,iBAAiB;YAC3D,QAAQ;gBACN,GAAG,OAAO;gBACV,sBAAsB;YACxB;QACF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,uBAAuB,OACrB;QAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAgB,kBAAkB;YACpE,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,cAAc,OAAO;QACnB,6EAA6E;QAC7E,sDAAsD;QACtD,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAS,UAAU;YACrD,QAAQ;gBACN,GAAG,OAAO;gBACV,eAAe;YACjB;QACF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,4BAA4B,OAAO;QACjC,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAQjC,uBAAuB;YACxB,QAAQ;gBACN,GAAG,OAAO;gBACV,OAAO;YACT;QACF;QACA,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/hooks/use-reports.ts"], "sourcesContent": ["import {\r\n  BankingSummary,\r\n  BankingSummaryFilters,\r\n  BankingFilters,\r\n  ChartData,\r\n  ReportFilterParams,\r\n  Sale,\r\n  SalesByCategoryData,\r\n  SalesByEmployeeData,\r\n  SalesByItemData,\r\n  SalesByPaymentTypeData,\r\n  TaxReportData,\r\n  processSalesByItem,\r\n  processSalesSummary,\r\n} from \"@/types\";\r\nimport { PhoneRepair, PhoneRepairFilters } from \"@/types/phone-repair\";\r\nimport { MpesaTransaction } from \"@/types/mpesa\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { format, parseISO } from \"date-fns\";\r\nimport bankingService from \"@/features/banking/api/banking-service\";\r\nimport reportsService from \"../api/reports-service\";\r\n// import apiClient from \"@/lib/api-client\";\r\n\r\n/**\r\n * Hook to fetch sales summary report data\r\n */\r\nexport const useSalesSummaryReport = (filters?: ReportFilterParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"sales-summary\", filters],\r\n    queryFn: async () => {\r\n      // Create a copy of filters without employee_id for the API call\r\n      // We now pass region_id to the API since we've updated the backend to support it\r\n      const { employee_id, ...apiFilters } = filters || {};\r\n\r\n      // Fetch sales data with region filter (API now supports region_id)\r\n      const salesData = await reportsService.getSalesReport(apiFilters);\r\n\r\n      // Apply employee filter client-side\r\n      let filteredSalesData = salesData;\r\n\r\n      // Apply payment method filter client-side if API doesn't handle it correctly\r\n      if (apiFilters.payment_method_id) {\r\n        filteredSalesData = filteredSalesData.filter(\r\n          (sale) => sale.payment_method_id === apiFilters.payment_method_id\r\n        );\r\n      }\r\n\r\n      // Apply employee filter client-side\r\n      if (employee_id) {\r\n        filteredSalesData = filteredSalesData.filter(\r\n          (sale) => sale.employee_id === employee_id || sale.sale_employee_id === employee_id\r\n        );\r\n      }\r\n\r\n      const summaryData = processSalesSummary(filteredSalesData);\r\n\r\n      // Process data for charts\r\n      const chartData = processChartData(filteredSalesData);\r\n\r\n      return {\r\n        salesData: filteredSalesData,\r\n        summaryData,\r\n        chartData,\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch sales by item report data\r\n */\r\nexport const useSalesByItemReport = (filters?: ReportFilterParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"sales-by-item\", filters],\r\n    queryFn: async () => {\r\n      // Create a copy of filters without employee_id for the API call\r\n      // We now pass region_id to the API since we've updated the backend to support it\r\n      const { employee_id, ...apiFilters } = filters || {};\r\n\r\n      // Fetch sales data with region filter (API now supports region_id)\r\n      const salesData = await reportsService.getSalesByItemReport(apiFilters);\r\n\r\n      // Apply employee filter client-side if needed\r\n      let filteredSalesData = salesData;\r\n\r\n      if (employee_id) {\r\n        filteredSalesData = filteredSalesData.filter(\r\n          (sale) => sale.employee_id === employee_id || sale.sale_employee_id === employee_id\r\n        );\r\n      }\r\n\r\n      const itemsData = processSalesByItem(filteredSalesData);\r\n\r\n      // Sort by total sales descending\r\n      itemsData.sort((a, b) => b.totalSales - a.totalSales);\r\n\r\n      // Process data for charts\r\n      const chartData = processItemChartData(itemsData);\r\n\r\n      return {\r\n        salesData: filteredSalesData,\r\n        itemsData,\r\n        chartData,\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch sales by category report data\r\n */\r\nexport const useSalesByCategoryReport = (filters?: ReportFilterParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"sales-by-category\", filters],\r\n    queryFn: async () => {\r\n      // Create a copy of filters without employee_id for the API call\r\n      // We now pass region_id to the API since we've updated the backend to support it\r\n      const { employee_id, ...apiFilters } = filters || {};\r\n\r\n      // Fetch sales data with region filter (API now supports region_id)\r\n      const salesData = await reportsService.getSalesByCategoryReport(apiFilters);\r\n\r\n      // Process category data\r\n      const categoryMap = new Map<number, SalesByCategoryData>();\r\n      let totalSales = 0;\r\n\r\n      salesData.forEach((sale) => {\r\n        if (!sale.SaleItems) return;\r\n\r\n        sale.SaleItems.forEach((item) => {\r\n          if (!item.Product) return;\r\n\r\n          // Get category information from the Product object\r\n          // The backend now includes the ProductCategory model in the Product\r\n          const categoryId = item.Product.category_id || 0;\r\n          // Access the ProductCategory directly (no alias)\r\n          const categoryName = item.Product.ProductCategory?.name || \"Uncategorized\";\r\n          const saleAmount = parseFloat(item.total_price);\r\n          const cost = parseFloat(item.buying_price) * item.quantity;\r\n          const profit = saleAmount - cost;\r\n\r\n          totalSales += saleAmount;\r\n\r\n          const existingCategory = categoryMap.get(categoryId);\r\n          if (existingCategory) {\r\n            existingCategory.quantity += item.quantity;\r\n            existingCategory.totalSales += saleAmount;\r\n            existingCategory.profit += profit;\r\n          } else {\r\n            categoryMap.set(categoryId, {\r\n              categoryId,\r\n              categoryName,\r\n              quantity: item.quantity,\r\n              totalSales: saleAmount,\r\n              profit,\r\n              percentage: 0, // Will calculate after processing all sales\r\n            });\r\n          }\r\n        });\r\n      });\r\n\r\n      // Calculate percentages\r\n      const categoryData = Array.from(categoryMap.values());\r\n      categoryData.forEach((category) => {\r\n        category.percentage =\r\n          totalSales > 0 ? (category.totalSales / totalSales) * 100 : 0;\r\n      });\r\n\r\n      // Sort by total sales descending\r\n      categoryData.sort((a, b) => b.totalSales - a.totalSales);\r\n\r\n      // Process data for charts\r\n      const chartData = processCategoryChartData(categoryData);\r\n\r\n      return {\r\n        salesData,\r\n        categoryData,\r\n        chartData,\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch sales by employee report data\r\n */\r\nexport const useSalesByEmployeeReport = (filters?: ReportFilterParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"sales-by-employee\", filters],\r\n    queryFn: async () => {\r\n      // Create a copy of filters without employee_id for the API call\r\n      // We now pass region_id to the API since we've updated the backend to support it\r\n      const { employee_id, ...apiFilters } = filters || {};\r\n\r\n      // Fetch sales data with region filter (API now supports region_id)\r\n      const salesData = await reportsService.getSalesByEmployeeReport(apiFilters);\r\n\r\n      // Apply employee filter client-side if needed\r\n      let filteredSalesData = salesData;\r\n      if (employee_id) {\r\n        filteredSalesData = filteredSalesData.filter(\r\n          (sale) => sale.employee_id === employee_id || sale.sale_employee_id === employee_id || sale.user_id === employee_id\r\n        );\r\n      }\r\n\r\n      // Process employee data\r\n      const employeeMap = new Map<number, SalesByEmployeeData>();\r\n\r\n      filteredSalesData.forEach((sale) => {\r\n        if (!sale.User) return;\r\n\r\n        const userId = sale.user_id;\r\n        const userName = sale.User.name || \"Unknown\";\r\n        const saleAmount = parseFloat(sale.total_amount);\r\n\r\n        // Calculate profit\r\n        let cost = 0;\r\n        if (sale.SaleItems) {\r\n          cost = sale.SaleItems.reduce(\r\n            (sum, item) => sum + parseFloat(item.buying_price) * item.quantity,\r\n            0\r\n          );\r\n        }\r\n        const profit = saleAmount - cost;\r\n\r\n        const existingEmployee = employeeMap.get(userId);\r\n        if (existingEmployee) {\r\n          existingEmployee.totalSales += saleAmount;\r\n          existingEmployee.totalTransactions += 1;\r\n          existingEmployee.profit += profit;\r\n        } else {\r\n          employeeMap.set(userId, {\r\n            userId,\r\n            userName,\r\n            totalSales: saleAmount,\r\n            totalTransactions: 1,\r\n            averageSale: saleAmount,\r\n            profit,\r\n          });\r\n        }\r\n      });\r\n\r\n      // Calculate average sale\r\n      const employeeData = Array.from(employeeMap.values());\r\n      employeeData.forEach((employee) => {\r\n        employee.averageSale =\r\n          employee.totalTransactions > 0\r\n            ? employee.totalSales / employee.totalTransactions\r\n            : 0;\r\n      });\r\n\r\n      // Sort by total sales descending\r\n      employeeData.sort((a, b) => b.totalSales - a.totalSales);\r\n\r\n      // Process data for charts\r\n      const chartData = processEmployeeChartData(employeeData);\r\n\r\n      return {\r\n        salesData: filteredSalesData,\r\n        employeeData,\r\n        chartData,\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch sales by payment type report data\r\n */\r\nexport const useSalesByPaymentTypeReport = (filters?: ReportFilterParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"sales-by-payment-type\", filters],\r\n    queryFn: async () => {\r\n      const salesData = await reportsService.getSalesByPaymentTypeReport(\r\n        filters\r\n      );\r\n\r\n      // Process payment type data\r\n      const paymentMap = new Map<number, SalesByPaymentTypeData>();\r\n      let totalSales = 0;\r\n\r\n      salesData.forEach((sale) => {\r\n        if (!sale.PaymentMethod) return;\r\n\r\n        const paymentMethodId = sale.payment_method_id;\r\n        const paymentMethodName = sale.PaymentMethod.name || \"Unknown\";\r\n        const saleAmount = parseFloat(sale.total_amount);\r\n\r\n        totalSales += saleAmount;\r\n\r\n        const existingPayment = paymentMap.get(paymentMethodId);\r\n        if (existingPayment) {\r\n          existingPayment.totalSales += saleAmount;\r\n          existingPayment.totalTransactions += 1;\r\n        } else {\r\n          paymentMap.set(paymentMethodId, {\r\n            paymentMethodId,\r\n            paymentMethodName,\r\n            totalSales: saleAmount,\r\n            totalTransactions: 1,\r\n            averageSale: saleAmount,\r\n            percentage: 0, // Will calculate after processing all sales\r\n          });\r\n        }\r\n      });\r\n\r\n      // Calculate percentages and average sale\r\n      const paymentData = Array.from(paymentMap.values());\r\n      paymentData.forEach((payment) => {\r\n        payment.percentage =\r\n          totalSales > 0 ? (payment.totalSales / totalSales) * 100 : 0;\r\n        payment.averageSale =\r\n          payment.totalTransactions > 0\r\n            ? payment.totalSales / payment.totalTransactions\r\n            : 0;\r\n      });\r\n\r\n      // Sort by total sales descending\r\n      paymentData.sort((a, b) => b.totalSales - a.totalSales);\r\n\r\n      // Process data for charts\r\n      const chartData = processPaymentChartData(paymentData);\r\n\r\n      return {\r\n        salesData,\r\n        paymentData,\r\n        chartData,\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch shifts (POS sessions) report data\r\n */\r\nexport const useShiftsReport = (filters?: ReportFilterParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"shifts\", filters],\r\n    queryFn: async () => {\r\n      const shiftsData = await reportsService.getShiftsReport(filters);\r\n\r\n      // Process shifts data\r\n      const processedShifts = shiftsData.map((shift) => {\r\n        // Use the total sales and transaction count from the backend if available\r\n        // Otherwise, fall back to calculating from Sales array (for backward compatibility)\r\n        let totalSales = 0;\r\n        let totalTransactions = 0;\r\n\r\n        if (shift.total_sales !== undefined && shift.transaction_count !== undefined) {\r\n          // Use the values provided by the backend\r\n          totalSales = parseFloat(shift.total_sales);\r\n          totalTransactions = parseInt(shift.transaction_count);\r\n          console.log(`Using backend totals for shift ${shift.id}: sales=${totalSales}, transactions=${totalTransactions}`);\r\n        } else if (shift.Sales) {\r\n          // Fall back to calculating from Sales array\r\n          totalSales = shift.Sales.reduce(\r\n            (sum: number, sale: any) => sum + parseFloat(sale.total_amount),\r\n            0\r\n          );\r\n          totalTransactions = shift.Sales.length;\r\n          console.log(`Calculated totals for shift ${shift.id}: sales=${totalSales}, transactions=${totalTransactions}`);\r\n        }\r\n\r\n        // ✅ Expenses calculation\r\n        const expenses = shift.Expenses\r\n          ? shift.Expenses.reduce((sum: number, expense: any) => {\r\n              return expense.status === \"approved\"\r\n                ? sum + parseFloat(expense.amount)\r\n                : sum;\r\n            }, 0)\r\n          : 0;\r\n        return {\r\n          id: shift.id,\r\n          startTime: shift.start_time,\r\n          endTime: shift.end_time,\r\n          status: shift.status,\r\n          openingBalance: shift.opening_balance\r\n            ? parseFloat(shift.opening_balance)\r\n            : 0,\r\n          closingBalance: shift.closing_balance\r\n            ? parseFloat(shift.closing_balance)\r\n            : null,\r\n          totalSales,\r\n          totalTransactions,\r\n          cashPaidIn: shift.cash_paid_in\r\n            ? parseFloat(shift.cash_paid_in)\r\n            : null,\r\n          cashPaidOut: shift.cash_paid_out\r\n            ? parseFloat(shift.cash_paid_out)\r\n            : null,\r\n          discrepancies: shift.discrepancies\r\n            ? parseFloat(shift.discrepancies)\r\n            : null,\r\n          userId: shift.user_id,\r\n          userName: shift.User?.name || \"Unknown\",\r\n          branchId: shift.branch_id,\r\n          branchName: shift.Branch?.name || \"Unknown\",\r\n          reconciliation: shift.reconciliation || null,\r\n          expenses, // computed approved expenses total\r\n\r\n        };\r\n      });\r\n\r\n      // Sort by start time descending\r\n      processedShifts.sort(\r\n        (a, b) =>\r\n          new Date(b.startTime).getTime() - new Date(a.startTime).getTime()\r\n      );\r\n\r\n      return {\r\n        shiftsData: processedShifts,\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Process sales data into chart data\r\n */\r\nfunction processChartData(salesData: Sale[]): ChartData {\r\n  // Group sales by date\r\n  const salesByDate = new Map<string, number>();\r\n\r\n  salesData.forEach((sale) => {\r\n    const date = format(parseISO(sale.created_at), \"yyyy-MM-dd\");\r\n    const amount = parseFloat(sale.total_amount);\r\n\r\n    const existingAmount = salesByDate.get(date) || 0;\r\n    salesByDate.set(date, existingAmount + amount);\r\n  });\r\n\r\n  // Sort dates\r\n  const sortedDates = Array.from(salesByDate.keys()).sort();\r\n\r\n  // Create chart data\r\n  return {\r\n    labels: sortedDates.map((date) => format(parseISO(date), \"MMM dd\")),\r\n    datasets: [\r\n      {\r\n        label: \"Sales\",\r\n        data: sortedDates.map((date) => salesByDate.get(date) || 0),\r\n        backgroundColor: \"rgba(59, 130, 246, 0.5)\",\r\n        borderColor: \"rgb(59, 130, 246)\",\r\n        borderWidth: 2,\r\n      },\r\n    ],\r\n  };\r\n}\r\n\r\n/**\r\n * Hook to fetch MPESA transactions report data\r\n */\r\nexport const useMpesaTransactionsReport = (filters?: ReportFilterParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"mpesa-transactions\", filters],\r\n    queryFn: async () => {\r\n      const transactionsData = await reportsService.getMpesaTransactionsReport(filters);\r\n\r\n      // Process transactions data\r\n      const depositsData = transactionsData.filter(tx => tx.type === 'deposit');\r\n      const withdrawalsData = transactionsData.filter(tx => tx.type === 'withdrawal');\r\n\r\n      // Calculate summary metrics\r\n      const totalDeposits = depositsData.reduce((sum, tx) => sum + parseFloat(tx.amount as string), 0);\r\n      const totalWithdrawals = withdrawalsData.reduce((sum, tx) => sum + parseFloat(tx.amount as string), 0);\r\n      const totalTransactions = transactionsData.length;\r\n      const netCashflow = totalDeposits - totalWithdrawals;\r\n\r\n      // Process data for charts - group by date\r\n      const depositsByDate = new Map<string, number>();\r\n      const withdrawalsByDate = new Map<string, number>();\r\n\r\n      transactionsData.forEach((tx) => {\r\n        const date = format(parseISO(tx.transaction_date), \"yyyy-MM-dd\");\r\n        const amount = parseFloat(tx.amount as string);\r\n\r\n        if (tx.type === 'deposit') {\r\n          const existingAmount = depositsByDate.get(date) || 0;\r\n          depositsByDate.set(date, existingAmount + amount);\r\n        } else if (tx.type === 'withdrawal') {\r\n          const existingAmount = withdrawalsByDate.get(date) || 0;\r\n          withdrawalsByDate.set(date, existingAmount + amount);\r\n        }\r\n      });\r\n\r\n      // Sort dates\r\n      const allDates = new Set([\r\n        ...Array.from(depositsByDate.keys()),\r\n        ...Array.from(withdrawalsByDate.keys())\r\n      ]);\r\n      const sortedDates = Array.from(allDates).sort();\r\n\r\n      // Create chart data\r\n      const chartData = {\r\n        labels: sortedDates.map((date) => format(parseISO(date), \"MMM dd\")),\r\n        datasets: [\r\n          {\r\n            label: \"Deposits\",\r\n            data: sortedDates.map((date) => depositsByDate.get(date) || 0),\r\n            backgroundColor: \"rgba(16, 185, 129, 0.5)\",\r\n            borderColor: \"rgb(16, 185, 129)\",\r\n            borderWidth: 2,\r\n          },\r\n          {\r\n            label: \"Withdrawals\",\r\n            data: sortedDates.map((date) => withdrawalsByDate.get(date) || 0),\r\n            backgroundColor: \"rgba(239, 68, 68, 0.5)\",\r\n            borderColor: \"rgb(239, 68, 68)\",\r\n            borderWidth: 2,\r\n          }\r\n        ],\r\n      };\r\n\r\n      // Process data for summary table\r\n      const summaryData = sortedDates.map(date => {\r\n        const formattedDate = format(parseISO(date), \"MMM dd, yyyy\");\r\n        const deposits = depositsByDate.get(date) || 0;\r\n        const withdrawals = withdrawalsByDate.get(date) || 0;\r\n        const net = deposits - withdrawals;\r\n\r\n        return {\r\n          date: formattedDate,\r\n          rawDate: date,\r\n          deposits,\r\n          withdrawals,\r\n          net\r\n        };\r\n      });\r\n\r\n      // Sort by date descending (newest first)\r\n      summaryData.sort((a, b) => new Date(b.rawDate).getTime() - new Date(a.rawDate).getTime());\r\n\r\n      // Process data for branch summary\r\n      const branchMap = new Map<number, {\r\n        branchId: number;\r\n        branchName: string;\r\n        deposits: number;\r\n        withdrawals: number;\r\n        net: number;\r\n        transactionCount: number;\r\n      }>();\r\n\r\n      transactionsData.forEach(tx => {\r\n        if (!tx.Branch) return;\r\n\r\n        const branchId = tx.branch_id;\r\n        const branchName = tx.Branch.name;\r\n        const amount = parseFloat(tx.amount as string);\r\n\r\n        const existingBranch = branchMap.get(branchId);\r\n        if (existingBranch) {\r\n          if (tx.type === 'deposit') {\r\n            existingBranch.deposits += amount;\r\n          } else if (tx.type === 'withdrawal') {\r\n            existingBranch.withdrawals += amount;\r\n          }\r\n          existingBranch.transactionCount += 1;\r\n          existingBranch.net = existingBranch.deposits - existingBranch.withdrawals;\r\n        } else {\r\n          branchMap.set(branchId, {\r\n            branchId,\r\n            branchName,\r\n            deposits: tx.type === 'deposit' ? amount : 0,\r\n            withdrawals: tx.type === 'withdrawal' ? amount : 0,\r\n            net: tx.type === 'deposit' ? amount : -amount,\r\n            transactionCount: 1\r\n          });\r\n        }\r\n      });\r\n\r\n      const branchData = Array.from(branchMap.values());\r\n\r\n      // Sort by total transaction amount descending\r\n      branchData.sort((a, b) => (b.deposits + b.withdrawals) - (a.deposits + a.withdrawals));\r\n\r\n      return {\r\n        transactionsData,\r\n        depositsData,\r\n        withdrawalsData,\r\n        summaryData,\r\n        branchData,\r\n        chartData,\r\n        metrics: {\r\n          totalDeposits,\r\n          totalWithdrawals,\r\n          totalTransactions,\r\n          netCashflow\r\n        }\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Process items data into chart data\r\n */\r\nfunction processItemChartData(itemsData: SalesByItemData[]): ChartData {\r\n  // Take top 10 items\r\n  const topItems = itemsData.slice(0, 10);\r\n\r\n  return {\r\n    labels: topItems.map((item) => item.productName),\r\n    datasets: [\r\n      {\r\n        label: \"Sales\",\r\n        data: topItems.map((item) => item.totalSales),\r\n        backgroundColor: \"rgba(16, 185, 129, 0.5)\",\r\n        borderColor: \"rgb(16, 185, 129)\",\r\n        borderWidth: 2,\r\n      },\r\n    ],\r\n  };\r\n}\r\n\r\n/**\r\n * Process category data into chart data\r\n */\r\nfunction processCategoryChartData(\r\n  categoryData: SalesByCategoryData[]\r\n): ChartData {\r\n  // Take top 10 categories\r\n  const topCategories = categoryData.slice(0, 10);\r\n\r\n  return {\r\n    labels: topCategories.map((category) => category.categoryName),\r\n    datasets: [\r\n      {\r\n        label: \"Sales\",\r\n        data: topCategories.map((category) => category.totalSales),\r\n        backgroundColor: [\r\n          \"rgba(59, 130, 246, 0.5)\",\r\n          \"rgba(16, 185, 129, 0.5)\",\r\n          \"rgba(245, 158, 11, 0.5)\",\r\n          \"rgba(239, 68, 68, 0.5)\",\r\n          \"rgba(139, 92, 246, 0.5)\",\r\n          \"rgba(236, 72, 153, 0.5)\",\r\n          \"rgba(6, 182, 212, 0.5)\",\r\n          \"rgba(168, 85, 247, 0.5)\",\r\n          \"rgba(234, 179, 8, 0.5)\",\r\n          \"rgba(249, 115, 22, 0.5)\",\r\n        ],\r\n        borderColor: [\r\n          \"rgb(59, 130, 246)\",\r\n          \"rgb(16, 185, 129)\",\r\n          \"rgb(245, 158, 11)\",\r\n          \"rgb(239, 68, 68)\",\r\n          \"rgb(139, 92, 246)\",\r\n          \"rgb(236, 72, 153)\",\r\n          \"rgb(6, 182, 212)\",\r\n          \"rgb(168, 85, 247)\",\r\n          \"rgb(234, 179, 8)\",\r\n          \"rgb(249, 115, 22)\",\r\n        ],\r\n        borderWidth: 2,\r\n      },\r\n    ],\r\n  };\r\n}\r\n\r\n/**\r\n * Hook to fetch tax report data\r\n */\r\nexport const useTaxReport = (filters?: ReportFilterParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"tax-report\", filters],\r\n    queryFn: async () => {\r\n      // Create a copy of filters without employee_id for the API call\r\n      const { /* employee_id, */ ...apiFilters } = filters || {};\r\n\r\n      // Fetch sales data with region filter\r\n      const salesData = await reportsService.getTaxReport(apiFilters);\r\n\r\n      // Process tax data\r\n      const vatRateMap = new Map<number, TaxReportData>();\r\n      let totalVatAmount = 0;\r\n      let totalTaxableSales = 0;\r\n      let totalNonTaxableSales = 0;\r\n      let totalTransactions = 0;\r\n\r\n      // Group sales by date for chart data\r\n      const vatByDate = new Map<string, number>();\r\n      const taxableSalesByDate = new Map<string, number>();\r\n\r\n      salesData.forEach((sale) => {\r\n        const saleAmount = parseFloat(sale.total_amount);\r\n        const vatAmount = parseFloat(sale.total_vat_amount || \"0\");\r\n        const taxableAmount = parseFloat(sale.total_excluding_vat || \"0\");\r\n        const date = format(parseISO(sale.created_at), \"yyyy-MM-dd\");\r\n\r\n        // Add to date maps for charts\r\n        const existingVatAmount = vatByDate.get(date) || 0;\r\n        vatByDate.set(date, existingVatAmount + vatAmount);\r\n\r\n        const existingTaxableAmount = taxableSalesByDate.get(date) || 0;\r\n        taxableSalesByDate.set(date, existingTaxableAmount + taxableAmount);\r\n\r\n        // Update totals\r\n        totalVatAmount += vatAmount;\r\n\r\n        if (vatAmount > 0) {\r\n          totalTaxableSales += taxableAmount;\r\n          totalTransactions += 1;\r\n        } else {\r\n          totalNonTaxableSales += saleAmount;\r\n        }\r\n\r\n        // Process sale items to get VAT rates\r\n        if (sale.SaleItems) {\r\n          sale.SaleItems.forEach((item) => {\r\n            const vatRate = parseFloat(item.vat_rate || \"0\");\r\n            if (vatRate <= 0) return; // Skip non-taxable items\r\n\r\n            const itemVatAmount = parseFloat(item.vat_amount || \"0\");\r\n            const itemTaxableAmount = parseFloat(item.price_excluding_vat || \"0\") * item.quantity;\r\n\r\n            // Get VAT name from product if available\r\n            const vatName = item.Product?.VatRate?.name || `${vatRate}% VAT`;\r\n\r\n            const existingVatData = vatRateMap.get(vatRate);\r\n            if (existingVatData) {\r\n              existingVatData.vatAmount += itemVatAmount;\r\n              existingVatData.taxableSales += itemTaxableAmount;\r\n              existingVatData.transactionCount += 1;\r\n            } else {\r\n              vatRateMap.set(vatRate, {\r\n                vatRate,\r\n                vatName,\r\n                vatAmount: itemVatAmount,\r\n                taxableSales: itemTaxableAmount,\r\n                transactionCount: 1,\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n\r\n      // Convert map to array and sort by VAT rate\r\n      const taxData = Array.from(vatRateMap.values());\r\n      taxData.sort((a, b) => b.vatRate - a.vatRate);\r\n\r\n      // Process chart data\r\n      const sortedDates = Array.from(vatByDate.keys()).sort();\r\n\r\n      const vatChartData: ChartData = {\r\n        labels: sortedDates.map((date) => format(parseISO(date), \"MMM dd\")),\r\n        datasets: [\r\n          {\r\n            label: \"VAT Collected\",\r\n            data: sortedDates.map((date) => vatByDate.get(date) || 0),\r\n            backgroundColor: \"rgba(239, 68, 68, 0.5)\",\r\n            borderColor: \"rgb(239, 68, 68)\",\r\n            borderWidth: 2,\r\n          },\r\n        ],\r\n      };\r\n\r\n      const taxableChartData: ChartData = {\r\n        labels: sortedDates.map((date) => format(parseISO(date), \"MMM dd\")),\r\n        datasets: [\r\n          {\r\n            label: \"Taxable Sales\",\r\n            data: sortedDates.map((date) => taxableSalesByDate.get(date) || 0),\r\n            backgroundColor: \"rgba(16, 185, 129, 0.5)\",\r\n            borderColor: \"rgb(16, 185, 129)\",\r\n            borderWidth: 2,\r\n          },\r\n        ],\r\n      };\r\n\r\n      return {\r\n        salesData,\r\n        taxData,\r\n        summaryData: {\r\n          totalVatAmount,\r\n          totalTaxableSales,\r\n          totalNonTaxableSales,\r\n          totalTransactions,\r\n        },\r\n        vatChartData,\r\n        taxableChartData,\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Process employee data into chart data\r\n */\r\nfunction processEmployeeChartData(\r\n  employeeData: SalesByEmployeeData[]\r\n): ChartData {\r\n  return {\r\n    labels: employeeData.map((employee) => employee.userName),\r\n    datasets: [\r\n      {\r\n        label: \"Sales\",\r\n        data: employeeData.map((employee) => employee.totalSales),\r\n        backgroundColor: \"rgba(245, 158, 11, 0.5)\",\r\n        borderColor: \"rgb(245, 158, 11)\",\r\n        borderWidth: 2,\r\n      },\r\n    ],\r\n  };\r\n}\r\n\r\n/**\r\n * Process payment type data into chart data\r\n */\r\nfunction processPaymentChartData(\r\n  paymentData: SalesByPaymentTypeData[]\r\n): ChartData {\r\n  return {\r\n    labels: paymentData.map((payment) => payment.paymentMethodName),\r\n    datasets: [\r\n      {\r\n        label: \"Sales\",\r\n        data: paymentData.map((payment) => payment.totalSales),\r\n        backgroundColor: [\r\n          \"rgba(59, 130, 246, 0.5)\",\r\n          \"rgba(16, 185, 129, 0.5)\",\r\n          \"rgba(245, 158, 11, 0.5)\",\r\n          \"rgba(239, 68, 68, 0.5)\",\r\n          \"rgba(139, 92, 246, 0.5)\",\r\n        ],\r\n        borderColor: [\r\n          \"rgb(59, 130, 246)\",\r\n          \"rgb(16, 185, 129)\",\r\n          \"rgb(245, 158, 11)\",\r\n          \"rgb(239, 68, 68)\",\r\n          \"rgb(139, 92, 246)\",\r\n        ],\r\n        borderWidth: 2,\r\n      },\r\n    ],\r\n  };\r\n}\r\n\r\n/**\r\n * Hook to fetch Mpesa banking report data\r\n */\r\nexport const useMpesaBankingReport = (filters?: ReportFilterParams) => {\r\n  // Convert ReportFilterParams to BankingSummaryFilters\r\n  const bankingFilters: BankingSummaryFilters & { banking_method?: string } = {\r\n    branch_id: filters?.branch_id, // Make branch_id optional\r\n    start_date: filters?.start_date || format(new Date(), \"yyyy-MM-dd\"),\r\n    end_date: filters?.end_date,\r\n    banking_method: filters?.payment_method_id\r\n      ? String(filters.payment_method_id)\r\n      : undefined, // Use payment_method_id as banking_method if provided\r\n  };\r\n\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"mpesa-banking\", filters],\r\n    queryFn: async () => {\r\n      // Use the regular banking endpoint if branch_id is not provided\r\n      let summaryData: BankingSummary[];\r\n      if (bankingFilters.branch_id && bankingFilters.branch_id > 0) {\r\n        summaryData = await bankingService.getBankingSummary(bankingFilters);\r\n      } else {\r\n        // Convert to BankingFilters\r\n        const regularFilters: BankingFilters = {\r\n          branch_id: bankingFilters.branch_id,\r\n          start_date: bankingFilters.start_date,\r\n          end_date: bankingFilters.end_date,\r\n          banking_method: bankingFilters.banking_method as any,\r\n        };\r\n        summaryData = await bankingService.getBankingRecordsAsSummary(\r\n          regularFilters\r\n        );\r\n      }\r\n\r\n      // Process data for charts\r\n      const chartData = processMpesaBankingChartData(summaryData);\r\n\r\n      // Calculate totals and metrics\r\n      const totalBank = summaryData.reduce(\r\n        (sum: number, item: BankingSummary) => sum + item.bank,\r\n        0\r\n      );\r\n      const totalMpesa = summaryData.reduce(\r\n        (sum: number, item: BankingSummary) => sum + item.mpesa,\r\n        0\r\n      );\r\n      const totalAgent = summaryData.reduce(\r\n        (sum: number, item: BankingSummary) => sum + item.agent,\r\n        0\r\n      );\r\n      const totalAmount = summaryData.reduce(\r\n        (sum: number, item: BankingSummary) => sum + item.total,\r\n        0\r\n      );\r\n      const totalTransactions = summaryData.reduce(\r\n        (sum: number, item: BankingSummary) => sum + item.transaction_count,\r\n        0\r\n      );\r\n\r\n      return {\r\n        summaryData,\r\n        chartData,\r\n        metrics: {\r\n          totalBank,\r\n          totalMpesa,\r\n          totalAgent,\r\n          totalAmount,\r\n          totalTransactions,\r\n          averageTransaction:\r\n            totalTransactions > 0 ? totalAmount / totalTransactions : 0,\r\n        },\r\n      };\r\n    },\r\n    enabled: !!bankingFilters.start_date, // Only require start_date\r\n  });\r\n};\r\n\r\n/**\r\n * Process chart data for Mpesa banking\r\n */\r\nfunction processMpesaBankingChartData(\r\n  summaryData: BankingSummary[]\r\n): ChartData {\r\n  return {\r\n    labels: summaryData.map((item) => format(new Date(item.date), \"MMM dd\")),\r\n    datasets: [\r\n      {\r\n        label: \"Bank\",\r\n        data: summaryData.map((item) => item.bank),\r\n        backgroundColor: \"rgba(59, 130, 246, 0.5)\",\r\n        borderColor: \"rgb(59, 130, 246)\",\r\n        borderWidth: 2,\r\n      },\r\n      {\r\n        label: \"M-Pesa\",\r\n        data: summaryData.map((item) => item.mpesa),\r\n        backgroundColor: \"rgba(16, 185, 129, 0.5)\",\r\n        borderColor: \"rgb(16, 185, 129)\",\r\n        borderWidth: 2,\r\n      },\r\n      {\r\n        label: \"Agent\",\r\n        data: summaryData.map((item) => item.agent),\r\n        backgroundColor: \"rgba(245, 158, 11, 0.5)\",\r\n        borderColor: \"rgb(245, 158, 11)\",\r\n        borderWidth: 2,\r\n      },\r\n    ],\r\n  };\r\n}\r\n\r\n/**\r\n * Hook to fetch DSA sales report data\r\n */\r\nexport const useDsaSalesReport = (filters?: ReportFilterParams) => {\r\n  // Ensure is_dsa is set to true\r\n  const dsaFilters: ReportFilterParams = {\r\n    ...filters,\r\n    is_dsa: true,\r\n  };\r\n\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"dsa-sales\", dsaFilters],\r\n    queryFn: async () => {\r\n      const salesData = await reportsService.getSalesReport(dsaFilters);\r\n      const summaryData = processSalesSummary(salesData);\r\n\r\n      // Process data for charts\r\n      const chartData = processChartData(salesData);\r\n\r\n      // Process data for payment methods\r\n      const paymentMethodsMap = new Map<number, SalesByPaymentTypeData>();\r\n      salesData.forEach((sale) => {\r\n        if (sale.PaymentMethod) {\r\n          const methodId = sale.PaymentMethod.id;\r\n          const amount = parseFloat(sale.total_amount);\r\n\r\n          if (!paymentMethodsMap.has(methodId)) {\r\n            paymentMethodsMap.set(methodId, {\r\n              paymentMethodId: methodId,\r\n              paymentMethodName: sale.PaymentMethod.name,\r\n              totalSales: 0,\r\n              totalTransactions: 0,\r\n              averageSale: 0,\r\n              percentage: 0,\r\n            });\r\n          }\r\n\r\n          const method = paymentMethodsMap.get(methodId)!;\r\n          method.totalSales += amount;\r\n          method.totalTransactions += 1;\r\n        }\r\n      });\r\n\r\n      const paymentData = Array.from(paymentMethodsMap.values());\r\n\r\n      // Calculate total sales for percentage calculation\r\n      const totalSalesAmount = paymentData.reduce(\r\n        (sum, method) => sum + method.totalSales,\r\n        0\r\n      );\r\n\r\n      // Calculate average sale and percentage for each payment method\r\n      paymentData.forEach((method) => {\r\n        method.averageSale =\r\n          method.totalTransactions > 0\r\n            ? method.totalSales / method.totalTransactions\r\n            : 0;\r\n        method.percentage =\r\n          totalSalesAmount > 0\r\n            ? (method.totalSales / totalSalesAmount) * 100\r\n            : 0;\r\n      });\r\n\r\n      paymentData.sort((a, b) => b.totalSales - a.totalSales);\r\n\r\n      // Process data for agents\r\n      const agentsMap = new Map<number, SalesByEmployeeData>();\r\n      salesData.forEach((sale) => {\r\n        if (sale.User) {\r\n          const userId = sale.User.id;\r\n          const amount = parseFloat(sale.total_amount);\r\n\r\n          if (!agentsMap.has(userId)) {\r\n            agentsMap.set(userId, {\r\n              userId,\r\n              userName: sale.User.name,\r\n              totalSales: 0,\r\n              totalTransactions: 0,\r\n              averageSale: 0,\r\n              profit: 0,\r\n            });\r\n          }\r\n\r\n          const agent = agentsMap.get(userId)!;\r\n          agent.totalSales += amount;\r\n          agent.totalTransactions += 1;\r\n        }\r\n      });\r\n\r\n      const agentData = Array.from(agentsMap.values());\r\n\r\n      // Calculate average sale for each agent\r\n      agentData.forEach((agent) => {\r\n        agent.averageSale =\r\n          agent.totalTransactions > 0\r\n            ? agent.totalSales / agent.totalTransactions\r\n            : 0;\r\n        // For simplicity, we're setting profit to 30% of sales as we don't have item-level data here\r\n        agent.profit = agent.totalSales * 0.3;\r\n      });\r\n\r\n      agentData.sort((a, b) => b.totalSales - a.totalSales);\r\n\r\n      // Process payment method chart data\r\n      const paymentChartData = processPaymentChartData(paymentData);\r\n\r\n      return {\r\n        salesData,\r\n        summaryData,\r\n        chartData,\r\n        paymentData,\r\n        agentData,\r\n        paymentChartData,\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch phone repairs report data\r\n */\r\nexport const usePhoneRepairsReport = (filters?: PhoneRepairFilters) => {\r\n  return useQuery({\r\n    queryKey: [\"reports\", \"phone-repairs\", filters],\r\n    queryFn: async () => {\r\n      const repairsData = await reportsService.getPhoneRepairsReport(filters);\r\n\r\n      // Process data for charts\r\n      const chartData = processPhoneRepairsChartData(repairsData);\r\n\r\n      // Process data for status breakdown\r\n      const statusData = processPhoneRepairsStatusData(repairsData);\r\n\r\n      return {\r\n        repairsData,\r\n        chartData,\r\n        statusData,\r\n      };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Process phone repairs data for charts\r\n */\r\nfunction processPhoneRepairsChartData(repairs: PhoneRepair[]): ChartData {\r\n  // Count repairs by status\r\n  const statusCounts: Record<string, number> = {};\r\n  repairs.forEach((repair) => {\r\n    const status = repair.status;\r\n    statusCounts[status] = (statusCounts[status] || 0) + 1;\r\n  });\r\n\r\n  // Count repairs by branch\r\n  const branchCounts: Record<string, number> = {};\r\n  repairs.forEach((repair) => {\r\n    const branchName = repair.Branch?.name || \"Unknown\";\r\n    branchCounts[branchName] = (branchCounts[branchName] || 0) + 1;\r\n  });\r\n\r\n  // Create chart data\r\n  return {\r\n    labels: Object.keys(statusCounts),\r\n    datasets: [\r\n      {\r\n        label: \"Repairs by Status\",\r\n        data: Object.values(statusCounts),\r\n        backgroundColor: [\r\n          \"rgba(59, 130, 246, 0.5)\",\r\n          \"rgba(16, 185, 129, 0.5)\",\r\n          \"rgba(245, 158, 11, 0.5)\",\r\n          \"rgba(239, 68, 68, 0.5)\",\r\n          \"rgba(139, 92, 246, 0.5)\",\r\n        ],\r\n        borderColor: [\r\n          \"rgb(59, 130, 246)\",\r\n          \"rgb(16, 185, 129)\",\r\n          \"rgb(245, 158, 11)\",\r\n          \"rgb(239, 68, 68)\",\r\n          \"rgb(139, 92, 246)\",\r\n        ],\r\n        borderWidth: 2,\r\n      },\r\n    ],\r\n    secondaryData: {\r\n      labels: Object.keys(branchCounts),\r\n      datasets: [\r\n        {\r\n          label: \"Repairs by Branch\",\r\n          data: Object.values(branchCounts),\r\n          backgroundColor: [\r\n            \"rgba(59, 130, 246, 0.5)\",\r\n            \"rgba(16, 185, 129, 0.5)\",\r\n            \"rgba(245, 158, 11, 0.5)\",\r\n            \"rgba(239, 68, 68, 0.5)\",\r\n            \"rgba(139, 92, 246, 0.5)\",\r\n          ],\r\n          borderColor: [\r\n            \"rgb(59, 130, 246)\",\r\n            \"rgb(16, 185, 129)\",\r\n            \"rgb(245, 158, 11)\",\r\n            \"rgb(239, 68, 68)\",\r\n            \"rgb(139, 92, 246)\",\r\n          ],\r\n          borderWidth: 2,\r\n        },\r\n      ],\r\n    },\r\n  };\r\n}\r\n\r\n/**\r\n * Process phone repairs data for status breakdown\r\n */\r\nfunction processPhoneRepairsStatusData(repairs: PhoneRepair[]) {\r\n  // Group repairs by status\r\n  const statusGroups: Record<string, PhoneRepair[]> = {};\r\n  repairs.forEach((repair) => {\r\n    const status = repair.status;\r\n    if (!statusGroups[status]) {\r\n      statusGroups[status] = [];\r\n    }\r\n    statusGroups[status].push(repair);\r\n  });\r\n\r\n  // Calculate totals and averages\r\n  const statusData = Object.entries(statusGroups).map(([status, repairs]) => {\r\n    const count = repairs.length;\r\n\r\n    // Calculate average estimated cost\r\n    const totalEstimatedCost = repairs.reduce(\r\n      (sum, repair) => sum + parseFloat(repair.estimated_cost || \"0\"),\r\n      0\r\n    );\r\n    const avgEstimatedCost = count > 0 ? totalEstimatedCost / count : 0;\r\n\r\n    // Calculate average actual cost for completed repairs\r\n    const completedRepairs = repairs.filter((r) => r.amount_charged);\r\n    const totalActualCost = completedRepairs.reduce(\r\n      (sum, repair) => sum + parseFloat(repair.amount_charged || \"0\"),\r\n      0\r\n    );\r\n    const avgActualCost =\r\n      completedRepairs.length > 0\r\n        ? totalActualCost / completedRepairs.length\r\n        : 0;\r\n\r\n    return {\r\n      status,\r\n      count,\r\n      totalEstimatedCost,\r\n      avgEstimatedCost,\r\n      totalActualCost,\r\n      avgActualCost,\r\n      completedCount: completedRepairs.length,\r\n    };\r\n  });\r\n\r\n  return statusData;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAiBA;AACA;AAAA;AACA;AACA;;;;;;;AAMO,MAAM,wBAAwB,CAAC;;IACpC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAiB;SAAQ;QAC/C,OAAO;8CAAE;gBACP,gEAAgE;gBAChE,iFAAiF;gBACjF,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,GAAG,WAAW,CAAC;gBAEnD,mEAAmE;gBACnE,MAAM,YAAY,MAAM,0JAAA,CAAA,UAAc,CAAC,cAAc,CAAC;gBAEtD,oCAAoC;gBACpC,IAAI,oBAAoB;gBAExB,6EAA6E;gBAC7E,IAAI,WAAW,iBAAiB,EAAE;oBAChC,oBAAoB,kBAAkB,MAAM;0DAC1C,CAAC,OAAS,KAAK,iBAAiB,KAAK,WAAW,iBAAiB;;gBAErE;gBAEA,oCAAoC;gBACpC,IAAI,aAAa;oBACf,oBAAoB,kBAAkB,MAAM;0DAC1C,CAAC,OAAS,KAAK,WAAW,KAAK,eAAe,KAAK,gBAAgB,KAAK;;gBAE5E;gBAEA,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;gBAExC,0BAA0B;gBAC1B,MAAM,YAAY,iBAAiB;gBAEnC,OAAO;oBACL,WAAW;oBACX;oBACA;gBACF;YACF;;IACF;AACF;GAxCa;;QACJ,8KAAA,CAAA,WAAQ;;;AA4CV,MAAM,uBAAuB,CAAC;;IACnC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAiB;SAAQ;QAC/C,OAAO;6CAAE;gBACP,gEAAgE;gBAChE,iFAAiF;gBACjF,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,GAAG,WAAW,CAAC;gBAEnD,mEAAmE;gBACnE,MAAM,YAAY,MAAM,0JAAA,CAAA,UAAc,CAAC,oBAAoB,CAAC;gBAE5D,8CAA8C;gBAC9C,IAAI,oBAAoB;gBAExB,IAAI,aAAa;oBACf,oBAAoB,kBAAkB,MAAM;yDAC1C,CAAC,OAAS,KAAK,WAAW,KAAK,eAAe,KAAK,gBAAgB,KAAK;;gBAE5E;gBAEA,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;gBAErC,iCAAiC;gBACjC,UAAU,IAAI;qDAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;;gBAEpD,0BAA0B;gBAC1B,MAAM,YAAY,qBAAqB;gBAEvC,OAAO;oBACL,WAAW;oBACX;oBACA;gBACF;YACF;;IACF;AACF;IAnCa;;QACJ,8KAAA,CAAA,WAAQ;;;AAuCV,MAAM,2BAA2B,CAAC;;IACvC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAqB;SAAQ;QACnD,OAAO;iDAAE;gBACP,gEAAgE;gBAChE,iFAAiF;gBACjF,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,GAAG,WAAW,CAAC;gBAEnD,mEAAmE;gBACnE,MAAM,YAAY,MAAM,0JAAA,CAAA,UAAc,CAAC,wBAAwB,CAAC;gBAEhE,wBAAwB;gBACxB,MAAM,cAAc,IAAI;gBACxB,IAAI,aAAa;gBAEjB,UAAU,OAAO;yDAAC,CAAC;wBACjB,IAAI,CAAC,KAAK,SAAS,EAAE;wBAErB,KAAK,SAAS,CAAC,OAAO;iEAAC,CAAC;gCACtB,IAAI,CAAC,KAAK,OAAO,EAAE;gCAEnB,mDAAmD;gCACnD,oEAAoE;gCACpE,MAAM,aAAa,KAAK,OAAO,CAAC,WAAW,IAAI;gCAC/C,iDAAiD;gCACjD,MAAM,eAAe,KAAK,OAAO,CAAC,eAAe,EAAE,QAAQ;gCAC3D,MAAM,aAAa,WAAW,KAAK,WAAW;gCAC9C,MAAM,OAAO,WAAW,KAAK,YAAY,IAAI,KAAK,QAAQ;gCAC1D,MAAM,SAAS,aAAa;gCAE5B,cAAc;gCAEd,MAAM,mBAAmB,YAAY,GAAG,CAAC;gCACzC,IAAI,kBAAkB;oCACpB,iBAAiB,QAAQ,IAAI,KAAK,QAAQ;oCAC1C,iBAAiB,UAAU,IAAI;oCAC/B,iBAAiB,MAAM,IAAI;gCAC7B,OAAO;oCACL,YAAY,GAAG,CAAC,YAAY;wCAC1B;wCACA;wCACA,UAAU,KAAK,QAAQ;wCACvB,YAAY;wCACZ;wCACA,YAAY;oCACd;gCACF;4BACF;;oBACF;;gBAEA,wBAAwB;gBACxB,MAAM,eAAe,MAAM,IAAI,CAAC,YAAY,MAAM;gBAClD,aAAa,OAAO;yDAAC,CAAC;wBACpB,SAAS,UAAU,GACjB,aAAa,IAAI,AAAC,SAAS,UAAU,GAAG,aAAc,MAAM;oBAChE;;gBAEA,iCAAiC;gBACjC,aAAa,IAAI;yDAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;;gBAEvD,0BAA0B;gBAC1B,MAAM,YAAY,yBAAyB;gBAE3C,OAAO;oBACL;oBACA;oBACA;gBACF;YACF;;IACF;AACF;IAtEa;;QACJ,8KAAA,CAAA,WAAQ;;;AA0EV,MAAM,2BAA2B,CAAC;;IACvC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAqB;SAAQ;QACnD,OAAO;iDAAE;gBACP,gEAAgE;gBAChE,iFAAiF;gBACjF,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,GAAG,WAAW,CAAC;gBAEnD,mEAAmE;gBACnE,MAAM,YAAY,MAAM,0JAAA,CAAA,UAAc,CAAC,wBAAwB,CAAC;gBAEhE,8CAA8C;gBAC9C,IAAI,oBAAoB;gBACxB,IAAI,aAAa;oBACf,oBAAoB,kBAAkB,MAAM;6DAC1C,CAAC,OAAS,KAAK,WAAW,KAAK,eAAe,KAAK,gBAAgB,KAAK,eAAe,KAAK,OAAO,KAAK;;gBAE5G;gBAEA,wBAAwB;gBACxB,MAAM,cAAc,IAAI;gBAExB,kBAAkB,OAAO;yDAAC,CAAC;wBACzB,IAAI,CAAC,KAAK,IAAI,EAAE;wBAEhB,MAAM,SAAS,KAAK,OAAO;wBAC3B,MAAM,WAAW,KAAK,IAAI,CAAC,IAAI,IAAI;wBACnC,MAAM,aAAa,WAAW,KAAK,YAAY;wBAE/C,mBAAmB;wBACnB,IAAI,OAAO;wBACX,IAAI,KAAK,SAAS,EAAE;4BAClB,OAAO,KAAK,SAAS,CAAC,MAAM;qEAC1B,CAAC,KAAK,OAAS,MAAM,WAAW,KAAK,YAAY,IAAI,KAAK,QAAQ;oEAClE;wBAEJ;wBACA,MAAM,SAAS,aAAa;wBAE5B,MAAM,mBAAmB,YAAY,GAAG,CAAC;wBACzC,IAAI,kBAAkB;4BACpB,iBAAiB,UAAU,IAAI;4BAC/B,iBAAiB,iBAAiB,IAAI;4BACtC,iBAAiB,MAAM,IAAI;wBAC7B,OAAO;4BACL,YAAY,GAAG,CAAC,QAAQ;gCACtB;gCACA;gCACA,YAAY;gCACZ,mBAAmB;gCACnB,aAAa;gCACb;4BACF;wBACF;oBACF;;gBAEA,yBAAyB;gBACzB,MAAM,eAAe,MAAM,IAAI,CAAC,YAAY,MAAM;gBAClD,aAAa,OAAO;yDAAC,CAAC;wBACpB,SAAS,WAAW,GAClB,SAAS,iBAAiB,GAAG,IACzB,SAAS,UAAU,GAAG,SAAS,iBAAiB,GAChD;oBACR;;gBAEA,iCAAiC;gBACjC,aAAa,IAAI;yDAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;;gBAEvD,0BAA0B;gBAC1B,MAAM,YAAY,yBAAyB;gBAE3C,OAAO;oBACL,WAAW;oBACX;oBACA;gBACF;YACF;;IACF;AACF;IA9Ea;;QACJ,8KAAA,CAAA,WAAQ;;;AAkFV,MAAM,8BAA8B,CAAC;;IAC1C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAyB;SAAQ;QACvD,OAAO;oDAAE;gBACP,MAAM,YAAY,MAAM,0JAAA,CAAA,UAAc,CAAC,2BAA2B,CAChE;gBAGF,4BAA4B;gBAC5B,MAAM,aAAa,IAAI;gBACvB,IAAI,aAAa;gBAEjB,UAAU,OAAO;4DAAC,CAAC;wBACjB,IAAI,CAAC,KAAK,aAAa,EAAE;wBAEzB,MAAM,kBAAkB,KAAK,iBAAiB;wBAC9C,MAAM,oBAAoB,KAAK,aAAa,CAAC,IAAI,IAAI;wBACrD,MAAM,aAAa,WAAW,KAAK,YAAY;wBAE/C,cAAc;wBAEd,MAAM,kBAAkB,WAAW,GAAG,CAAC;wBACvC,IAAI,iBAAiB;4BACnB,gBAAgB,UAAU,IAAI;4BAC9B,gBAAgB,iBAAiB,IAAI;wBACvC,OAAO;4BACL,WAAW,GAAG,CAAC,iBAAiB;gCAC9B;gCACA;gCACA,YAAY;gCACZ,mBAAmB;gCACnB,aAAa;gCACb,YAAY;4BACd;wBACF;oBACF;;gBAEA,yCAAyC;gBACzC,MAAM,cAAc,MAAM,IAAI,CAAC,WAAW,MAAM;gBAChD,YAAY,OAAO;4DAAC,CAAC;wBACnB,QAAQ,UAAU,GAChB,aAAa,IAAI,AAAC,QAAQ,UAAU,GAAG,aAAc,MAAM;wBAC7D,QAAQ,WAAW,GACjB,QAAQ,iBAAiB,GAAG,IACxB,QAAQ,UAAU,GAAG,QAAQ,iBAAiB,GAC9C;oBACR;;gBAEA,iCAAiC;gBACjC,YAAY,IAAI;4DAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;;gBAEtD,0BAA0B;gBAC1B,MAAM,YAAY,wBAAwB;gBAE1C,OAAO;oBACL;oBACA;oBACA;gBACF;YACF;;IACF;AACF;IA7Da;;QACJ,8KAAA,CAAA,WAAQ;;;AAiEV,MAAM,kBAAkB,CAAC;;IAC9B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAU;SAAQ;QACxC,OAAO;wCAAE;gBACP,MAAM,aAAa,MAAM,0JAAA,CAAA,UAAc,CAAC,eAAe,CAAC;gBAExD,sBAAsB;gBACtB,MAAM,kBAAkB,WAAW,GAAG;gEAAC,CAAC;wBACtC,0EAA0E;wBAC1E,oFAAoF;wBACpF,IAAI,aAAa;wBACjB,IAAI,oBAAoB;wBAExB,IAAI,MAAM,WAAW,KAAK,aAAa,MAAM,iBAAiB,KAAK,WAAW;4BAC5E,yCAAyC;4BACzC,aAAa,WAAW,MAAM,WAAW;4BACzC,oBAAoB,SAAS,MAAM,iBAAiB;4BACpD,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,MAAM,EAAE,CAAC,QAAQ,EAAE,WAAW,eAAe,EAAE,mBAAmB;wBAClH,OAAO,IAAI,MAAM,KAAK,EAAE;4BACtB,4CAA4C;4BAC5C,aAAa,MAAM,KAAK,CAAC,MAAM;4EAC7B,CAAC,KAAa,OAAc,MAAM,WAAW,KAAK,YAAY;2EAC9D;4BAEF,oBAAoB,MAAM,KAAK,CAAC,MAAM;4BACtC,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,MAAM,EAAE,CAAC,QAAQ,EAAE,WAAW,eAAe,EAAE,mBAAmB;wBAC/G;wBAEA,yBAAyB;wBACzB,MAAM,WAAW,MAAM,QAAQ,GAC3B,MAAM,QAAQ,CAAC,MAAM;wEAAC,CAAC,KAAa;gCAClC,OAAO,QAAQ,MAAM,KAAK,aACtB,MAAM,WAAW,QAAQ,MAAM,IAC/B;4BACN;uEAAG,KACH;wBACJ,OAAO;4BACL,IAAI,MAAM,EAAE;4BACZ,WAAW,MAAM,UAAU;4BAC3B,SAAS,MAAM,QAAQ;4BACvB,QAAQ,MAAM,MAAM;4BACpB,gBAAgB,MAAM,eAAe,GACjC,WAAW,MAAM,eAAe,IAChC;4BACJ,gBAAgB,MAAM,eAAe,GACjC,WAAW,MAAM,eAAe,IAChC;4BACJ;4BACA;4BACA,YAAY,MAAM,YAAY,GAC1B,WAAW,MAAM,YAAY,IAC7B;4BACJ,aAAa,MAAM,aAAa,GAC5B,WAAW,MAAM,aAAa,IAC9B;4BACJ,eAAe,MAAM,aAAa,GAC9B,WAAW,MAAM,aAAa,IAC9B;4BACJ,QAAQ,MAAM,OAAO;4BACrB,UAAU,MAAM,IAAI,EAAE,QAAQ;4BAC9B,UAAU,MAAM,SAAS;4BACzB,YAAY,MAAM,MAAM,EAAE,QAAQ;4BAClC,gBAAgB,MAAM,cAAc,IAAI;4BACxC;wBAEF;oBACF;;gBAEA,gCAAgC;gBAChC,gBAAgB,IAAI;gDAClB,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;;gBAGnE,OAAO;oBACL,YAAY;gBACd;YACF;;IACF;AACF;IA/Ea;;QACJ,8KAAA,CAAA,WAAQ;;;AAgFjB;;CAEC,GACD,SAAS,iBAAiB,SAAiB;IACzC,sBAAsB;IACtB,MAAM,cAAc,IAAI;IAExB,UAAU,OAAO,CAAC,CAAC;QACjB,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,UAAU,GAAG;QAC/C,MAAM,SAAS,WAAW,KAAK,YAAY;QAE3C,MAAM,iBAAiB,YAAY,GAAG,CAAC,SAAS;QAChD,YAAY,GAAG,CAAC,MAAM,iBAAiB;IACzC;IAEA,aAAa;IACb,MAAM,cAAc,MAAM,IAAI,CAAC,YAAY,IAAI,IAAI,IAAI;IAEvD,oBAAoB;IACpB,OAAO;QACL,QAAQ,YAAY,GAAG,CAAC,CAAC,OAAS,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QACzD,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,YAAY,GAAG,CAAC,CAAC,OAAS,YAAY,GAAG,CAAC,SAAS;gBACzD,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;AACF;AAKO,MAAM,6BAA6B,CAAC;;IACzC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAsB;SAAQ;QACpD,OAAO;mDAAE;gBACP,MAAM,mBAAmB,MAAM,0JAAA,CAAA,UAAc,CAAC,0BAA0B,CAAC;gBAEzE,4BAA4B;gBAC5B,MAAM,eAAe,iBAAiB,MAAM;wEAAC,CAAA,KAAM,GAAG,IAAI,KAAK;;gBAC/D,MAAM,kBAAkB,iBAAiB,MAAM;2EAAC,CAAA,KAAM,GAAG,IAAI,KAAK;;gBAElE,4BAA4B;gBAC5B,MAAM,gBAAgB,aAAa,MAAM;yEAAC,CAAC,KAAK,KAAO,MAAM,WAAW,GAAG,MAAM;wEAAa;gBAC9F,MAAM,mBAAmB,gBAAgB,MAAM;4EAAC,CAAC,KAAK,KAAO,MAAM,WAAW,GAAG,MAAM;2EAAa;gBACpG,MAAM,oBAAoB,iBAAiB,MAAM;gBACjD,MAAM,cAAc,gBAAgB;gBAEpC,0CAA0C;gBAC1C,MAAM,iBAAiB,IAAI;gBAC3B,MAAM,oBAAoB,IAAI;gBAE9B,iBAAiB,OAAO;2DAAC,CAAC;wBACxB,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,gBAAgB,GAAG;wBACnD,MAAM,SAAS,WAAW,GAAG,MAAM;wBAEnC,IAAI,GAAG,IAAI,KAAK,WAAW;4BACzB,MAAM,iBAAiB,eAAe,GAAG,CAAC,SAAS;4BACnD,eAAe,GAAG,CAAC,MAAM,iBAAiB;wBAC5C,OAAO,IAAI,GAAG,IAAI,KAAK,cAAc;4BACnC,MAAM,iBAAiB,kBAAkB,GAAG,CAAC,SAAS;4BACtD,kBAAkB,GAAG,CAAC,MAAM,iBAAiB;wBAC/C;oBACF;;gBAEA,aAAa;gBACb,MAAM,WAAW,IAAI,IAAI;uBACpB,MAAM,IAAI,CAAC,eAAe,IAAI;uBAC9B,MAAM,IAAI,CAAC,kBAAkB,IAAI;iBACrC;gBACD,MAAM,cAAc,MAAM,IAAI,CAAC,UAAU,IAAI;gBAE7C,oBAAoB;gBACpB,MAAM,YAAY;oBAChB,QAAQ,YAAY,GAAG;+DAAC,CAAC,OAAS,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;;oBACzD,UAAU;wBACR;4BACE,OAAO;4BACP,MAAM,YAAY,GAAG;uEAAC,CAAC,OAAS,eAAe,GAAG,CAAC,SAAS;;4BAC5D,iBAAiB;4BACjB,aAAa;4BACb,aAAa;wBACf;wBACA;4BACE,OAAO;4BACP,MAAM,YAAY,GAAG;uEAAC,CAAC,OAAS,kBAAkB,GAAG,CAAC,SAAS;;4BAC/D,iBAAiB;4BACjB,aAAa;4BACb,aAAa;wBACf;qBACD;gBACH;gBAEA,iCAAiC;gBACjC,MAAM,cAAc,YAAY,GAAG;uEAAC,CAAA;wBAClC,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;wBAC7C,MAAM,WAAW,eAAe,GAAG,CAAC,SAAS;wBAC7C,MAAM,cAAc,kBAAkB,GAAG,CAAC,SAAS;wBACnD,MAAM,MAAM,WAAW;wBAEvB,OAAO;4BACL,MAAM;4BACN,SAAS;4BACT;4BACA;4BACA;wBACF;oBACF;;gBAEA,yCAAyC;gBACzC,YAAY,IAAI;2DAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO;;gBAEtF,kCAAkC;gBAClC,MAAM,YAAY,IAAI;gBAStB,iBAAiB,OAAO;2DAAC,CAAA;wBACvB,IAAI,CAAC,GAAG,MAAM,EAAE;wBAEhB,MAAM,WAAW,GAAG,SAAS;wBAC7B,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI;wBACjC,MAAM,SAAS,WAAW,GAAG,MAAM;wBAEnC,MAAM,iBAAiB,UAAU,GAAG,CAAC;wBACrC,IAAI,gBAAgB;4BAClB,IAAI,GAAG,IAAI,KAAK,WAAW;gCACzB,eAAe,QAAQ,IAAI;4BAC7B,OAAO,IAAI,GAAG,IAAI,KAAK,cAAc;gCACnC,eAAe,WAAW,IAAI;4BAChC;4BACA,eAAe,gBAAgB,IAAI;4BACnC,eAAe,GAAG,GAAG,eAAe,QAAQ,GAAG,eAAe,WAAW;wBAC3E,OAAO;4BACL,UAAU,GAAG,CAAC,UAAU;gCACtB;gCACA;gCACA,UAAU,GAAG,IAAI,KAAK,YAAY,SAAS;gCAC3C,aAAa,GAAG,IAAI,KAAK,eAAe,SAAS;gCACjD,KAAK,GAAG,IAAI,KAAK,YAAY,SAAS,CAAC;gCACvC,kBAAkB;4BACpB;wBACF;oBACF;;gBAEA,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU,MAAM;gBAE9C,8CAA8C;gBAC9C,WAAW,IAAI;2DAAC,CAAC,GAAG,IAAM,AAAC,EAAE,QAAQ,GAAG,EAAE,WAAW,GAAI,CAAC,EAAE,QAAQ,GAAG,EAAE,WAAW;;gBAEpF,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA,SAAS;wBACP;wBACA;wBACA;wBACA;oBACF;gBACF;YACF;;IACF;AACF;IA3Ia;;QACJ,8KAAA,CAAA,WAAQ;;;AA4IjB;;CAEC,GACD,SAAS,qBAAqB,SAA4B;IACxD,oBAAoB;IACpB,MAAM,WAAW,UAAU,KAAK,CAAC,GAAG;IAEpC,OAAO;QACL,QAAQ,SAAS,GAAG,CAAC,CAAC,OAAS,KAAK,WAAW;QAC/C,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,SAAS,GAAG,CAAC,CAAC,OAAS,KAAK,UAAU;gBAC5C,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;AACF;AAEA;;CAEC,GACD,SAAS,yBACP,YAAmC;IAEnC,yBAAyB;IACzB,MAAM,gBAAgB,aAAa,KAAK,CAAC,GAAG;IAE5C,OAAO;QACL,QAAQ,cAAc,GAAG,CAAC,CAAC,WAAa,SAAS,YAAY;QAC7D,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,cAAc,GAAG,CAAC,CAAC,WAAa,SAAS,UAAU;gBACzD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;oBACX;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;YACf;SACD;IACH;AACF;AAKO,MAAM,eAAe,CAAC;;IAC3B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAc;SAAQ;QAC5C,OAAO;qCAAE;gBACP,gEAAgE;gBAChE,MAAM,EAAqB,GAAG,YAAY,GAAG,WAAW,CAAC;gBAEzD,sCAAsC;gBACtC,MAAM,YAAY,MAAM,0JAAA,CAAA,UAAc,CAAC,YAAY,CAAC;gBAEpD,mBAAmB;gBACnB,MAAM,aAAa,IAAI;gBACvB,IAAI,iBAAiB;gBACrB,IAAI,oBAAoB;gBACxB,IAAI,uBAAuB;gBAC3B,IAAI,oBAAoB;gBAExB,qCAAqC;gBACrC,MAAM,YAAY,IAAI;gBACtB,MAAM,qBAAqB,IAAI;gBAE/B,UAAU,OAAO;6CAAC,CAAC;wBACjB,MAAM,aAAa,WAAW,KAAK,YAAY;wBAC/C,MAAM,YAAY,WAAW,KAAK,gBAAgB,IAAI;wBACtD,MAAM,gBAAgB,WAAW,KAAK,mBAAmB,IAAI;wBAC7D,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,UAAU,GAAG;wBAE/C,8BAA8B;wBAC9B,MAAM,oBAAoB,UAAU,GAAG,CAAC,SAAS;wBACjD,UAAU,GAAG,CAAC,MAAM,oBAAoB;wBAExC,MAAM,wBAAwB,mBAAmB,GAAG,CAAC,SAAS;wBAC9D,mBAAmB,GAAG,CAAC,MAAM,wBAAwB;wBAErD,gBAAgB;wBAChB,kBAAkB;wBAElB,IAAI,YAAY,GAAG;4BACjB,qBAAqB;4BACrB,qBAAqB;wBACvB,OAAO;4BACL,wBAAwB;wBAC1B;wBAEA,sCAAsC;wBACtC,IAAI,KAAK,SAAS,EAAE;4BAClB,KAAK,SAAS,CAAC,OAAO;yDAAC,CAAC;oCACtB,MAAM,UAAU,WAAW,KAAK,QAAQ,IAAI;oCAC5C,IAAI,WAAW,GAAG,QAAQ,yBAAyB;oCAEnD,MAAM,gBAAgB,WAAW,KAAK,UAAU,IAAI;oCACpD,MAAM,oBAAoB,WAAW,KAAK,mBAAmB,IAAI,OAAO,KAAK,QAAQ;oCAErF,yCAAyC;oCACzC,MAAM,UAAU,KAAK,OAAO,EAAE,SAAS,QAAQ,GAAG,QAAQ,KAAK,CAAC;oCAEhE,MAAM,kBAAkB,WAAW,GAAG,CAAC;oCACvC,IAAI,iBAAiB;wCACnB,gBAAgB,SAAS,IAAI;wCAC7B,gBAAgB,YAAY,IAAI;wCAChC,gBAAgB,gBAAgB,IAAI;oCACtC,OAAO;wCACL,WAAW,GAAG,CAAC,SAAS;4CACtB;4CACA;4CACA,WAAW;4CACX,cAAc;4CACd,kBAAkB;wCACpB;oCACF;gCACF;;wBACF;oBACF;;gBAEA,4CAA4C;gBAC5C,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,MAAM;gBAC5C,QAAQ,IAAI;6CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO;;gBAE5C,qBAAqB;gBACrB,MAAM,cAAc,MAAM,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI;gBAErD,MAAM,eAA0B;oBAC9B,QAAQ,YAAY,GAAG;iDAAC,CAAC,OAAS,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;;oBACzD,UAAU;wBACR;4BACE,OAAO;4BACP,MAAM,YAAY,GAAG;yDAAC,CAAC,OAAS,UAAU,GAAG,CAAC,SAAS;;4BACvD,iBAAiB;4BACjB,aAAa;4BACb,aAAa;wBACf;qBACD;gBACH;gBAEA,MAAM,mBAA8B;oBAClC,QAAQ,YAAY,GAAG;iDAAC,CAAC,OAAS,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;;oBACzD,UAAU;wBACR;4BACE,OAAO;4BACP,MAAM,YAAY,GAAG;yDAAC,CAAC,OAAS,mBAAmB,GAAG,CAAC,SAAS;;4BAChE,iBAAiB;4BACjB,aAAa;4BACb,aAAa;wBACf;qBACD;gBACH;gBAEA,OAAO;oBACL;oBACA;oBACA,aAAa;wBACX;wBACA;wBACA;wBACA;oBACF;oBACA;oBACA;gBACF;YACF;;IACF;AACF;IAzHa;;QACJ,8KAAA,CAAA,WAAQ;;;AA0HjB;;CAEC,GACD,SAAS,yBACP,YAAmC;IAEnC,OAAO;QACL,QAAQ,aAAa,GAAG,CAAC,CAAC,WAAa,SAAS,QAAQ;QACxD,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,aAAa,GAAG,CAAC,CAAC,WAAa,SAAS,UAAU;gBACxD,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;AACF;AAEA;;CAEC,GACD,SAAS,wBACP,WAAqC;IAErC,OAAO;QACL,QAAQ,YAAY,GAAG,CAAC,CAAC,UAAY,QAAQ,iBAAiB;QAC9D,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,YAAY,GAAG,CAAC,CAAC,UAAY,QAAQ,UAAU;gBACrD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;oBACX;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;YACf;SACD;IACH;AACF;AAKO,MAAM,wBAAwB,CAAC;;IACpC,sDAAsD;IACtD,MAAM,iBAAsE;QAC1E,WAAW,SAAS;QACpB,YAAY,SAAS,cAAc,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;QACtD,UAAU,SAAS;QACnB,gBAAgB,SAAS,oBACrB,OAAO,QAAQ,iBAAiB,IAChC;IACN;IAEA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAiB;SAAQ;QAC/C,OAAO;8CAAE;gBACP,gEAAgE;gBAChE,IAAI;gBACJ,IAAI,eAAe,SAAS,IAAI,eAAe,SAAS,GAAG,GAAG;oBAC5D,cAAc,MAAM,0JAAA,CAAA,UAAc,CAAC,iBAAiB,CAAC;gBACvD,OAAO;oBACL,4BAA4B;oBAC5B,MAAM,iBAAiC;wBACrC,WAAW,eAAe,SAAS;wBACnC,YAAY,eAAe,UAAU;wBACrC,UAAU,eAAe,QAAQ;wBACjC,gBAAgB,eAAe,cAAc;oBAC/C;oBACA,cAAc,MAAM,0JAAA,CAAA,UAAc,CAAC,0BAA0B,CAC3D;gBAEJ;gBAEA,0BAA0B;gBAC1B,MAAM,YAAY,6BAA6B;gBAE/C,+BAA+B;gBAC/B,MAAM,YAAY,YAAY,MAAM;gEAClC,CAAC,KAAa,OAAyB,MAAM,KAAK,IAAI;+DACtD;gBAEF,MAAM,aAAa,YAAY,MAAM;iEACnC,CAAC,KAAa,OAAyB,MAAM,KAAK,KAAK;gEACvD;gBAEF,MAAM,aAAa,YAAY,MAAM;iEACnC,CAAC,KAAa,OAAyB,MAAM,KAAK,KAAK;gEACvD;gBAEF,MAAM,cAAc,YAAY,MAAM;kEACpC,CAAC,KAAa,OAAyB,MAAM,KAAK,KAAK;iEACvD;gBAEF,MAAM,oBAAoB,YAAY,MAAM;wEAC1C,CAAC,KAAa,OAAyB,MAAM,KAAK,iBAAiB;uEACnE;gBAGF,OAAO;oBACL;oBACA;oBACA,SAAS;wBACP;wBACA;wBACA;wBACA;wBACA;wBACA,oBACE,oBAAoB,IAAI,cAAc,oBAAoB;oBAC9D;gBACF;YACF;;QACA,SAAS,CAAC,CAAC,eAAe,UAAU;IACtC;AACF;IAxEa;;QAWJ,8KAAA,CAAA,WAAQ;;;AA+DjB;;CAEC,GACD,SAAS,6BACP,WAA6B;IAE7B,OAAO;QACL,QAAQ,YAAY,GAAG,CAAC,CAAC,OAAS,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,IAAI,GAAG;QAC9D,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,YAAY,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;gBACzC,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;YACA;gBACE,OAAO;gBACP,MAAM,YAAY,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK;gBAC1C,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;YACA;gBACE,OAAO;gBACP,MAAM,YAAY,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK;gBAC1C,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;AACF;AAKO,MAAM,oBAAoB,CAAC;;IAChC,+BAA+B;IAC/B,MAAM,aAAiC;QACrC,GAAG,OAAO;QACV,QAAQ;IACV;IAEA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAa;SAAW;QAC9C,OAAO;0CAAE;gBACP,MAAM,YAAY,MAAM,0JAAA,CAAA,UAAc,CAAC,cAAc,CAAC;gBACtD,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;gBAExC,0BAA0B;gBAC1B,MAAM,YAAY,iBAAiB;gBAEnC,mCAAmC;gBACnC,MAAM,oBAAoB,IAAI;gBAC9B,UAAU,OAAO;kDAAC,CAAC;wBACjB,IAAI,KAAK,aAAa,EAAE;4BACtB,MAAM,WAAW,KAAK,aAAa,CAAC,EAAE;4BACtC,MAAM,SAAS,WAAW,KAAK,YAAY;4BAE3C,IAAI,CAAC,kBAAkB,GAAG,CAAC,WAAW;gCACpC,kBAAkB,GAAG,CAAC,UAAU;oCAC9B,iBAAiB;oCACjB,mBAAmB,KAAK,aAAa,CAAC,IAAI;oCAC1C,YAAY;oCACZ,mBAAmB;oCACnB,aAAa;oCACb,YAAY;gCACd;4BACF;4BAEA,MAAM,SAAS,kBAAkB,GAAG,CAAC;4BACrC,OAAO,UAAU,IAAI;4BACrB,OAAO,iBAAiB,IAAI;wBAC9B;oBACF;;gBAEA,MAAM,cAAc,MAAM,IAAI,CAAC,kBAAkB,MAAM;gBAEvD,mDAAmD;gBACnD,MAAM,mBAAmB,YAAY,MAAM;mEACzC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU;kEACxC;gBAGF,gEAAgE;gBAChE,YAAY,OAAO;kDAAC,CAAC;wBACnB,OAAO,WAAW,GAChB,OAAO,iBAAiB,GAAG,IACvB,OAAO,UAAU,GAAG,OAAO,iBAAiB,GAC5C;wBACN,OAAO,UAAU,GACf,mBAAmB,IACf,AAAC,OAAO,UAAU,GAAG,mBAAoB,MACzC;oBACR;;gBAEA,YAAY,IAAI;kDAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;;gBAEtD,0BAA0B;gBAC1B,MAAM,YAAY,IAAI;gBACtB,UAAU,OAAO;kDAAC,CAAC;wBACjB,IAAI,KAAK,IAAI,EAAE;4BACb,MAAM,SAAS,KAAK,IAAI,CAAC,EAAE;4BAC3B,MAAM,SAAS,WAAW,KAAK,YAAY;4BAE3C,IAAI,CAAC,UAAU,GAAG,CAAC,SAAS;gCAC1B,UAAU,GAAG,CAAC,QAAQ;oCACpB;oCACA,UAAU,KAAK,IAAI,CAAC,IAAI;oCACxB,YAAY;oCACZ,mBAAmB;oCACnB,aAAa;oCACb,QAAQ;gCACV;4BACF;4BAEA,MAAM,QAAQ,UAAU,GAAG,CAAC;4BAC5B,MAAM,UAAU,IAAI;4BACpB,MAAM,iBAAiB,IAAI;wBAC7B;oBACF;;gBAEA,MAAM,YAAY,MAAM,IAAI,CAAC,UAAU,MAAM;gBAE7C,wCAAwC;gBACxC,UAAU,OAAO;kDAAC,CAAC;wBACjB,MAAM,WAAW,GACf,MAAM,iBAAiB,GAAG,IACtB,MAAM,UAAU,GAAG,MAAM,iBAAiB,GAC1C;wBACN,6FAA6F;wBAC7F,MAAM,MAAM,GAAG,MAAM,UAAU,GAAG;oBACpC;;gBAEA,UAAU,IAAI;kDAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;;gBAEpD,oCAAoC;gBACpC,MAAM,mBAAmB,wBAAwB;gBAEjD,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;gBACF;YACF;;IACF;AACF;IAjHa;;QAOJ,8KAAA,CAAA,WAAQ;;;AA+GV,MAAM,wBAAwB,CAAC;;IACpC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;YAAiB;SAAQ;QAC/C,OAAO;8CAAE;gBACP,MAAM,cAAc,MAAM,0JAAA,CAAA,UAAc,CAAC,qBAAqB,CAAC;gBAE/D,0BAA0B;gBAC1B,MAAM,YAAY,6BAA6B;gBAE/C,oCAAoC;gBACpC,MAAM,aAAa,8BAA8B;gBAEjD,OAAO;oBACL;oBACA;oBACA;gBACF;YACF;;IACF;AACF;KAnBa;;QACJ,8KAAA,CAAA,WAAQ;;;AAoBjB;;CAEC,GACD,SAAS,6BAA6B,OAAsB;IAC1D,0BAA0B;IAC1B,MAAM,eAAuC,CAAC;IAC9C,QAAQ,OAAO,CAAC,CAAC;QACf,MAAM,SAAS,OAAO,MAAM;QAC5B,YAAY,CAAC,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,IAAI;IACvD;IAEA,0BAA0B;IAC1B,MAAM,eAAuC,CAAC;IAC9C,QAAQ,OAAO,CAAC,CAAC;QACf,MAAM,aAAa,OAAO,MAAM,EAAE,QAAQ;QAC1C,YAAY,CAAC,WAAW,GAAG,CAAC,YAAY,CAAC,WAAW,IAAI,CAAC,IAAI;IAC/D;IAEA,oBAAoB;IACpB,OAAO;QACL,QAAQ,OAAO,IAAI,CAAC;QACpB,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,OAAO,MAAM,CAAC;gBACpB,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;oBACX;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;YACf;SACD;QACD,eAAe;YACb,QAAQ,OAAO,IAAI,CAAC;YACpB,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM,OAAO,MAAM,CAAC;oBACpB,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,aAAa;wBACX;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,aAAa;gBACf;aACD;QACH;IACF;AACF;AAEA;;CAEC,GACD,SAAS,8BAA8B,OAAsB;IAC3D,0BAA0B;IAC1B,MAAM,eAA8C,CAAC;IACrD,QAAQ,OAAO,CAAC,CAAC;QACf,MAAM,SAAS,OAAO,MAAM;QAC5B,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACzB,YAAY,CAAC,OAAO,GAAG,EAAE;QAC3B;QACA,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;IAC5B;IAEA,gCAAgC;IAChC,MAAM,aAAa,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,QAAQ,QAAQ;QACpE,MAAM,QAAQ,QAAQ,MAAM;QAE5B,mCAAmC;QACnC,MAAM,qBAAqB,QAAQ,MAAM,CACvC,CAAC,KAAK,SAAW,MAAM,WAAW,OAAO,cAAc,IAAI,MAC3D;QAEF,MAAM,mBAAmB,QAAQ,IAAI,qBAAqB,QAAQ;QAElE,sDAAsD;QACtD,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,cAAc;QAC/D,MAAM,kBAAkB,iBAAiB,MAAM,CAC7C,CAAC,KAAK,SAAW,MAAM,WAAW,OAAO,cAAc,IAAI,MAC3D;QAEF,MAAM,gBACJ,iBAAiB,MAAM,GAAG,IACtB,kBAAkB,iBAAiB,MAAM,GACzC;QAEN,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA,gBAAgB,iBAAiB,MAAM;QACzC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1290, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/api/reports-api-service.ts"], "sourcesContent": ["/**\r\n * Reports API Service\r\n * Handles API calls for the new reports endpoints from REPORTS_ENDPOINT_GUIDE.md\r\n */\r\n\r\nimport apiClient from \"@/lib/api-client\";\r\nimport { \r\n  StockHistoryParams, \r\n  StockHistoryResponse,\r\n  SalesSummaryParams,\r\n  SalesSummaryResponse,\r\n  BankingTransactionsParams,\r\n  BankingTransactionsResponse,\r\n  ExpenseExportParams,\r\n  ExpenseExportResponse\r\n} from \"@/types/reports-api\";\r\n\r\n/**\r\n * Reports API Service\r\n * Uses the new endpoints specified in REPORTS_ENDPOINT_GUIDE.md\r\n */\r\nconst reportsApiService = {\r\n  /**\r\n   * Get stock history for a specific product\r\n   * Endpoint: GET /api/v1/reports/stock-history\r\n   * Permission: stock_reports:read\r\n   */\r\n  getStockHistory: async (params: StockHistoryParams): Promise<StockHistoryResponse | Blob> => {\r\n    try {\r\n      const config: any = { params };\r\n      \r\n      // If format is excel, expect blob response\r\n      if (params.format === 'excel') {\r\n        config.responseType = 'blob';\r\n        const response = await apiClient.get(\"/reports/stock-history\", config);\r\n        return response as Blob;\r\n      }\r\n      \r\n      const response = await apiClient.get<StockHistoryResponse>(\"/reports/stock-history\", config);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching stock history:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get sales summary report with enhanced filtering\r\n   * Endpoint: GET /api/v1/reports/sales-summary\r\n   * Permission: sales_reports:read\r\n   */\r\n  getSalesSummary: async (params: SalesSummaryParams): Promise<SalesSummaryResponse | Blob> => {\r\n    try {\r\n      const config: any = { params };\r\n      \r\n      // If format is excel, expect blob response\r\n      if (params.format === 'excel') {\r\n        config.responseType = 'blob';\r\n        const response = await apiClient.get(\"/reports/sales-summary\", config);\r\n        return response as Blob;\r\n      }\r\n      \r\n      const response = await apiClient.get<SalesSummaryResponse>(\"/reports/sales-summary\", config);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching sales summary:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get banking transactions report with pagination\r\n   * Endpoint: GET /api/v1/reports/banking-transactions\r\n   * Permission: banking_reports:read\r\n   */\r\n  getBankingTransactions: async (params?: BankingTransactionsParams): Promise<BankingTransactionsResponse | Blob> => {\r\n    try {\r\n      const config: any = { params };\r\n      \r\n      // If format is excel, expect blob response\r\n      if (params?.format === 'excel') {\r\n        config.responseType = 'blob';\r\n        const response = await apiClient.get(\"/reports/banking-transactions\", config);\r\n        return response as Blob;\r\n      }\r\n      \r\n      const response = await apiClient.get<BankingTransactionsResponse>(\"/reports/banking-transactions\", config);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching banking transactions:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Export expenses in Excel format\r\n   * Endpoint: GET /api/v1/expense-analytics/export\r\n   * Permission: expense_reports:read\r\n   */\r\n  exportExpenses: async (params?: ExpenseExportParams): Promise<ExpenseExportResponse> => {\r\n    try {\r\n      const response = await apiClient.get(\"/expense-analytics/export\", {\r\n        params,\r\n        responseType: 'blob',\r\n      });\r\n      return response as Blob;\r\n    } catch (error) {\r\n      console.error('Error exporting expenses:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get stock history summary for dashboard\r\n   * Simplified version for dashboard widgets\r\n   */\r\n  getStockHistorySummary: async (params: { branch_id?: number }): Promise<{\r\n    total_products: number;\r\n    low_stock_count: number;\r\n    total_value: number;\r\n    last_updated: string;\r\n  }> => {\r\n    try {\r\n      // This would be a simplified endpoint for dashboard use\r\n      // For now, we'll use the main endpoint and process the data\r\n      const response = await apiClient.get<any>(\"/reports/stock-summary\", {\r\n        params,\r\n      });\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching stock summary:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get banking transactions summary for dashboard\r\n   * Simplified version for dashboard widgets\r\n   */\r\n  getBankingTransactionsSummary: async (params: { \r\n    start_date?: string; \r\n    end_date?: string; \r\n    branch_id?: number; \r\n  }): Promise<{\r\n    total_transactions: number;\r\n    total_amount: number;\r\n    pending_count: number;\r\n    completed_count: number;\r\n  }> => {\r\n    try {\r\n      // Get summary data from the main endpoint\r\n      const response = await reportsApiService.getBankingTransactions({\r\n        ...params,\r\n        limit: 1, // We only need the summary, not the transactions\r\n      }) as BankingTransactionsResponse;\r\n      \r\n      return {\r\n        total_transactions: response.summary.total_transactions,\r\n        total_amount: response.summary.total_amount,\r\n        pending_count: response.transactions.filter(t => t.status === 'pending').length,\r\n        completed_count: response.transactions.filter(t => t.status === 'completed').length,\r\n      };\r\n    } catch (error) {\r\n      console.error('Error fetching banking summary:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\nexport default reportsApiService;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAYA;;;CAGC,GACD,MAAM,oBAAoB;IACxB;;;;GAIC,GACD,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,SAAc;gBAAE;YAAO;YAE7B,2CAA2C;YAC3C,IAAI,OAAO,MAAM,KAAK,SAAS;gBAC7B,OAAO,YAAY,GAAG;gBACtB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,0BAA0B;gBAC/D,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAuB,0BAA0B;YACrF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,SAAc;gBAAE;YAAO;YAE7B,2CAA2C;YAC3C,IAAI,OAAO,MAAM,KAAK,SAAS;gBAC7B,OAAO,YAAY,GAAG;gBACtB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,0BAA0B;gBAC/D,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAuB,0BAA0B;YACrF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,wBAAwB,OAAO;QAC7B,IAAI;YACF,MAAM,SAAc;gBAAE;YAAO;YAE7B,2CAA2C;YAC3C,IAAI,QAAQ,WAAW,SAAS;gBAC9B,OAAO,YAAY,GAAG;gBACtB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,iCAAiC;gBACtE,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAA8B,iCAAiC;YACnG,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,6BAA6B;gBAChE;gBACA,cAAc;YAChB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA;;;GAGC,GACD,wBAAwB,OAAO;QAM7B,IAAI;YACF,wDAAwD;YACxD,4DAA4D;YAC5D,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,0BAA0B;gBAClE;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;;GAGC,GACD,+BAA+B,OAAO;QAUpC,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,MAAM,kBAAkB,sBAAsB,CAAC;gBAC9D,GAAG,MAAM;gBACT,OAAO;YACT;YAEA,OAAO;gBACL,oBAAoB,SAAS,OAAO,CAAC,kBAAkB;gBACvD,cAAc,SAAS,OAAO,CAAC,YAAY;gBAC3C,eAAe,SAAS,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;gBAC/E,iBAAiB,SAAS,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YACrF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/hooks/use-reports-api.ts"], "sourcesContent": ["/**\r\n * React hooks for the new Reports API endpoints\r\n * Based on REPORTS_ENDPOINT_GUIDE.md specification\r\n */\r\n\r\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport reportsApiService from \"../api/reports-api-service\";\r\nimport { \r\n  StockHistoryParams,\r\n  SalesSummaryParams, \r\n  BankingTransactionsParams,\r\n  ExpenseExportParams,\r\n  StockHistoryResponse,\r\n  SalesSummaryResponse,\r\n  BankingTransactionsResponse\r\n} from \"@/types/reports-api\";\r\n\r\n/**\r\n * Hook to fetch stock history report data\r\n * Requires product_id to be enabled\r\n */\r\nexport const useStockHistory = (params: StockHistoryParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports-api\", \"stock-history\", params],\r\n    queryFn: async () => {\r\n      const response = await reportsApiService.getStockHistory(params);\r\n      return response as StockHistoryResponse;\r\n    },\r\n    enabled: !!params.product_id && params.format !== 'excel',\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch sales summary report data using the new API endpoint\r\n * Requires start_date and end_date to be enabled\r\n */\r\nexport const useSalesSummaryApi = (params: SalesSummaryParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports-api\", \"sales-summary\", params],\r\n    queryFn: async () => {\r\n      const response = await reportsApiService.getSalesSummary(params);\r\n      return response as SalesSummaryResponse;\r\n    },\r\n    enabled: !!(params.start_date && params.end_date) && params.format !== 'excel',\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch banking transactions report data with pagination\r\n */\r\nexport const useBankingTransactionsReport = (params?: BankingTransactionsParams) => {\r\n  return useQuery({\r\n    queryKey: [\"reports-api\", \"banking-transactions\", params],\r\n    queryFn: async () => {\r\n      const response = await reportsApiService.getBankingTransactions(params);\r\n      return response as BankingTransactionsResponse;\r\n    },\r\n    enabled: params?.format !== 'excel',\r\n    staleTime: 2 * 60 * 1000, // 2 minutes (more frequent updates for financial data)\r\n    gcTime: 5 * 60 * 1000, // 5 minutes\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch stock history summary for dashboard\r\n */\r\nexport const useStockHistorySummary = (params: { branch_id?: number } = {}) => {\r\n  return useQuery({\r\n    queryKey: [\"reports-api\", \"stock-summary\", params],\r\n    queryFn: () => reportsApiService.getStockHistorySummary(params),\r\n    staleTime: 10 * 60 * 1000, // 10 minutes\r\n    gcTime: 30 * 60 * 1000, // 30 minutes\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch banking transactions summary for dashboard\r\n */\r\nexport const useBankingTransactionsSummary = (params: { \r\n  start_date?: string; \r\n  end_date?: string; \r\n  branch_id?: number; \r\n} = {}) => {\r\n  return useQuery({\r\n    queryKey: [\"reports-api\", \"banking-summary\", params],\r\n    queryFn: () => reportsApiService.getBankingTransactionsSummary(params),\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    gcTime: 15 * 60 * 1000, // 15 minutes\r\n  });\r\n};\r\n\r\n/**\r\n * Mutation hook for stock history export\r\n */\r\nexport const useStockHistoryExport = () => {\r\n  return useMutation({\r\n    mutationFn: async (params: StockHistoryParams) => {\r\n      const exportParams = { ...params, format: 'excel' as const };\r\n      const blob = await reportsApiService.getStockHistory(exportParams) as Blob;\r\n      return { blob, params };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Mutation hook for sales summary export\r\n */\r\nexport const useSalesSummaryExport = () => {\r\n  return useMutation({\r\n    mutationFn: async (params: SalesSummaryParams) => {\r\n      const exportParams = { ...params, format: 'excel' as const };\r\n      const blob = await reportsApiService.getSalesSummary(exportParams) as Blob;\r\n      return { blob, params };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Mutation hook for banking transactions export\r\n */\r\nexport const useBankingTransactionsExport = () => {\r\n  return useMutation({\r\n    mutationFn: async (params: BankingTransactionsParams) => {\r\n      const exportParams = { ...params, format: 'excel' as const };\r\n      const blob = await reportsApiService.getBankingTransactions(exportParams) as Blob;\r\n      return { blob, params };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Mutation hook for expense export\r\n */\r\nexport const useExpenseExport = () => {\r\n  return useMutation({\r\n    mutationFn: async (params: ExpenseExportParams) => {\r\n      const blob = await reportsApiService.exportExpenses(params);\r\n      return { blob, params };\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to invalidate all reports cache\r\n * Useful after data changes that might affect reports\r\n */\r\nexport const useInvalidateReports = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return {\r\n    invalidateAll: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"reports-api\"] });\r\n    },\r\n    invalidateStockHistory: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"reports-api\", \"stock-history\"] });\r\n    },\r\n    invalidateSalesSummary: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"reports-api\", \"sales-summary\"] });\r\n    },\r\n    invalidateBankingTransactions: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"reports-api\", \"banking-transactions\"] });\r\n    },\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAED;AAAA;AAAA;AACA;;;;AAeO,MAAM,kBAAkB,CAAC;;IAC9B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;YAAiB;SAAO;QAClD,OAAO;wCAAE;gBACP,MAAM,WAAW,MAAM,iKAAA,CAAA,UAAiB,CAAC,eAAe,CAAC;gBACzD,OAAO;YACT;;QACA,SAAS,CAAC,CAAC,OAAO,UAAU,IAAI,OAAO,MAAM,KAAK;QAClD,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;AACF;GAXa;;QACJ,8KAAA,CAAA,WAAQ;;;AAgBV,MAAM,qBAAqB,CAAC;;IACjC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;YAAiB;SAAO;QAClD,OAAO;2CAAE;gBACP,MAAM,WAAW,MAAM,iKAAA,CAAA,UAAiB,CAAC,eAAe,CAAC;gBACzD,OAAO;YACT;;QACA,SAAS,CAAC,CAAC,CAAC,OAAO,UAAU,IAAI,OAAO,QAAQ,KAAK,OAAO,MAAM,KAAK;QACvE,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;AACF;IAXa;;QACJ,8KAAA,CAAA,WAAQ;;;AAeV,MAAM,+BAA+B,CAAC;;IAC3C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;YAAwB;SAAO;QACzD,OAAO;qDAAE;gBACP,MAAM,WAAW,MAAM,iKAAA,CAAA,UAAiB,CAAC,sBAAsB,CAAC;gBAChE,OAAO;YACT;;QACA,SAAS,QAAQ,WAAW;QAC5B,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;IACnB;AACF;IAXa;;QACJ,8KAAA,CAAA,WAAQ;;;AAeV,MAAM,yBAAyB,CAAC,SAAiC,CAAC,CAAC;;IACxE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;YAAiB;SAAO;QAClD,OAAO;+CAAE,IAAM,iKAAA,CAAA,UAAiB,CAAC,sBAAsB,CAAC;;QACxD,WAAW,KAAK,KAAK;QACrB,QAAQ,KAAK,KAAK;IACpB;AACF;IAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AAWV,MAAM,gCAAgC,CAAC,SAI1C,CAAC,CAAC;;IACJ,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;YAAmB;SAAO;QACpD,OAAO;sDAAE,IAAM,iKAAA,CAAA,UAAiB,CAAC,6BAA6B,CAAC;;QAC/D,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;AACF;IAXa;;QAKJ,8KAAA,CAAA,WAAQ;;;AAWV,MAAM,wBAAwB;;IACnC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,OAAO;gBACjB,MAAM,eAAe;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAiB;gBAC3D,MAAM,OAAO,MAAM,iKAAA,CAAA,UAAiB,CAAC,eAAe,CAAC;gBACrD,OAAO;oBAAE;oBAAM;gBAAO;YACxB;;IACF;AACF;IARa;;QACJ,iLAAA,CAAA,cAAW;;;AAYb,MAAM,wBAAwB;;IACnC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,OAAO;gBACjB,MAAM,eAAe;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAiB;gBAC3D,MAAM,OAAO,MAAM,iKAAA,CAAA,UAAiB,CAAC,eAAe,CAAC;gBACrD,OAAO;oBAAE;oBAAM;gBAAO;YACxB;;IACF;AACF;IARa;;QACJ,iLAAA,CAAA,cAAW;;;AAYb,MAAM,+BAA+B;;IAC1C,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;wDAAE,OAAO;gBACjB,MAAM,eAAe;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAiB;gBAC3D,MAAM,OAAO,MAAM,iKAAA,CAAA,UAAiB,CAAC,sBAAsB,CAAC;gBAC5D,OAAO;oBAAE;oBAAM;gBAAO;YACxB;;IACF;AACF;IARa;;QACJ,iLAAA,CAAA,cAAW;;;AAYb,MAAM,mBAAmB;;IAC9B,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,MAAM,OAAO,MAAM,iKAAA,CAAA,UAAiB,CAAC,cAAc,CAAC;gBACpD,OAAO;oBAAE;oBAAM;gBAAO;YACxB;;IACF;AACF;IAPa;;QACJ,iLAAA,CAAA,cAAW;;;AAYb,MAAM,uBAAuB;;IAClC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL,eAAe;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;QAC5D;QACA,wBAAwB;YACtB,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAe;iBAAgB;YAAC;QAC7E;QACA,wBAAwB;YACtB,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAe;iBAAgB;YAAC;QAC7E;QACA,+BAA+B;YAC7B,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAe;iBAAuB;YAAC;QACpF;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc", "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/product-selector.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { Check, ChevronsUpDown, Loader2 } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from \"@/components/ui/command\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { useProducts } from \"@/features/products/hooks/use-products\";\r\n\r\ninterface ProductSelectorProps {\r\n  value?: number;\r\n  onValueChange: (value: number | undefined) => void;\r\n  placeholder?: string;\r\n  disabled?: boolean;\r\n  includeAllOption?: boolean;\r\n}\r\n\r\nexport function ProductSelector({\r\n  value,\r\n  onValueChange,\r\n  placeholder = \"Select product...\",\r\n  disabled = false,\r\n  includeAllOption = true,\r\n}: ProductSelectorProps) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n\r\n  const { data: productsResponse, isLoading } = useProducts({\r\n    search: searchTerm,\r\n    page: 1,\r\n    limit: 50,\r\n  });\r\n\r\n  const products = productsResponse?.data || [];\r\n  const selectedProduct = products.find(p => p.id === value);\r\n\r\n  const handleSelect = (productId: number | undefined) => {\r\n    onValueChange(productId);\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          className=\"w-full justify-between\"\r\n          disabled={disabled}\r\n        >\r\n          {selectedProduct ? (\r\n            <span className=\"truncate\">\r\n              {selectedProduct.name}\r\n            </span>\r\n          ) : (\r\n            <span className=\"text-muted-foreground\">{placeholder}</span>\r\n          )}\r\n          <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[300px] p-0\">\r\n        <Command>\r\n          <CommandInput\r\n            placeholder=\"Search products...\"\r\n            value={searchTerm}\r\n            onValueChange={setSearchTerm}\r\n          />\r\n          <CommandList>\r\n            {isLoading ? (\r\n              <div className=\"flex items-center justify-center p-4\">\r\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                <span>Loading products...</span>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                <CommandEmpty>No products found.</CommandEmpty>\r\n                <CommandGroup>\r\n                  {includeAllOption && (\r\n                    <CommandItem\r\n                      value=\"all-products\"\r\n                      onSelect={() => handleSelect(undefined)}\r\n                    >\r\n                      <Check\r\n                        className={cn(\r\n                          \"mr-2 h-4 w-4\",\r\n                          !value ? \"opacity-100\" : \"opacity-0\"\r\n                        )}\r\n                      />\r\n                      All Products\r\n                    </CommandItem>\r\n                  )}\r\n                  {products.map((product) => (\r\n                    <CommandItem\r\n                      key={product.id}\r\n                      value={product.id.toString()}\r\n                      onSelect={() => handleSelect(product.id)}\r\n                    >\r\n                      <Check\r\n                        className={cn(\r\n                          \"mr-2 h-4 w-4\",\r\n                          value === product.id ? \"opacity-100\" : \"opacity-0\"\r\n                        )}\r\n                      />\r\n                      <div className=\"flex flex-col\">\r\n                        <span className=\"truncate\">{product.name}</span>\r\n                        <span className=\"text-xs text-muted-foreground truncate\">\r\n                          SKU: {product.sku}\r\n                        </span>\r\n                      </div>\r\n                    </CommandItem>\r\n                  ))}\r\n                </CommandGroup>\r\n              </>\r\n            )}\r\n          </CommandList>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAQA;AAKA;;;AAnBA;;;;;;;;AA6BO,SAAS,gBAAgB,EAC9B,KAAK,EACL,aAAa,EACb,cAAc,mBAAmB,EACjC,WAAW,KAAK,EAChB,mBAAmB,IAAI,EACF;;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,MAAM,gBAAgB,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE;QACxD,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IAEA,MAAM,WAAW,kBAAkB,QAAQ,EAAE;IAC7C,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEpD,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,QAAQ;IACV;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,WAAU;oBACV,UAAU;;wBAET,gCACC,6LAAC;4BAAK,WAAU;sCACb,gBAAgB,IAAI;;;;;iDAGvB,6LAAC;4BAAK,WAAU;sCAAyB;;;;;;sCAE3C,6LAAC,iOAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,6LAAC,sIAAA,CAAA,UAAO;;sCACN,6LAAC,sIAAA,CAAA,eAAY;4BACX,aAAY;4BACZ,OAAO;4BACP,eAAe;;;;;;sCAEjB,6LAAC,sIAAA,CAAA,cAAW;sCACT,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAK;;;;;;;;;;;qDAGR;;kDACE,6LAAC,sIAAA,CAAA,eAAY;kDAAC;;;;;;kDACd,6LAAC,sIAAA,CAAA,eAAY;;4CACV,kCACC,6LAAC,sIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,UAAU,IAAM,aAAa;;kEAE7B,6LAAC,uMAAA,CAAA,QAAK;wDACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,CAAC,QAAQ,gBAAgB;;;;;;oDAE3B;;;;;;;4CAIL,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,sIAAA,CAAA,cAAW;oDAEV,OAAO,QAAQ,EAAE,CAAC,QAAQ;oDAC1B,UAAU,IAAM,aAAa,QAAQ,EAAE;;sEAEvC,6LAAC,uMAAA,CAAA,QAAK;4DACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,UAAU,QAAQ,EAAE,GAAG,gBAAgB;;;;;;sEAG3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAY,QAAQ,IAAI;;;;;;8EACxC,6LAAC;oEAAK,WAAU;;wEAAyC;wEACjD,QAAQ,GAAG;;;;;;;;;;;;;;mDAbhB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BrC;GAvGgB;;QAUgC,0JAAA,CAAA,cAAW;;;KAV3C", "debugId": null}}, {"offset": {"line": 1947, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/report-filters.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { DateRangePicker } from \"@/components/ui/date-range-picker\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { useBranches } from \"@/features/branches/hooks/use-branches\";\r\nimport { EmployeeSelector } from \"@/features/employees/components/employee-selector\";\r\nimport { PaymentMethodSelector } from \"@/features/payment-methods/components/payment-method-selector\";\r\nimport { usePaymentMethods } from \"@/features/payment-methods/hooks/use-payment-methods\";\r\nimport { usePosSessions } from \"@/features/pos/hooks/use-pos-sessions\";\r\nimport { useRegions } from \"@/features/regions/hooks/use-regions\";\r\nimport { ProductSelector } from \"./product-selector\";\r\nimport {\r\n  DATE_RANGE_OPTIONS,\r\n  ReportFilterParams,\r\n  TIME_RANGE_OPTIONS,\r\n} from \"@/types\";\r\nimport { endOfDay, format, startOfDay, subDays } from \"date-fns\";\r\nimport { FilterIcon, RefreshCw } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\nimport { DateRange } from \"react-day-picker\";\r\n\r\ninterface ReportFiltersProps {\r\n  filters: ReportFilterParams;\r\n  onFilterChange: (filters: ReportFilterParams) => void;\r\n  showTimeFilter?: boolean;\r\n  showSessionFilter?: boolean;\r\n  showUserFilter?: boolean;\r\n  showBranchFilter?: boolean;\r\n  showRegionFilter?: boolean;\r\n  showPaymentMethodFilter?: boolean;\r\n  showStatusFilter?: boolean;\r\n  showDsaFilter?: boolean;\r\n  showProductFilter?: boolean;\r\n  showCategoryFilter?: boolean;\r\n  showLocationFilter?: boolean;\r\n  showBankingMethodFilter?: boolean;\r\n  showTransactionTypeFilter?: boolean;\r\n}\r\n\r\nexport function ReportFilters({\r\n  filters,\r\n  onFilterChange,\r\n  showTimeFilter = true,\r\n  showSessionFilter = false,\r\n  showUserFilter = true,\r\n  showBranchFilter = true,\r\n  showRegionFilter = false,\r\n  showPaymentMethodFilter = true,\r\n  showStatusFilter = false,\r\n  showDsaFilter = false,\r\n  showProductFilter = false,\r\n  showCategoryFilter = false,\r\n  showLocationFilter = false,\r\n  showBankingMethodFilter = false,\r\n  showTransactionTypeFilter = false,\r\n}: ReportFiltersProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [dateRange, setDateRange] = useState<DateRange>({\r\n    from: filters.start_date\r\n      ? new Date(filters.start_date)\r\n      : subDays(new Date(), 7),\r\n    to: filters.end_date ? new Date(filters.end_date) : new Date(),\r\n  });\r\n  const [selectedDateRange, setSelectedDateRange] =\r\n    useState<string>(\"this_week\");\r\n  // Time filter removed\r\n\r\n  const { data: branchesResponse } = useBranches();\r\n  const { data: paymentMethodsResponse } = usePaymentMethods();\r\n  const { data: posSessionsResponse } = usePosSessions();\r\n  const { data: regionsResponse } = useRegions();\r\n\r\n  // Filter branches by selected region\r\n  const filteredBranches = branchesResponse?.data?.filter((branch) => {\r\n    if (!filters.region_id) return true; // Show all branches if no region selected\r\n    return branch.region_id === filters.region_id;\r\n  }) || [];\r\n\r\n  const handleDateRangeChange = (range: DateRange) => {\r\n    setDateRange(range);\r\n    if (range?.from && range?.to) {\r\n      onFilterChange({\r\n        ...filters,\r\n        start_date: format(range.from, \"yyyy-MM-dd\"),\r\n        end_date: format(range.to, \"yyyy-MM-dd\"),\r\n      });\r\n    }\r\n  };\r\n\r\n  const handlePredefinedDateRange = (value: string) => {\r\n    setSelectedDateRange(value);\r\n    let from: Date | undefined;\r\n    let to: Date | undefined;\r\n\r\n    const today = new Date();\r\n\r\n    switch (value) {\r\n      case \"today\":\r\n        from = startOfDay(today);\r\n        to = endOfDay(today);\r\n        break;\r\n      case \"yesterday\":\r\n        from = startOfDay(subDays(today, 1));\r\n        to = endOfDay(subDays(today, 1));\r\n        break;\r\n      case \"this_week\":\r\n        from = startOfDay(subDays(today, today.getDay()));\r\n        to = endOfDay(today);\r\n        break;\r\n      case \"last_week\":\r\n        from = startOfDay(subDays(today, today.getDay() + 7));\r\n        to = endOfDay(subDays(today, today.getDay() + 1));\r\n        break;\r\n      case \"this_month\":\r\n        from = startOfDay(new Date(today.getFullYear(), today.getMonth(), 1));\r\n        to = endOfDay(today);\r\n        break;\r\n      case \"last_month\":\r\n        from = startOfDay(\r\n          new Date(today.getFullYear(), today.getMonth() - 1, 1)\r\n        );\r\n        to = endOfDay(new Date(today.getFullYear(), today.getMonth(), 0));\r\n        break;\r\n      case \"this_year\":\r\n        from = startOfDay(new Date(today.getFullYear(), 0, 1));\r\n        to = endOfDay(today);\r\n        break;\r\n      case \"last_year\":\r\n        from = startOfDay(new Date(today.getFullYear() - 1, 0, 1));\r\n        to = endOfDay(new Date(today.getFullYear() - 1, 11, 31));\r\n        break;\r\n      case \"custom\":\r\n        // Keep the current date range\r\n        return;\r\n      default:\r\n        from = subDays(today, 7);\r\n        to = today;\r\n    }\r\n\r\n    setDateRange({ from, to });\r\n\r\n    if (from && to) {\r\n      onFilterChange({\r\n        ...filters,\r\n        start_date: format(from, \"yyyy-MM-dd\"),\r\n        end_date: format(to, \"yyyy-MM-dd\"),\r\n      });\r\n    }\r\n  };\r\n\r\n  // Time filter removed\r\n\r\n  const handleBranchChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      branch_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleRegionChange = (value: string) => {\r\n    const regionId = value === \"all\" ? undefined : parseInt(value, 10);\r\n\r\n    onFilterChange({\r\n      ...filters,\r\n      region_id: regionId,\r\n      // Reset branch filter when region changes\r\n      branch_id: undefined,\r\n    });\r\n  };\r\n\r\n  const handleEmployeeChange = (value: number | undefined) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      employee_id: value,\r\n    });\r\n  };\r\n\r\n  const handlePaymentMethodChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      payment_method_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleSessionChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      pos_session_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleStatusChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      status: value === \"all\" ? undefined : value,\r\n    });\r\n  };\r\n\r\n  const handleDsaChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      is_dsa: value === \"all\" ? undefined : value === \"true\",\r\n    });\r\n  };\r\n\r\n  const handleProductChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      product_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleCategoryChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      category_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleLocationChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      location_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleBankingMethodChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      banking_method: value === \"all\" ? undefined : value,\r\n    });\r\n  };\r\n\r\n  const handleTransactionTypeChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      transaction_type: value === \"all\" ? undefined : value,\r\n    });\r\n  };\r\n\r\n  const handleReset = () => {\r\n    setSelectedDateRange(\"this_week\");\r\n    const today = new Date();\r\n    const from = subDays(today, 7);\r\n    const to = today;\r\n    setDateRange({ from, to });\r\n\r\n    // Reset all filters to default values\r\n    onFilterChange({\r\n      start_date: format(from, \"yyyy-MM-dd\"),\r\n      end_date: format(to, \"yyyy-MM-dd\"),\r\n      branch_id: undefined,\r\n      region_id: undefined,\r\n      payment_method_id: undefined,\r\n      employee_id: undefined,\r\n      pos_session_id: undefined,\r\n      status: undefined,\r\n      is_dsa: undefined,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Card className=\"mb-6\">\r\n      <CardContent className=\"p-4\">\r\n        <div className=\"flex flex-col space-y-4 md:flex-row md:flex-wrap md:items-end md:gap-4 md:space-y-0\">\r\n          {/* Date Range Filter */}\r\n          <div className=\"flex-1 space-y-2\">\r\n            <Label>Date Range</Label>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              <Select\r\n                value={selectedDateRange}\r\n                onValueChange={handlePredefinedDateRange}\r\n              >\r\n                <SelectTrigger className=\"w-[180px]\">\r\n                  <SelectValue placeholder=\"Select date range\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {DATE_RANGE_OPTIONS.map((option) => (\r\n                    <SelectItem key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n\r\n              <DateRangePicker\r\n                value={dateRange}\r\n                onChange={handleDateRangeChange}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Branch Filter - Moved outside of More Filters */}\r\n          {showBranchFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Branch</Label>\r\n              <Select\r\n                value={filters.branch_id ? filters.branch_id.toString() : \"all\"}\r\n                onValueChange={handleBranchChange}\r\n              >\r\n                <SelectTrigger className=\"w-[180px]\">\r\n                  <SelectValue placeholder=\"All Branches\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Branches</SelectItem>\r\n                  {filteredBranches.map((branch) => (\r\n                    <SelectItem key={branch.id} value={branch.id.toString()}>\r\n                      {branch.name}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          )}\r\n\r\n          {/* Payment Method Filter - Moved outside of More Filters */}\r\n          {showPaymentMethodFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Payment Method</Label>\r\n              <div className=\"w-[220px]\">\r\n                <PaymentMethodSelector\r\n                  value={filters.payment_method_id}\r\n                  onValueChange={(value) =>\r\n                    handlePaymentMethodChange(\r\n                      value !== undefined ? value.toString() : \"all\"\r\n                    )\r\n                  }\r\n                  placeholder=\"All Payment Methods\"\r\n                  includeAllOption={true}\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Product Filter */}\r\n          {showProductFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Product</Label>\r\n              <div className=\"w-[220px]\">\r\n                <ProductSelector\r\n                  value={filters.product_id}\r\n                  onValueChange={(value) =>\r\n                    handleProductChange(value?.toString() || \"all\")\r\n                  }\r\n                  placeholder=\"All Products\"\r\n                  includeAllOption={true}\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Region Filter */}\r\n          {showRegionFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Region</Label>\r\n              <Select\r\n                value={filters.region_id ? filters.region_id.toString() : \"all\"}\r\n                onValueChange={handleRegionChange}\r\n              >\r\n                <SelectTrigger className=\"w-[180px]\">\r\n                  <SelectValue placeholder=\"All Regions\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Regions</SelectItem>\r\n                  {regionsResponse?.data?.map((region) => (\r\n                    <SelectItem key={region.id} value={region.id.toString()}>\r\n                      {region.name}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          )}\r\n\r\n          {/* Employee Filter - Moved outside of More Filters */}\r\n          {showUserFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Employee</Label>\r\n              <div className=\"w-[220px]\">\r\n                <EmployeeSelector\r\n                  value={filters.employee_id}\r\n                  onValueChange={handleEmployeeChange}\r\n                  placeholder=\"All Employees\"\r\n                  includeAllOption={true}\r\n                  branchId={filters.branch_id}\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Time Range Filter removed */}\r\n\r\n          {/* More Filters Button */}\r\n          <div className=\"flex space-x-2\">\r\n            <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n              <PopoverTrigger asChild>\r\n                <Button variant=\"outline\">\r\n                  <FilterIcon className=\"mr-2 h-4 w-4\" />\r\n                  More Filters\r\n                </Button>\r\n              </PopoverTrigger>\r\n              <PopoverContent className=\"w-80 p-4\" align=\"end\">\r\n                <div className=\"grid gap-4\">\r\n                  {/* Branch Filter - Moved outside */}\r\n                  {/* Employee Filter - Moved outside */}\r\n\r\n                  {/* Session Filter */}\r\n                  {showSessionFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>POS Session</Label>\r\n                      <Select\r\n                        value={\r\n                          filters.pos_session_id\r\n                            ? filters.pos_session_id.toString()\r\n                            : \"all\"\r\n                        }\r\n                        onValueChange={handleSessionChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Sessions\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Sessions</SelectItem>\r\n                          {posSessionsResponse?.map((session) => (\r\n                            <SelectItem\r\n                              key={session.id}\r\n                              value={session.id.toString()}\r\n                            >\r\n                              Session #{session.id}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Status Filter */}\r\n                  {showStatusFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>Status</Label>\r\n                      <Select\r\n                        value={filters.status || \"all\"}\r\n                        onValueChange={handleStatusChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Statuses\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Statuses</SelectItem>\r\n                          <SelectItem value=\"completed\">Completed</SelectItem>\r\n                          <SelectItem value=\"pending\">Pending</SelectItem>\r\n                          <SelectItem value=\"cancelled\">Cancelled</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* DSA Filter */}\r\n                  {showDsaFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>Sale Type</Label>\r\n                      <Select\r\n                        value={\r\n                          filters.is_dsa !== undefined\r\n                            ? filters.is_dsa.toString()\r\n                            : \"all\"\r\n                        }\r\n                        onValueChange={handleDsaChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Sales\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Sales</SelectItem>\r\n                          <SelectItem value=\"true\">DSA Sales</SelectItem>\r\n                          <SelectItem value=\"false\">Regular Sales</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Banking Method Filter */}\r\n                  {showBankingMethodFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>Banking Method</Label>\r\n                      <Select\r\n                        value={filters.banking_method || \"all\"}\r\n                        onValueChange={handleBankingMethodChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Methods\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Methods</SelectItem>\r\n                          <SelectItem value=\"bank\">Bank</SelectItem>\r\n                          <SelectItem value=\"agent\">Agent</SelectItem>\r\n                          <SelectItem value=\"mpesa\">M-Pesa</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Transaction Type Filter */}\r\n                  {showTransactionTypeFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>Transaction Type</Label>\r\n                      <Select\r\n                        value={filters.transaction_type || \"all\"}\r\n                        onValueChange={handleTransactionTypeChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Types\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Types</SelectItem>\r\n                          <SelectItem value=\"deposit\">Deposit</SelectItem>\r\n                          <SelectItem value=\"withdrawal\">Withdrawal</SelectItem>\r\n                          <SelectItem value=\"transfer\">Transfer</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </PopoverContent>\r\n            </Popover>\r\n\r\n            <Button variant=\"outline\" onClick={handleReset}>\r\n              <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n              Reset\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAKA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAKA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAhCA;;;;;;;;;;;;;;;;;;AAqDO,SAAS,cAAc,EAC5B,OAAO,EACP,cAAc,EACd,iBAAiB,IAAI,EACrB,oBAAoB,KAAK,EACzB,iBAAiB,IAAI,EACrB,mBAAmB,IAAI,EACvB,mBAAmB,KAAK,EACxB,0BAA0B,IAAI,EAC9B,mBAAmB,KAAK,EACxB,gBAAgB,KAAK,EACrB,oBAAoB,KAAK,EACzB,qBAAqB,KAAK,EAC1B,qBAAqB,KAAK,EAC1B,0BAA0B,KAAK,EAC/B,4BAA4B,KAAK,EACd;;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM,QAAQ,UAAU,GACpB,IAAI,KAAK,QAAQ,UAAU,IAC3B,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;QACxB,IAAI,QAAQ,QAAQ,GAAG,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;IAC1D;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAC7C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnB,sBAAsB;IAEtB,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD;IAC7C,MAAM,EAAE,MAAM,sBAAsB,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,oBAAiB,AAAD;IACzD,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD;IACnD,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD;IAE3C,qCAAqC;IACrC,MAAM,mBAAmB,kBAAkB,MAAM,OAAO,CAAC;QACvD,IAAI,CAAC,QAAQ,SAAS,EAAE,OAAO,MAAM,0CAA0C;QAC/E,OAAO,OAAO,SAAS,KAAK,QAAQ,SAAS;IAC/C,MAAM,EAAE;IAER,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QACb,IAAI,OAAO,QAAQ,OAAO,IAAI;YAC5B,eAAe;gBACb,GAAG,OAAO;gBACV,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE;gBAC/B,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,EAAE;YAC7B;QACF;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,qBAAqB;QACrB,IAAI;QACJ,IAAI;QAEJ,MAAM,QAAQ,IAAI;QAElB,OAAQ;YACN,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE;gBAClB,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;gBACd;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACjC,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBAC7B;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,MAAM,MAAM;gBAC7C,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;gBACd;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,MAAM,MAAM,KAAK;gBAClD,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,MAAM,MAAM,KAAK;gBAC9C;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,IAAI;gBAClE,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;gBACd;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EACd,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,KAAK,GAAG;gBAEtD,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,IAAI;gBAC9D;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,IAAI,GAAG;gBACnD,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;gBACd;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,GAAG;gBACvD,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,IAAI;gBACpD;YACF,KAAK;gBACH,8BAA8B;gBAC9B;YACF;gBACE,OAAO,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACtB,KAAK;QACT;QAEA,aAAa;YAAE;YAAM;QAAG;QAExB,IAAI,QAAQ,IAAI;YACd,eAAe;gBACb,GAAG,OAAO;gBACV,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;gBACzB,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI;YACvB;QACF;IACF;IAEA,sBAAsB;IAEtB,MAAM,qBAAqB,CAAC;QAC1B,eAAe;YACb,GAAG,OAAO;YACV,WAAW,UAAU,QAAQ,YAAY,SAAS,OAAO;QAC3D;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,WAAW,UAAU,QAAQ,YAAY,SAAS,OAAO;QAE/D,eAAe;YACb,GAAG,OAAO;YACV,WAAW;YACX,0CAA0C;YAC1C,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;YACb,GAAG,OAAO;YACV,aAAa;QACf;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,eAAe;YACb,GAAG,OAAO;YACV,mBAAmB,UAAU,QAAQ,YAAY,SAAS,OAAO;QACnE;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,eAAe;YACb,GAAG,OAAO;YACV,gBAAgB,UAAU,QAAQ,YAAY,SAAS,OAAO;QAChE;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe;YACb,GAAG,OAAO;YACV,QAAQ,UAAU,QAAQ,YAAY;QACxC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,eAAe;YACb,GAAG,OAAO;YACV,QAAQ,UAAU,QAAQ,YAAY,UAAU;QAClD;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,eAAe;YACb,GAAG,OAAO;YACV,YAAY,UAAU,QAAQ,YAAY,SAAS,OAAO;QAC5D;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;YACb,GAAG,OAAO;YACV,aAAa,UAAU,QAAQ,YAAY,SAAS,OAAO;QAC7D;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;YACb,GAAG,OAAO;YACV,aAAa,UAAU,QAAQ,YAAY,SAAS,OAAO;QAC7D;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,eAAe;YACb,GAAG,OAAO;YACV,gBAAgB,UAAU,QAAQ,YAAY;QAChD;IACF;IAEA,MAAM,8BAA8B,CAAC;QACnC,eAAe;YACb,GAAG,OAAO;YACV,kBAAkB,UAAU,QAAQ,YAAY;QAClD;IACF;IAEA,MAAM,cAAc;QAClB,qBAAqB;QACrB,MAAM,QAAQ,IAAI;QAClB,MAAM,OAAO,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QAC5B,MAAM,KAAK;QACX,aAAa;YAAE;YAAM;QAAG;QAExB,sCAAsC;QACtC,eAAe;YACb,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACzB,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI;YACrB,WAAW;YACX,WAAW;YACX,mBAAmB;YACnB,aAAa;YACb,gBAAgB;YAChB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,eAAe;;0DAEf,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;0DACX,0HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,uBACvB,6LAAC,qIAAA,CAAA,aAAU;wDAAoB,OAAO,OAAO,KAAK;kEAC/C,OAAO,KAAK;uDADE,OAAO,KAAK;;;;;;;;;;;;;;;;kDAOnC,6LAAC,sJAAA,CAAA,kBAAe;wCACd,OAAO;wCACP,UAAU;;;;;;;;;;;;;;;;;;oBAMf,kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,QAAQ,KAAK;gCAC1D,eAAe;;kDAEf,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;4CACvB,iBAAiB,GAAG,CAAC,CAAC,uBACrB,6LAAC,qIAAA,CAAA,aAAU;oDAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;8DAClD,OAAO,IAAI;mDADG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;oBAUnC,yCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,wLAAA,CAAA,wBAAqB;oCACpB,OAAO,QAAQ,iBAAiB;oCAChC,eAAe,CAAC,QACd,0BACE,UAAU,YAAY,MAAM,QAAQ,KAAK;oCAG7C,aAAY;oCACZ,kBAAkB;;;;;;;;;;;;;;;;;oBAOzB,mCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mKAAA,CAAA,kBAAe;oCACd,OAAO,QAAQ,UAAU;oCACzB,eAAe,CAAC,QACd,oBAAoB,OAAO,cAAc;oCAE3C,aAAY;oCACZ,kBAAkB;;;;;;;;;;;;;;;;;oBAOzB,kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,QAAQ,KAAK;gCAC1D,eAAe;;kDAEf,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;4CACvB,iBAAiB,MAAM,IAAI,CAAC,uBAC3B,6LAAC,qIAAA,CAAA,aAAU;oDAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;8DAClD,OAAO,IAAI;mDADG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;oBAUnC,gCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sKAAA,CAAA,mBAAgB;oCACf,OAAO,QAAQ,WAAW;oCAC1B,eAAe;oCACf,aAAY;oCACZ,kBAAkB;oCAClB,UAAU,QAAQ,SAAS;;;;;;;;;;;;;;;;;kCASnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAO;gCAAC,MAAM;gCAAQ,cAAc;;kDACnC,6LAAC,sIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;;8DACd,6LAAC,6MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI3C,6LAAC,sIAAA,CAAA,iBAAc;wCAAC,WAAU;wCAAW,OAAM;kDACzC,cAAA,6LAAC;4CAAI,WAAU;;gDAKZ,mCACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OACE,QAAQ,cAAc,GAClB,QAAQ,cAAc,CAAC,QAAQ,KAC/B;4DAEN,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;wEACvB,qBAAqB,IAAI,CAAC,wBACzB,6LAAC,qIAAA,CAAA,aAAU;gFAET,OAAO,QAAQ,EAAE,CAAC,QAAQ;;oFAC3B;oFACW,QAAQ,EAAE;;+EAHf,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;gDAY1B,kCACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO,QAAQ,MAAM,IAAI;4DACzB,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;sFACxB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAY;;;;;;sFAC9B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;sFAC5B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAY;;;;;;;;;;;;;;;;;;;;;;;;gDAOrC,+BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OACE,QAAQ,MAAM,KAAK,YACf,QAAQ,MAAM,CAAC,QAAQ,KACvB;4DAEN,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;sFACxB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAO;;;;;;sFACzB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;gDAOjC,yCACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO,QAAQ,cAAc,IAAI;4DACjC,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;sFACxB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAO;;;;;;sFACzB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAQ;;;;;;sFAC1B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;gDAOjC,2CACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO,QAAQ,gBAAgB,IAAI;4DACnC,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;sFACxB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;sFAC5B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAa;;;;;;sFAC/B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;GAhfgB;;QA4BqB,0JAAA,CAAA,cAAW;QACL,8KAAA,CAAA,oBAAiB;QACpB,4JAAA,CAAA,iBAAc;QAClB,wJAAA,CAAA,aAAU;;;KA/B9B", "debugId": null}}, {"offset": {"line": 2971, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/report-stat-card.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\r\nimport { cn, formatCurrency } from \"@/lib/utils\";\r\nimport {\r\n  BanknoteIcon,\r\n  CheckCircle2,\r\n  Clock,\r\n  CreditCard,\r\n  DollarSign,\r\n  LucideIcon,\r\n  Smartphone,\r\n  Wallet,\r\n} from \"lucide-react\";\r\nimport { ReactNode } from \"react\";\r\n\r\ninterface ReportStatCardProps {\r\n  title: string;\r\n  value: string | number;\r\n  description?: string;\r\n  icon?: LucideIcon | string | ReactNode;\r\n  trend?: number;\r\n  trendLabel?: string;\r\n  isCurrency?: boolean;\r\n  isPercentage?: boolean;\r\n  isCount?: boolean;\r\n  className?: string;\r\n}\r\n\r\nexport function ReportStatCard({\r\n  title,\r\n  value,\r\n  description,\r\n  icon: Icon,\r\n  trend,\r\n  trendLabel,\r\n  isCurrency = false,\r\n  isPercentage = false,\r\n  isCount = false,\r\n  className,\r\n}: ReportStatCardProps) {\r\n  const formattedValue = isCurrency\r\n    ? formatCurrency(\r\n        typeof value === \"string\" ? parseFloat(value || \"0\") : value || 0\r\n      )\r\n    : isPercentage\r\n    ? `${value || 0}%`\r\n    : isCount\r\n    ? value !== undefined && value !== null\r\n      ? Number(value).toLocaleString()\r\n      : \"0\"\r\n    : value || \"\";\r\n\r\n  return (\r\n    <Card className={cn(\"overflow-hidden\", className)}>\r\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n        <CardTitle className=\"text-sm font-medium\">{title}</CardTitle>\r\n        {typeof Icon === \"string\" ? (\r\n          getIconByName(Icon as string)\r\n        ) : typeof Icon === \"function\" ? (\r\n          <Icon className=\"h-4 w-4 text-muted-foreground\" />\r\n        ) : Icon ? (\r\n          Icon\r\n        ) : null}\r\n      </CardHeader>\r\n      <CardContent>\r\n        <div className=\"text-2xl font-bold\">{formattedValue}</div>\r\n        {(description || trend !== undefined) && (\r\n          <p className=\"text-xs text-muted-foreground\">\r\n            {description}\r\n            {trend !== undefined && (\r\n              <span\r\n                className={cn(\r\n                  \"ml-1\",\r\n                  trend > 0 ? \"text-green-500\" : trend < 0 ? \"text-red-500\" : \"\"\r\n                )}\r\n              >\r\n                {trend > 0 ? \"+\" : \"\"}\r\n                {trend}%{trendLabel && ` ${trendLabel}`}\r\n              </span>\r\n            )}\r\n          </p>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n\r\n// Helper function to get icon by name\r\nfunction getIconByName(iconName: string) {\r\n  switch (iconName.toLowerCase()) {\r\n    case \"bank\":\r\n      return <BanknoteIcon className=\"h-4 w-4 text-blue-500\" />;\r\n    case \"mpesa\":\r\n      return <CreditCard className=\"h-4 w-4 text-green-500\" />;\r\n    case \"agent\":\r\n      return <Wallet className=\"h-4 w-4 text-amber-500\" />;\r\n    case \"money\":\r\n      return <DollarSign className=\"h-4 w-4 text-emerald-500\" />;\r\n    case \"transaction\":\r\n      return <Clock className=\"h-4 w-4 text-purple-500\" />;\r\n    case \"calculator\":\r\n      return <CheckCircle2 className=\"h-4 w-4 text-green-500\" />;\r\n    case \"phone\":\r\n      return <Smartphone className=\"h-4 w-4 text-indigo-500\" />;\r\n    default:\r\n      return <DollarSign className=\"h-4 w-4 text-muted-foreground\" />;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AA6BO,SAAS,eAAe,EAC7B,KAAK,EACL,KAAK,EACL,WAAW,EACX,MAAM,IAAI,EACV,KAAK,EACL,UAAU,EACV,aAAa,KAAK,EAClB,eAAe,KAAK,EACpB,UAAU,KAAK,EACf,SAAS,EACW;IACpB,MAAM,iBAAiB,aACnB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EACX,OAAO,UAAU,WAAW,WAAW,SAAS,OAAO,SAAS,KAElE,eACA,GAAG,SAAS,EAAE,CAAC,CAAC,GAChB,UACA,UAAU,aAAa,UAAU,OAC/B,OAAO,OAAO,cAAc,KAC5B,MACF,SAAS;IAEb,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;;0BACrC,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAuB;;;;;;oBAC3C,OAAO,SAAS,WACf,cAAc,QACZ,OAAO,SAAS,2BAClB,6LAAC;wBAAK,WAAU;;;;;+BACd,OACF,OACE;;;;;;;0BAEN,6LAAC,mIAAA,CAAA,cAAW;;kCACV,6LAAC;wBAAI,WAAU;kCAAsB;;;;;;oBACpC,CAAC,eAAe,UAAU,SAAS,mBAClC,6LAAC;wBAAE,WAAU;;4BACV;4BACA,UAAU,2BACT,6LAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,QAAQ,IAAI,mBAAmB,QAAQ,IAAI,iBAAiB;;oCAG7D,QAAQ,IAAI,MAAM;oCAClB;oCAAM;oCAAE,cAAc,CAAC,CAAC,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;KAzDgB;AA2DhB,sCAAsC;AACtC,SAAS,cAAc,QAAgB;IACrC,OAAQ,SAAS,WAAW;QAC1B,KAAK;YACH,qBAAO,6LAAC,iNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;QACjC,KAAK;YACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B,KAAK;YACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC3B,KAAK;YACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B,KAAK;YACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B,KAAK;YACH,qBAAO,6LAAC,wNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;QACjC,KAAK;YACH,qBAAO,6LAAC,iNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B;YACE,qBAAO,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;IACjC;AACF", "debugId": null}}, {"offset": {"line": 3145, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/report-chart.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\r\nimport { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { ChartData } from \"@/types\";\r\nimport {\r\n  <PERSON>,\r\n  <PERSON><PERSON>hart,\r\n  CartesianGrid,\r\n  Cell,\r\n  Line,\r\n  LineChart,\r\n  Pie,\r\n  <PERSON>hart,\r\n  XAxis,\r\n  YAxis,\r\n} from \"recharts\";\r\nimport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  type ChartConfig,\r\n} from \"@/components/ui/chart\";\r\n\r\ninterface ReportChartProps {\r\n  title: string;\r\n  description?: string;\r\n  data: ChartData;\r\n  chartTypes?: (\"line\" | \"bar\" | \"pie\")[];\r\n  defaultChartType?: \"line\" | \"bar\" | \"pie\";\r\n  height?: number;\r\n  showLegend?: boolean;\r\n  showGrid?: boolean;\r\n  showTooltip?: boolean;\r\n  className?: string;\r\n  trendInfo?: {\r\n    value: number;\r\n    isPositive: boolean;\r\n    label?: string;\r\n  };\r\n}\r\n\r\nexport function ReportChart({\r\n  title,\r\n  description,\r\n  data,\r\n  chartTypes = [\"line\", \"bar\"],\r\n  defaultChartType = \"line\",\r\n  height = 250,\r\n  showLegend = true,\r\n  showGrid = true,\r\n  showTooltip = true,\r\n  className,\r\n  trendInfo,\r\n}: ReportChartProps) {\r\n  // Generate chart config for Shadcn UI chart\r\n  const chartConfig: ChartConfig = {};\r\n\r\n  // Define colors for datasets\r\n  const colors = [\r\n    { light: \"#3b82f6\", dark: \"#60a5fa\" }, // blue-500/400\r\n    { light: \"#10b981\", dark: \"#34d399\" }, // emerald-500/400\r\n    { light: \"#f59e0b\", dark: \"#fbbf24\" }, // amber-500/400\r\n    { light: \"#ef4444\", dark: \"#f87171\" }, // red-500/400\r\n    { light: \"#8b5cf6\", dark: \"#a78bfa\" }, // violet-500/400\r\n    { light: \"#ec4899\", dark: \"#f472b6\" }, // pink-500/400\r\n    { light: \"#06b6d4\", dark: \"#22d3ee\" }, // cyan-500/400\r\n  ];\r\n\r\n  // Create chart config for each dataset\r\n  data.datasets.forEach((dataset, index) => {\r\n    chartConfig[dataset.label] = {\r\n      label: dataset.label,\r\n      theme: {\r\n        light:\r\n          (dataset.borderColor as string) ||\r\n          colors[index % colors.length].light,\r\n        dark: colors[index % colors.length].dark,\r\n      },\r\n    };\r\n\r\n    // For pie charts, add colors for each label\r\n    if (chartTypes.includes(\"pie\")) {\r\n      data.labels.forEach((label, labelIndex) => {\r\n        const labelKey = label.toString().replace(/\\s+/g, \"-\").toLowerCase();\r\n        if (!chartConfig[labelKey]) {\r\n          chartConfig[labelKey] = {\r\n            label: label.toString(),\r\n            theme: {\r\n              light: colors[labelIndex % colors.length].light,\r\n              dark: colors[labelIndex % colors.length].dark,\r\n            },\r\n          };\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  // Prepare data for charts\r\n  const chartData = data.labels.map((label, i) => ({\r\n    name: label,\r\n    ...data.datasets.reduce(\r\n      (acc, dataset) => ({\r\n        ...acc,\r\n        [dataset.label]: dataset.data[i],\r\n      }),\r\n      {}\r\n    ),\r\n  }));\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle>{title}</CardTitle>\r\n        {description && (\r\n          <p className=\"text-sm text-muted-foreground\">{description}</p>\r\n        )}\r\n      </CardHeader>\r\n      <CardContent>\r\n        <Tabs defaultValue={defaultChartType}>\r\n          <TabsList className=\"mb-4\">\r\n            {chartTypes.includes(\"line\") && (\r\n              <TabsTrigger value=\"line\">Line</TabsTrigger>\r\n            )}\r\n            {chartTypes.includes(\"bar\") && (\r\n              <TabsTrigger value=\"bar\">Bar</TabsTrigger>\r\n            )}\r\n            {chartTypes.includes(\"pie\") && (\r\n              <TabsTrigger value=\"pie\">Pie</TabsTrigger>\r\n            )}\r\n          </TabsList>\r\n\r\n          {chartTypes.includes(\"line\") && (\r\n            <TabsContent value=\"line\" className=\"space-y-4\">\r\n              <ChartContainer\r\n                config={chartConfig}\r\n                className=\"h-[250px]\"\r\n              >\r\n                <LineChart\r\n                  accessibilityLayer\r\n                  data={chartData}\r\n                  margin={{ top: 10, right: 30, left: 20, bottom: 20 }}\r\n                >\r\n                  {showGrid && (\r\n                    <CartesianGrid strokeDasharray=\"3 3\" vertical={false} />\r\n                  )}\r\n                  <XAxis\r\n                    dataKey=\"name\"\r\n                    tickLine={false}\r\n                    axisLine={false}\r\n                    tickMargin={10}\r\n                    tickFormatter={(value) =>\r\n                      typeof value === \"string\" && value.length > 10\r\n                        ? `${value.slice(0, 10)}...`\r\n                        : value\r\n                    }\r\n                  />\r\n                  <YAxis tickLine={false} axisLine={false} tickMargin={10} />\r\n                  {showTooltip && (\r\n                    <ChartTooltip\r\n                      cursor={false}\r\n                      content={<ChartTooltipContent hideLabel />}\r\n                    />\r\n                  )}\r\n                  {showLegend && (\r\n                    <ChartLegend\r\n                      content={<ChartLegendContent />}\r\n                      verticalAlign=\"top\"\r\n                      height={36}\r\n                    />\r\n                  )}\r\n                  {data.datasets.map((dataset) => (\r\n                    <Line\r\n                      key={dataset.label}\r\n                      type=\"natural\"\r\n                      dataKey={dataset.label}\r\n                      stroke={`var(--color-${dataset.label})`}\r\n                      strokeWidth={2}\r\n                      dot={{\r\n                        fill: `var(--color-${dataset.label})`,\r\n                        r: 4,\r\n                      }}\r\n                      activeDot={{\r\n                        r: 6,\r\n                        strokeWidth: 2,\r\n                      }}\r\n                    />\r\n                  ))}\r\n                </LineChart>\r\n              </ChartContainer>\r\n            </TabsContent>\r\n          )}\r\n\r\n          {chartTypes.includes(\"bar\") && (\r\n            <TabsContent value=\"bar\" className=\"space-y-4\">\r\n              <ChartContainer\r\n                config={chartConfig}\r\n                className=\"h-[250px]\"\r\n              >\r\n                <BarChart\r\n                  accessibilityLayer\r\n                  data={chartData}\r\n                  margin={{ top: 10, right: 30, left: 20, bottom: 20 }}\r\n                >\r\n                  {showGrid && (\r\n                    <CartesianGrid strokeDasharray=\"3 3\" vertical={false} />\r\n                  )}\r\n                  <XAxis\r\n                    dataKey=\"name\"\r\n                    tickLine={false}\r\n                    axisLine={false}\r\n                    tickMargin={10}\r\n                    tickFormatter={(value) =>\r\n                      typeof value === \"string\" && value.length > 10\r\n                        ? `${value.slice(0, 10)}...`\r\n                        : value\r\n                    }\r\n                  />\r\n                  <YAxis tickLine={false} axisLine={false} tickMargin={10} />\r\n                  {showTooltip && (\r\n                    <ChartTooltip content={<ChartTooltipContent hideLabel />} />\r\n                  )}\r\n                  {showLegend && (\r\n                    <ChartLegend\r\n                      content={<ChartLegendContent />}\r\n                      verticalAlign=\"top\"\r\n                      height={36}\r\n                    />\r\n                  )}\r\n                  {data.datasets.map((dataset) => (\r\n                    <Bar\r\n                      key={dataset.label}\r\n                      dataKey={dataset.label}\r\n                      fill={`var(--color-${dataset.label})`}\r\n                      radius={[4, 4, 4, 4]}\r\n                      maxBarSize={60}\r\n                    />\r\n                  ))}\r\n                </BarChart>\r\n              </ChartContainer>\r\n            </TabsContent>\r\n          )}\r\n\r\n          {chartTypes.includes(\"pie\") && (\r\n            <TabsContent value=\"pie\" className=\"space-y-4\">\r\n              <ChartContainer\r\n                config={chartConfig}\r\n                className=\"h-[250px]\"\r\n              >\r\n                <PieChart margin={{ top: 10, right: 30, left: 20, bottom: 20 }}>\r\n                  {data.datasets.map((dataset) => {\r\n                    const pieData = data.labels.map((label, i) => ({\r\n                      name: label,\r\n                      value: dataset.data[i],\r\n                      dataKey: dataset.label,\r\n                      fill: `var(--color-${label\r\n                        .toString()\r\n                        .replace(/\\s+/g, \"-\")\r\n                        .toLowerCase()})`,\r\n                    }));\r\n\r\n                    return (\r\n                      <Pie\r\n                        key={dataset.label}\r\n                        data={pieData}\r\n                        cx=\"50%\"\r\n                        cy=\"50%\"\r\n                        outerRadius={80}\r\n                        innerRadius={30}\r\n                        paddingAngle={2}\r\n                        dataKey=\"value\"\r\n                        nameKey=\"name\"\r\n                        label={({ name, percent }) =>\r\n                          percent > 0.05\r\n                            ? `${name}: ${(percent * 100).toFixed(0)}%`\r\n                            : \"\"\r\n                        }\r\n                        labelLine={false}\r\n                        className=\"[&_.recharts-pie-label-text]:fill-foreground\"\r\n                      >\r\n                        {pieData.map((entry, index) => (\r\n                          <Cell key={`cell-${index}`} fill={entry.fill} />\r\n                        ))}\r\n                      </Pie>\r\n                    );\r\n                  })}\r\n                  {showTooltip && (\r\n                    <ChartTooltip content={<ChartTooltipContent hideLabel />} />\r\n                  )}\r\n                  {showLegend && (\r\n                    <ChartLegend\r\n                      content={<ChartLegendContent />}\r\n                      verticalAlign=\"bottom\"\r\n                      height={36}\r\n                      iconSize={10}\r\n                      iconType=\"circle\"\r\n                    />\r\n                  )}\r\n                </PieChart>\r\n              </ChartContainer>\r\n            </TabsContent>\r\n          )}\r\n        </Tabs>\r\n      </CardContent>\r\n      {trendInfo && (\r\n        <div className=\"border-t px-6 py-4 flex-col items-start gap-2 text-sm\">\r\n          <div className=\"flex items-center gap-2 font-medium leading-none\">\r\n            {trendInfo.isPositive ? (\r\n              <>\r\n                Trending up by {Math.abs(trendInfo.value).toFixed(1)}%{\" \"}\r\n                {trendInfo.label || \"\"}\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  className=\"h-4 w-4 text-emerald-500\"\r\n                >\r\n                  <path d=\"m6 9 6-6 6 6\" />\r\n                  <path d=\"M6 12h12\" />\r\n                  <path d=\"M6 15h12\" />\r\n                  <path d=\"M6 18h12\" />\r\n                </svg>\r\n              </>\r\n            ) : (\r\n              <>\r\n                Trending down by {Math.abs(trendInfo.value).toFixed(1)}%{\" \"}\r\n                {trendInfo.label || \"\"}\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  className=\"h-4 w-4 text-red-500\"\r\n                >\r\n                  <path d=\"m6 15 6 6 6-6\" />\r\n                  <path d=\"M6 6h12\" />\r\n                  <path d=\"M6 9h12\" />\r\n                  <path d=\"M6 12h12\" />\r\n                </svg>\r\n              </>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAjBA;;;;;;AA4CO,SAAS,YAAY,EAC1B,KAAK,EACL,WAAW,EACX,IAAI,EACJ,aAAa;IAAC;IAAQ;CAAM,EAC5B,mBAAmB,MAAM,EACzB,SAAS,GAAG,EACZ,aAAa,IAAI,EACjB,WAAW,IAAI,EACf,cAAc,IAAI,EAClB,SAAS,EACT,SAAS,EACQ;IACjB,4CAA4C;IAC5C,MAAM,cAA2B,CAAC;IAElC,6BAA6B;IAC7B,MAAM,SAAS;QACb;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;KACrC;IAED,uCAAuC;IACvC,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS;QAC9B,WAAW,CAAC,QAAQ,KAAK,CAAC,GAAG;YAC3B,OAAO,QAAQ,KAAK;YACpB,OAAO;gBACL,OACE,AAAC,QAAQ,WAAW,IACpB,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC,CAAC,KAAK;gBACrC,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC,CAAC,IAAI;YAC1C;QACF;QAEA,4CAA4C;QAC5C,IAAI,WAAW,QAAQ,CAAC,QAAQ;YAC9B,KAAK,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO;gBAC1B,MAAM,WAAW,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,WAAW;gBAClE,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;oBAC1B,WAAW,CAAC,SAAS,GAAG;wBACtB,OAAO,MAAM,QAAQ;wBACrB,OAAO;4BACL,OAAO,MAAM,CAAC,aAAa,OAAO,MAAM,CAAC,CAAC,KAAK;4BAC/C,MAAM,MAAM,CAAC,aAAa,OAAO,MAAM,CAAC,CAAC,IAAI;wBAC/C;oBACF;gBACF;YACF;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,YAAY,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,IAAM,CAAC;YAC/C,MAAM;YACN,GAAG,KAAK,QAAQ,CAAC,MAAM,CACrB,CAAC,KAAK,UAAY,CAAC;oBACjB,GAAG,GAAG;oBACN,CAAC,QAAQ,KAAK,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE;gBAClC,CAAC,GACD,CAAC,EACF;QACH,CAAC;IAED,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;kCAAE;;;;;;oBACX,6BACC,6LAAC;wBAAE,WAAU;kCAAiC;;;;;;;;;;;;0BAGlD,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,cAAc;;sCAClB,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;gCACjB,WAAW,QAAQ,CAAC,yBACnB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAO;;;;;;gCAE3B,WAAW,QAAQ,CAAC,wBACnB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAM;;;;;;gCAE1B,WAAW,QAAQ,CAAC,wBACnB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAM;;;;;;;;;;;;wBAI5B,WAAW,QAAQ,CAAC,yBACnB,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAO,WAAU;sCAClC,cAAA,6LAAC,oIAAA,CAAA,iBAAc;gCACb,QAAQ;gCACR,WAAU;0CAEV,cAAA,6LAAC,wJAAA,CAAA,YAAS;oCACR,kBAAkB;oCAClB,MAAM;oCACN,QAAQ;wCAAE,KAAK;wCAAI,OAAO;wCAAI,MAAM;wCAAI,QAAQ;oCAAG;;wCAElD,0BACC,6LAAC,gKAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,UAAU;;;;;;sDAEjD,6LAAC,wJAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,UAAU;4CACV,UAAU;4CACV,YAAY;4CACZ,eAAe,CAAC,QACd,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,KACxC,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,GAC1B;;;;;;sDAGR,6LAAC,wJAAA,CAAA,QAAK;4CAAC,UAAU;4CAAO,UAAU;4CAAO,YAAY;;;;;;wCACpD,6BACC,6LAAC,oIAAA,CAAA,eAAY;4CACX,QAAQ;4CACR,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;gDAAC,SAAS;;;;;;;;;;;wCAG1C,4BACC,6LAAC,oIAAA,CAAA,cAAW;4CACV,uBAAS,6LAAC,oIAAA,CAAA,qBAAkB;;;;;4CAC5B,eAAc;4CACd,QAAQ;;;;;;wCAGX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,6LAAC,uJAAA,CAAA,OAAI;gDAEH,MAAK;gDACL,SAAS,QAAQ,KAAK;gDACtB,QAAQ,CAAC,YAAY,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;gDACvC,aAAa;gDACb,KAAK;oDACH,MAAM,CAAC,YAAY,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;oDACrC,GAAG;gDACL;gDACA,WAAW;oDACT,GAAG;oDACH,aAAa;gDACf;+CAZK,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;wBAoB7B,WAAW,QAAQ,CAAC,wBACnB,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;sCACjC,cAAA,6LAAC,oIAAA,CAAA,iBAAc;gCACb,QAAQ;gCACR,WAAU;0CAEV,cAAA,6LAAC,uJAAA,CAAA,WAAQ;oCACP,kBAAkB;oCAClB,MAAM;oCACN,QAAQ;wCAAE,KAAK;wCAAI,OAAO;wCAAI,MAAM;wCAAI,QAAQ;oCAAG;;wCAElD,0BACC,6LAAC,gKAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,UAAU;;;;;;sDAEjD,6LAAC,wJAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,UAAU;4CACV,UAAU;4CACV,YAAY;4CACZ,eAAe,CAAC,QACd,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,KACxC,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,GAC1B;;;;;;sDAGR,6LAAC,wJAAA,CAAA,QAAK;4CAAC,UAAU;4CAAO,UAAU;4CAAO,YAAY;;;;;;wCACpD,6BACC,6LAAC,oIAAA,CAAA,eAAY;4CAAC,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;gDAAC,SAAS;;;;;;;;;;;wCAEtD,4BACC,6LAAC,oIAAA,CAAA,cAAW;4CACV,uBAAS,6LAAC,oIAAA,CAAA,qBAAkB;;;;;4CAC5B,eAAc;4CACd,QAAQ;;;;;;wCAGX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,6LAAC,sJAAA,CAAA,MAAG;gDAEF,SAAS,QAAQ,KAAK;gDACtB,MAAM,CAAC,YAAY,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;gDACrC,QAAQ;oDAAC;oDAAG;oDAAG;oDAAG;iDAAE;gDACpB,YAAY;+CAJP,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;wBAY7B,WAAW,QAAQ,CAAC,wBACnB,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;sCACjC,cAAA,6LAAC,oIAAA,CAAA,iBAAc;gCACb,QAAQ;gCACR,WAAU;0CAEV,cAAA,6LAAC,uJAAA,CAAA,WAAQ;oCAAC,QAAQ;wCAAE,KAAK;wCAAI,OAAO;wCAAI,MAAM;wCAAI,QAAQ;oCAAG;;wCAC1D,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;4CAClB,MAAM,UAAU,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,IAAM,CAAC;oDAC7C,MAAM;oDACN,OAAO,QAAQ,IAAI,CAAC,EAAE;oDACtB,SAAS,QAAQ,KAAK;oDACtB,MAAM,CAAC,YAAY,EAAE,MAClB,QAAQ,GACR,OAAO,CAAC,QAAQ,KAChB,WAAW,GAAG,CAAC,CAAC;gDACrB,CAAC;4CAED,qBACE,6LAAC,kJAAA,CAAA,MAAG;gDAEF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,aAAa;gDACb,aAAa;gDACb,cAAc;gDACd,SAAQ;gDACR,SAAQ;gDACR,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GACvB,UAAU,OACN,GAAG,KAAK,EAAE,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACzC;gDAEN,WAAW;gDACX,WAAU;0DAET,QAAQ,GAAG,CAAC,CAAC,OAAO,sBACnB,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,MAAM,IAAI;uDAAjC,CAAC,KAAK,EAAE,OAAO;;;;;+CAlBvB,QAAQ,KAAK;;;;;wCAsBxB;wCACC,6BACC,6LAAC,oIAAA,CAAA,eAAY;4CAAC,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;gDAAC,SAAS;;;;;;;;;;;wCAEtD,4BACC,6LAAC,oIAAA,CAAA,cAAW;4CACV,uBAAS,6LAAC,oIAAA,CAAA,qBAAkB;;;;;4CAC5B,eAAc;4CACd,QAAQ;4CACR,UAAU;4CACV,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASxB,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,UAAU,UAAU,iBACnB;;4BAAE;4BACgB,KAAK,GAAG,CAAC,UAAU,KAAK,EAAE,OAAO,CAAC;4BAAG;4BAAE;4BACtD,UAAU,KAAK,IAAI;0CACpB,6LAAC;gCACC,OAAM;gCACN,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;;kDAEV,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;qDAIZ;;4BAAE;4BACkB,KAAK,GAAG,CAAC,UAAU,KAAK,EAAE,OAAO,CAAC;4BAAG;4BAAE;4BACxD,UAAU,KAAK,IAAI;0CACpB,6LAAC;gCACC,OAAM;gCACN,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;;kDAEV,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1B;KAvTgB", "debugId": null}}, {"offset": {"line": 3746, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/report-data-table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardDescription,\r\n  Card<PERSON>ooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { DataPagination } from \"@/components/ui/data-pagination\";\r\nimport { SearchInput } from \"@/components/ui/search-input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { downloadTableAsExcel } from \"@/lib/export-utils\";\r\nimport {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  SortingState,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n} from \"@tanstack/react-table\";\r\nimport { ArrowDown, ArrowUp, Download } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\ninterface DataTableProps<TData, TValue> {\r\n  columns: ColumnDef<TData, TValue>[];\r\n  data: TData[];\r\n  title?: string;\r\n  description?: string;\r\n  searchPlaceholder?: string;\r\n  searchColumn?: string;\r\n  exportFilename?: string;\r\n  showExport?: boolean;\r\n  showSearch?: boolean;\r\n  showPagination?: boolean;\r\n  rowClassName?: (row: any) => string;\r\n  // Mobile responsiveness props\r\n  mobileBreakpoint?: number;\r\n  enableMobileCards?: boolean;\r\n  // Server-side pagination props\r\n  pagination?: {\r\n    currentPage: number;\r\n    totalPages: number;\r\n    onPageChange: (page: number) => void;\r\n    pageSize?: number;\r\n    onPageSizeChange?: (pageSize: number) => void;\r\n    totalItems?: number;\r\n    isLoading?: boolean;\r\n  };\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport function ReportDataTable<TData, TValue>({\r\n  columns,\r\n  data,\r\n  title,\r\n  description,\r\n  searchPlaceholder = \"Search...\",\r\n  searchColumn,\r\n  exportFilename = \"export\",\r\n  showExport = true,\r\n  showSearch = true,\r\n  showPagination = true,\r\n  rowClassName,\r\n  mobileBreakpoint = 768,\r\n  enableMobileCards = true,\r\n  pagination,\r\n  isLoading = false,\r\n}: DataTableProps<TData, TValue>) {\r\n  const [sorting, setSorting] = useState<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  // Check for mobile view\r\n  useEffect(() => {\r\n    const checkMobile = () => {\r\n      setIsMobile(window.innerWidth < mobileBreakpoint);\r\n    };\r\n\r\n    checkMobile();\r\n    window.addEventListener(\"resize\", checkMobile);\r\n    return () => window.removeEventListener(\"resize\", checkMobile);\r\n  }, [mobileBreakpoint]);\r\n\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    // Use server-side pagination if pagination prop is provided\r\n    ...(pagination\r\n      ? {\r\n          manualPagination: true,\r\n          pageCount: pagination.totalPages,\r\n        }\r\n      : {\r\n          getPaginationRowModel: getPaginationRowModel(),\r\n        }),\r\n    onSortingChange: setSorting,\r\n    getSortedRowModel: getSortedRowModel(),\r\n    onColumnFiltersChange: setColumnFilters,\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      ...(pagination\r\n        ? {\r\n            pagination: {\r\n              pageIndex: pagination.currentPage - 1,\r\n              pageSize: pagination.pageSize || 10,\r\n            },\r\n          }\r\n        : {}),\r\n    },\r\n  });\r\n\r\n  const handleExport = () => {\r\n    downloadTableAsExcel(table, exportFilename);\r\n  };\r\n\r\n  // Render mobile card for a row\r\n  const renderMobileCard = (row: any, index: number) => {\r\n    return (\r\n      <Card key={`mobile-card-${index}`} className=\"mb-4\">\r\n        <CardContent className=\"p-4\">\r\n          <div className=\"space-y-2\">\r\n            {table.getAllColumns().map((column) => {\r\n              const cell = row\r\n                .getVisibleCells()\r\n                .find((c: any) => c.column.id === column.id);\r\n              if (!cell) return null;\r\n\r\n              const value = flexRender(\r\n                cell.column.columnDef.cell,\r\n                cell.getContext()\r\n              );\r\n\r\n              // Skip empty values\r\n              if (!value || value === \"-\" || value === \"\") return null;\r\n\r\n              return (\r\n                <div\r\n                  key={column.id}\r\n                  className=\"flex justify-between items-center\"\r\n                >\r\n                  <span className=\"text-sm font-medium text-muted-foreground\">\r\n                    {typeof column.columnDef.header === \"string\"\r\n                      ? column.columnDef.header\r\n                      : column.id}\r\n                    :\r\n                  </span>\r\n                  <span className=\"text-sm\">{value}</span>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      {(title || description || showSearch || showExport) && (\r\n        <CardHeader className=\"space-y-1\">\r\n          {title && <CardTitle>{title}</CardTitle>}\r\n          {description && <CardDescription>{description}</CardDescription>}\r\n          {(showSearch || showExport) && (\r\n            <div className=\"flex items-center justify-between\">\r\n              {showSearch && searchColumn && (\r\n                <SearchInput\r\n                  placeholder={searchPlaceholder}\r\n                  value={\r\n                    (table\r\n                      .getColumn(searchColumn)\r\n                      ?.getFilterValue() as string) ?? \"\"\r\n                  }\r\n                  onSearch={(value) =>\r\n                    table.getColumn(searchColumn)?.setFilterValue(value)\r\n                  }\r\n                  onClear={() =>\r\n                    table.getColumn(searchColumn)?.setFilterValue(\"\")\r\n                  }\r\n                  mode=\"realtime\"\r\n                  className=\"max-w-sm\"\r\n                />\r\n              )}\r\n              {showExport && (\r\n                <Button variant=\"outline\" onClick={handleExport}>\r\n                  <Download className=\"mr-2 h-4 w-4\" />\r\n                  Export\r\n                </Button>\r\n              )}\r\n            </div>\r\n          )}\r\n        </CardHeader>\r\n      )}\r\n      <CardContent>\r\n        {/* Mobile Card Layout */}\r\n        {isMobile && enableMobileCards ? (\r\n          <div className=\"space-y-4\">\r\n            {isLoading || pagination?.isLoading ? (\r\n              // Show skeleton cards when loading\r\n              Array.from({ length: pagination?.pageSize || 10 }).map(\r\n                (_, index) => (\r\n                  <Card key={`skeleton-card-${index}`} className=\"mb-4\">\r\n                    <CardContent className=\"p-4\">\r\n                      <div className=\"space-y-2\">\r\n                        <div className=\"h-4 bg-muted animate-pulse rounded w-3/4\" />\r\n                        <div className=\"h-4 bg-muted animate-pulse rounded w-1/2\" />\r\n                        <div className=\"h-4 bg-muted animate-pulse rounded w-2/3\" />\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                )\r\n              )\r\n            ) : table.getRowModel().rows?.length ? (\r\n              table\r\n                .getRowModel()\r\n                .rows.map((row, index) => renderMobileCard(row, index))\r\n            ) : (\r\n              <div className=\"text-center py-8 text-muted-foreground\">\r\n                No data available\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          /* Desktop Table Layout */\r\n          <div className=\"rounded-md border overflow-x-auto\">\r\n            <Table>\r\n              <TableHeader>\r\n                {table.getHeaderGroups().map((headerGroup) => (\r\n                  <TableRow key={headerGroup.id}>\r\n                    {headerGroup.headers.map((header) => {\r\n                      return (\r\n                        <TableHead\r\n                          key={header.id}\r\n                          className=\"whitespace-nowrap\"\r\n                        >\r\n                          {header.isPlaceholder ? null : header.column.getCanSort() ? (\r\n                            <div\r\n                              className=\"flex items-center space-x-1 cursor-pointer\"\r\n                              onClick={header.column.getToggleSortingHandler()}\r\n                            >\r\n                              <span>\r\n                                {flexRender(\r\n                                  header.column.columnDef.header,\r\n                                  header.getContext()\r\n                                )}\r\n                              </span>\r\n                              {header.column.getIsSorted() === \"asc\" ? (\r\n                                <ArrowUp className=\"h-4 w-4\" />\r\n                              ) : header.column.getIsSorted() === \"desc\" ? (\r\n                                <ArrowDown className=\"h-4 w-4\" />\r\n                              ) : null}\r\n                            </div>\r\n                          ) : (\r\n                            flexRender(\r\n                              header.column.columnDef.header,\r\n                              header.getContext()\r\n                            )\r\n                          )}\r\n                        </TableHead>\r\n                      );\r\n                    })}\r\n                  </TableRow>\r\n                ))}\r\n              </TableHeader>\r\n              <TableBody>\r\n                {isLoading || pagination?.isLoading ? (\r\n                  // Show skeleton rows when loading\r\n                  Array.from({ length: pagination?.pageSize || 10 }).map(\r\n                    (_, index) => (\r\n                      <TableRow key={`skeleton-${index}`}>\r\n                        {columns.map((_, colIndex) => (\r\n                          <TableCell key={`skeleton-cell-${colIndex}`}>\r\n                            <div className=\"h-4 bg-muted animate-pulse rounded\" />\r\n                          </TableCell>\r\n                        ))}\r\n                      </TableRow>\r\n                    )\r\n                  )\r\n                ) : table.getRowModel().rows?.length ? (\r\n                  table.getRowModel().rows.map((row) => (\r\n                    <TableRow\r\n                      key={row.original.uniqueKey || row.id}\r\n                      data-state={row.getIsSelected() && \"selected\"}\r\n                      className={rowClassName ? rowClassName(row) : \"\"}\r\n                    >\r\n                      {row.getVisibleCells().map((cell) => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))\r\n                ) : (\r\n                  <TableRow>\r\n                    <TableCell\r\n                      colSpan={columns.length}\r\n                      className=\"h-24 text-center\"\r\n                    >\r\n                      No results.\r\n                    </TableCell>\r\n                  </TableRow>\r\n                )}\r\n              </TableBody>\r\n            </Table>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n      {showPagination && (\r\n        <CardFooter className=\"py-4\">\r\n          {pagination ? (\r\n            // Server-side pagination\r\n            <DataPagination\r\n              currentPage={pagination.currentPage}\r\n              totalPages={pagination.totalPages}\r\n              onPageChange={pagination.onPageChange}\r\n              pageSize={pagination.pageSize || 10}\r\n              onPageSizeChange={pagination.onPageSizeChange}\r\n              totalItems={pagination.totalItems || 0}\r\n              isLoading={pagination.isLoading || isLoading}\r\n              showPageSizeSelector={true}\r\n              showItemsInfo={true}\r\n              showFirstLastButtons={true}\r\n            />\r\n          ) : (\r\n            // Client-side pagination (fallback)\r\n            <div className=\"flex items-center justify-between space-x-2 w-full\">\r\n              <div className=\"flex-1 text-sm text-muted-foreground\">\r\n                Showing{\" \"}\r\n                <strong>\r\n                  {table.getState().pagination.pageIndex *\r\n                    table.getState().pagination.pageSize +\r\n                    1}\r\n                  -\r\n                  {Math.min(\r\n                    (table.getState().pagination.pageIndex + 1) *\r\n                      table.getState().pagination.pageSize,\r\n                    table.getFilteredRowModel().rows.length\r\n                  )}\r\n                </strong>{\" \"}\r\n                of <strong>{table.getFilteredRowModel().rows.length}</strong>{\" \"}\r\n                results\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => table.previousPage()}\r\n                  disabled={!table.getCanPreviousPage()}\r\n                >\r\n                  Previous\r\n                </Button>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => table.nextPage()}\r\n                  disabled={!table.getCanNextPage()}\r\n                >\r\n                  Next\r\n                </Button>\r\n                <Select\r\n                  value={table.getState().pagination.pageSize.toString()}\r\n                  onValueChange={(value) => {\r\n                    table.setPageSize(Number(value));\r\n                  }}\r\n                >\r\n                  <SelectTrigger className=\"h-8 w-[70px]\">\r\n                    <SelectValue\r\n                      placeholder={table.getState().pagination.pageSize}\r\n                    />\r\n                  </SelectTrigger>\r\n                  <SelectContent side=\"top\">\r\n                    {[10, 20, 30, 40, 50].map((pageSize) => (\r\n                      <SelectItem key={pageSize} value={pageSize.toString()}>\r\n                        {pageSize}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </CardFooter>\r\n      )}\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAOA;AAQA;AACA;AAAA;AAWA;AAAA;AAAA;AACA;;;AAzCA;;;;;;;;;;;AAuEO,SAAS,gBAA+B,EAC7C,OAAO,EACP,IAAI,EACJ,KAAK,EACL,WAAW,EACX,oBAAoB,WAAW,EAC/B,YAAY,EACZ,iBAAiB,QAAQ,EACzB,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,iBAAiB,IAAI,EACrB,YAAY,EACZ,mBAAmB,GAAG,EACtB,oBAAoB,IAAI,EACxB,UAAU,EACV,YAAY,KAAK,EACa;;IAC9B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;yDAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG;QAAC;KAAiB;IAErB,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;QAC/B,4DAA4D;QAC5D,GAAI,aACA;YACE,kBAAkB;YAClB,WAAW,WAAW,UAAU;QAClC,IACA;YACE,uBAAuB,CAAA,GAAA,wKAAA,CAAA,wBAAqB,AAAD;QAC7C,CAAC;QACL,iBAAiB;QACjB,mBAAmB,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD;QACnC,uBAAuB;QACvB,qBAAqB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD;QACvC,OAAO;YACL;YACA;YACA,GAAI,aACA;gBACE,YAAY;oBACV,WAAW,WAAW,WAAW,GAAG;oBACpC,UAAU,WAAW,QAAQ,IAAI;gBACnC;YACF,IACA,CAAC,CAAC;QACR;IACF;IAEA,MAAM,eAAe;QACnB,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;IAC9B;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC,KAAU;QAClC,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAA8B,WAAU;sBAC3C,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,aAAa,GAAG,GAAG,CAAC,CAAC;wBAC1B,MAAM,OAAO,IACV,eAAe,GACf,IAAI,CAAC,CAAC,IAAW,EAAE,MAAM,CAAC,EAAE,KAAK,OAAO,EAAE;wBAC7C,IAAI,CAAC,MAAM,OAAO;wBAElB,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACrB,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;wBAGjB,oBAAoB;wBACpB,IAAI,CAAC,SAAS,UAAU,OAAO,UAAU,IAAI,OAAO;wBAEpD,qBACE,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;;wCACb,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,WAChC,OAAO,SAAS,CAAC,MAAM,GACvB,OAAO,EAAE;wCAAC;;;;;;;8CAGhB,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;2BATtB,OAAO,EAAE;;;;;oBAYpB;;;;;;;;;;;WA/BK,CAAC,YAAY,EAAE,OAAO;;;;;IAoCrC;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;YACF,CAAC,SAAS,eAAe,cAAc,UAAU,mBAChD,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;oBACnB,uBAAS,6LAAC,mIAAA,CAAA,YAAS;kCAAE;;;;;;oBACrB,6BAAe,6LAAC,mIAAA,CAAA,kBAAe;kCAAE;;;;;;oBACjC,CAAC,cAAc,UAAU,mBACxB,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,8BACb,6LAAC,8IAAA,CAAA,cAAW;gCACV,aAAa;gCACb,OACE,AAAC,MACE,SAAS,CAAC,eACT,oBAA+B;gCAErC,UAAU,CAAC,QACT,MAAM,SAAS,CAAC,eAAe,eAAe;gCAEhD,SAAS,IACP,MAAM,SAAS,CAAC,eAAe,eAAe;gCAEhD,MAAK;gCACL,WAAU;;;;;;4BAGb,4BACC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAQjD,6LAAC,mIAAA,CAAA,cAAW;0BAET,YAAY,kCACX,6LAAC;oBAAI,WAAU;8BACZ,aAAa,YAAY,YACxB,mCAAmC;oBACnC,MAAM,IAAI,CAAC;wBAAE,QAAQ,YAAY,YAAY;oBAAG,GAAG,GAAG,CACpD,CAAC,GAAG,sBACF,6LAAC,mIAAA,CAAA,OAAI;4BAAgC,WAAU;sCAC7C,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;2BALV,CAAC,cAAc,EAAE,OAAO;;;;oCAWrC,MAAM,WAAW,GAAG,IAAI,EAAE,SAC5B,MACG,WAAW,GACX,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,QAAU,iBAAiB,KAAK,wBAElD,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;;;;;2BAM5D,wBAAwB,iBACxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;0CACJ,6LAAC,oIAAA,CAAA,cAAW;0CACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,6LAAC,oIAAA,CAAA,WAAQ;kDACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;4CACxB,qBACE,6LAAC,oIAAA,CAAA,YAAS;gDAER,WAAU;0DAET,OAAO,aAAa,GAAG,OAAO,OAAO,MAAM,CAAC,UAAU,mBACrD,6LAAC;oDACC,WAAU;oDACV,SAAS,OAAO,MAAM,CAAC,uBAAuB;;sEAE9C,6LAAC;sEACE,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;;;;;;wDAGpB,OAAO,MAAM,CAAC,WAAW,OAAO,sBAC/B,6LAAC,+MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;mEACjB,OAAO,MAAM,CAAC,WAAW,OAAO,uBAClC,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;mEACnB;;;;;;2DAGN,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;+CAvBhB,OAAO,EAAE;;;;;wCA4BpB;uCAhCa,YAAY,EAAE;;;;;;;;;;0CAoCjC,6LAAC,oIAAA,CAAA,YAAS;0CACP,aAAa,YAAY,YACxB,kCAAkC;gCAClC,MAAM,IAAI,CAAC;oCAAE,QAAQ,YAAY,YAAY;gCAAG,GAAG,GAAG,CACpD,CAAC,GAAG,sBACF,6LAAC,oIAAA,CAAA,WAAQ;kDACN,QAAQ,GAAG,CAAC,CAAC,GAAG,yBACf,6LAAC,oIAAA,CAAA,YAAS;0DACR,cAAA,6LAAC;oDAAI,WAAU;;;;;;+CADD,CAAC,cAAc,EAAE,UAAU;;;;;uCAFhC,CAAC,SAAS,EAAE,OAAO;;;;gDASpC,MAAM,WAAW,GAAG,IAAI,EAAE,SAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC5B,6LAAC,oIAAA,CAAA,WAAQ;wCAEP,cAAY,IAAI,aAAa,MAAM;wCACnC,WAAW,eAAe,aAAa,OAAO;kDAE7C,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,6LAAC,oIAAA,CAAA,YAAS;0DACP,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;+CAHH,KAAK,EAAE;;;;;uCALpB,IAAI,QAAQ,CAAC,SAAS,IAAI,IAAI,EAAE;;;;8DAezC,6LAAC,oIAAA,CAAA,WAAQ;8CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;wCACR,SAAS,QAAQ,MAAM;wCACvB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUd,gCACC,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACnB,aACC,yBAAyB;8BACzB,6LAAC,iJAAA,CAAA,iBAAc;oBACb,aAAa,WAAW,WAAW;oBACnC,YAAY,WAAW,UAAU;oBACjC,cAAc,WAAW,YAAY;oBACrC,UAAU,WAAW,QAAQ,IAAI;oBACjC,kBAAkB,WAAW,gBAAgB;oBAC7C,YAAY,WAAW,UAAU,IAAI;oBACrC,WAAW,WAAW,SAAS,IAAI;oBACnC,sBAAsB;oBACtB,eAAe;oBACf,sBAAsB;;;;;2BAGxB,oCAAoC;8BACpC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAAuC;gCAC5C;8CACR,6LAAC;;wCACE,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GACpC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,GACpC;wCAAE;wCAEH,KAAK,GAAG,CACP,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,IACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EACtC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;;;;;;;gCAEjC;gCAAI;8CACX,6LAAC;8CAAQ,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;;;;;;gCAAW;gCAAI;;;;;;;sCAGpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,MAAM,YAAY;oCACjC,UAAU,CAAC,MAAM,kBAAkB;8CACpC;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,MAAM,QAAQ;oCAC7B,UAAU,CAAC,MAAM,cAAc;8CAChC;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ;oCACpD,eAAe,CAAC;wCACd,MAAM,WAAW,CAAC,OAAO;oCAC3B;;sDAEA,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDACV,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;sDAGrD,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,MAAK;sDACjB;gDAAC;gDAAI;gDAAI;gDAAI;gDAAI;6CAAG,CAAC,GAAG,CAAC,CAAC,yBACzB,6LAAC,qIAAA,CAAA,aAAU;oDAAgB,OAAO,SAAS,QAAQ;8DAChD;mDADc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAavC;GAnVgB;;QAgCA,yLAAA,CAAA,gBAAa;;;KAhCb", "debugId": null}}]}