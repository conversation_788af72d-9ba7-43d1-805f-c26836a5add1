/**
 * PDF Generator using PDFKit
 *
 * This utility generates PDF documents programmatically using PDFKit.
 * It provides better control over layout and design compared to HTML-based solutions.
 */

const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');
const logger = require('./logger');

// Constants for styling
const COLORS = {
  PRIMARY: '#333333',
  SECONDARY: '#666666',
  ACCENT: '#0066cc',
  LIGHT_GRAY: '#f2f2f2',
  BORDER: '#dddddd'
};

const FONTS = {
  REGULAR: 'Helvetica',
  BOLD: 'Helvetica-Bold',
  OBLIQUE: 'Helvetica-Oblique',
  BOLD_OBLIQUE: 'Helvetica-BoldOblique'
};

/**
 * Format currency with KES symbol
 * @param {number} value - Amount to format
 * @returns {string} Formatted currency string
 */
function formatCurrency(value) {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES'
  }).format(value);
}

/**
 * Format date to local format
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string
 */
function formatDate(date) {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-KE');
}

/**
 * Generate a PDF invoice
 * @param {Object} data - Invoice data
 * @returns {Promise<Buffer>} PDF buffer
 */
async function generateInvoicePDF(data) {
  return new Promise((resolve, reject) => {
    try {
      // Create a PDF document
      const doc = new PDFDocument({
        size: 'A4',
        margin: 50,
        info: {
          Title: `Invoice ${data.invoice_number}`,
          Author: data.company?.name || 'Simba Telecom',
          Subject: 'Invoice',
          Keywords: 'invoice, billing, payment'
        }
      });

      // Collect PDF chunks
      const chunks = [];
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', err => reject(err));

      // Add company logo - positioned at top left
      const logoPath = path.join(__dirname, '../../logo/simbatelecomlogo.png');
      if (fs.existsSync(logoPath)) {
        doc.image(logoPath, 50, 50, { width: 100 });
      } else {
        logger.warn(`Logo not found at path: ${logoPath}`);
      }

      // Add invoice title and details - same title for all invoice types
      doc.font(FONTS.BOLD)
         .fontSize(20)
         .fillColor(COLORS.PRIMARY)
         .text('INVOICE', 400, 50, { align: 'right' });

      // Invoice details box - made taller to accommodate stacked layout
      doc.roundedRect(400, 80, 150, 80, 3)
         .lineWidth(1)
         .stroke(COLORS.BORDER);

      // Tax Date - positioned at the top of the box
      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .fillColor(COLORS.SECONDARY)
         .text('Date:', 410, 90);

      doc.font(FONTS.BOLD)
         .fillColor(COLORS.PRIMARY)
         .fontSize(9)
         .text(formatDate(data.invoice_date), 470, 90, { align: 'right' });

      // Invoice Number - positioned below tax date with more space
      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .fillColor(COLORS.SECONDARY)
         .text('Invoice No:', 410, 120);

      doc.font(FONTS.BOLD)
         .fillColor(COLORS.PRIMARY)
         .fontSize(9)
         .text(data.invoice_number, 410, 140, { width: 130 }); // Left-aligned with width constraint

      // Customer/Supplier details - positioned to avoid overlap with logo
      doc.font(FONTS.BOLD)
         .fontSize(10)
         .text('To:', 50, 170);

      // Determine whether to use customer or supplier details based on invoice type
      const entityType = data.type === 'supplier' ? 'supplier' : 'customer';
      const entity = data[entityType] || {};

      // For supplier invoices, we might want to show contact person
      const contactPerson = entityType === 'supplier' && entity.contact_person
        ? `Attn: ${entity.contact_person}`
        : '';

      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .text(entity.name || '', 50, 185);

      // Show contact person if available (for suppliers)
      if (contactPerson) {
        doc.text(contactPerson, 50, 200);
        doc.text(entity.address || '', 50, 215);
      } else {
        doc.text(entity.address || '', 50, 200);
      }

      // Adjust positions based on whether contact person is shown
      const phoneY = contactPerson ? 230 : 215;
      const emailY = contactPerson ? 245 : 230;

      if (entity.phone) {
        doc.text(`Phone: ${entity.phone}`, 50, phoneY);
      }

      if (entity.email) {
        doc.text(`Email: ${entity.email}`, 50, emailY);
      }

      // Invoice info - dynamically positioned based on entity details
      // Calculate the appropriate Y position based on whether we have contact person and email
      // We already have entityType and entity from above, so just use those variables
      const hasContactPerson = entityType === 'supplier' && entity.contact_person;
      const hasEmail = !!entity.email;
      const hasPhone = !!entity.phone;

      // Base position plus additional space if we have more fields
      let infoY = 270; // Base position

      if (hasContactPerson) infoY += 15; // Add space for contact person
      if (hasEmail && hasPhone) infoY += 5; // Add a bit more space if we have both email and phone

      doc.font(FONTS.REGULAR)
         .fontSize(9) // Consistent font size
         .text('Due Date:', 200, infoY)
         .text('Terms:', 350, infoY);

      doc.font(FONTS.BOLD)
         .fontSize(9) // Consistent font size
         .text(data.lpo_number || 'N/A', 100, infoY)
         .text(formatDate(data.due_date), 250, infoY)
         .text(data.terms || 'N/A', 400, infoY);

      // Items table header
      const tableTop = infoY + 30;
      doc.font(FONTS.BOLD)
         .fillColor(COLORS.PRIMARY);

      // Draw table header background
      doc.rect(50, tableTop, 500, 20)
         .fillColor(COLORS.LIGHT_GRAY)
         .fill();

      // Draw table header text - adjusted for better spacing and to prevent wrapping
      doc.fillColor(COLORS.PRIMARY)
         .fontSize(9) // Smaller font size for headers
         .text('Description', 60, tableTop + 5, { width: 230 })
         .text('Qty', 300, tableTop + 5, { width: 40 })
         .text('Price', 350, tableTop + 5, { width: 80 })
         .text('Amount', 440, tableTop + 5, { width: 80 });

      // Items table rows
      let y = tableTop + 25;
      doc.font(FONTS.REGULAR);

      // Draw items
      if (data.items && data.items.length > 0) {
        data.items.forEach((item, i) => {
          // Alternate row background
          if (i % 2 === 1) {
            doc.rect(50, y - 5, 500, 20)
               .fillColor(COLORS.LIGHT_GRAY)
               .fillOpacity(0.3)
               .fill()
               .fillOpacity(1);
          }

          doc.fillColor(COLORS.PRIMARY)
             .fontSize(9) // Smaller font size for items
             .text(item.description, 60, y, { width: 230 })
             .text(item.quantity.toString(), 300, y, { width: 40 })
             .text(formatCurrency(item.unit_price), 350, y, { width: 80 })
             .text(formatCurrency(item.total_price), 440, y, { width: 80 });

          y += 20;
        });
      }

      // Draw horizontal line
      doc.moveTo(50, y + 10)
         .lineTo(550, y + 10)
         .stroke();

      // Totals - aligned with the new table layout
      y += 20;

      // Create a more compact totals table with labels and values aligned with the Amount column
      const totalsLabelX = 350;  // Align with Price column
      const totalsValueX = 440;  // Align with Amount column

      // Subtotal
      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .text('Subtotal:', totalsLabelX, y);

      doc.font(FONTS.BOLD)
         .fontSize(9)
         .text(formatCurrency(data.subtotal || 0), totalsValueX, y, { width: 80 });

      // VAT Amount
      y += 20;
      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .text('VAT Amount:', totalsLabelX, y);

      doc.font(FONTS.BOLD)
         .fontSize(9)
         .text(formatCurrency(data.vat_amount || 0), totalsValueX, y, { width: 80 });

      // Add a line before grand total
      y += 10;
      doc.moveTo(totalsLabelX, y)
         .lineTo(totalsValueX + 80, y)  // Extend line to cover the value
         .stroke();

      // Grand Total
      y += 15;
      doc.font(FONTS.BOLD)
         .fontSize(9)  // Keep consistent with other text
         .text('Grand Total:', totalsLabelX, y);

      doc.font(FONTS.BOLD)
         .fontSize(9)
         .text(formatCurrency(data.total_amount || 0), totalsValueX, y, { width: 80 });

      // KRA section
      y += 50;
      if (data.kra_qr_code_image) {
        doc.image(data.kra_qr_code_image, 50, y, { width: 80 });
      }

      doc.fontSize(8)
         .text(`CU SERIAL NUMBER: ${data.cu_serial_number || 'N/A'}`, 140, y)
         .text(`CU INVOICE NUMBER: ${data.cu_invoice_number || 'N/A'}`, 140, y + 15);


      // Footer
      doc.fontSize(10)
         .fillColor(COLORS.SECONDARY)
         .text('THE CUSTOMER SERVICE PEOPLE!', 0, 700, { align: 'center' });

      // Finalize the PDF
      doc.end();
    } catch (error) {
      logger.error(`Error generating PDF: ${error.message}`);
      reject(error);
    }
  });
}

/**
 * Generate a PDF for a credit note
 * @param {Object} data - Credit note data
 * @returns {Promise<Buffer>} PDF buffer
 */
async function generateCreditNotePDF(data) {
  return new Promise((resolve, reject) => {
    try {
      // Create a PDF document
      const doc = new PDFDocument({
        size: 'A4',
        margin: 50,
        info: {
          Title: `Credit Note ${data.credit_note_number}`,
          Author: 'Simba Telecom',
          Subject: 'Credit Note',
          Keywords: 'credit note, refund, adjustment'
        }
      });

      // Collect PDF chunks
      const chunks = [];
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', err => reject(err));

      // Add company logo - positioned at top left
      const logoPath = path.join(__dirname, '../../logo/simbatelecomlogo.png');
      if (fs.existsSync(logoPath)) {
        doc.image(logoPath, 50, 50, { width: 100 });
      } else {
        logger.warn(`Logo not found at path: ${logoPath}`);
      }

      // Add credit note title
      doc.font(FONTS.BOLD)
         .fontSize(20)
         .fillColor(COLORS.PRIMARY)
         .text('CREDIT NOTE', 400, 50, { align: 'right' });

      // Credit note details box
      doc.roundedRect(400, 80, 150, 80, 3)
         .lineWidth(1)
         .stroke(COLORS.BORDER);

      // Credit Note Date
      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .fillColor(COLORS.SECONDARY)
         .text('Date:', 410, 90);

      doc.font(FONTS.BOLD)
         .fillColor(COLORS.PRIMARY)
         .fontSize(9)
         .text(formatDate(data.credit_note_date), 470, 90, { align: 'right' });

      // Credit Note Number
      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .fillColor(COLORS.SECONDARY)
         .text('Credit Note No:', 410, 110);

      doc.font(FONTS.BOLD)
         .fillColor(COLORS.PRIMARY)
         .fontSize(9)
         .text(data.credit_note_number, 410, 130, { width: 130 });

      // Reference information
      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .fillColor(COLORS.SECONDARY)
         .text('Reference:', 410, 150);

      doc.font(FONTS.BOLD)
         .fillColor(COLORS.PRIMARY)
         .fontSize(9)
         .text(`${data.reference_type.toUpperCase()} #${data.reference_number}`, 410, 170, { width: 130 });

      // Customer/Supplier details
      doc.font(FONTS.BOLD)
         .fontSize(10)
         .text('To:', 50, 170);

      // Determine whether to use customer or supplier details
      const entityType = data.customer_id ? 'customer' : 'supplier';
      const entity = data[entityType] || {};

      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .text(entity.name || '', 50, 185);
      doc.text(entity.address || '', 50, 200);

      if (entity.phone) {
        doc.text(`Phone: ${entity.phone}`, 50, 215);
      }

      if (entity.email) {
        doc.text(`Email: ${entity.email}`, 50, 230);
      }

      // Reason for credit note
      const reasonY = 260;
      doc.font(FONTS.BOLD)
         .fontSize(10)
         .text('Reason for Credit Note:', 50, reasonY);

      const reasons = {
        'return': 'Product Return',
        'price_adjustment': 'Price Adjustment',
        'quantity_adjustment': 'Quantity Adjustment',
        'discount': 'Discount',
        'cancellation': 'Cancellation',
        'other': 'Other'
      };

      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .text(reasons[data.reason] || data.reason, 50, reasonY + 15);

      if (data.reason_details) {
        doc.text(data.reason_details, 50, reasonY + 30, { width: 500 });
      }

      // Items table header
      const tableTop = reasonY + 60;
      doc.font(FONTS.BOLD)
         .fillColor(COLORS.PRIMARY);

      // Draw table header background
      doc.rect(50, tableTop, 500, 20)
         .fillColor(COLORS.LIGHT_GRAY)
         .fill();

      // Draw table header text
      doc.fillColor(COLORS.PRIMARY)
         .fontSize(9)
         .text('Description', 60, tableTop + 5, { width: 230 })
         .text('Qty', 300, tableTop + 5, { width: 40 })
         .text('Price', 350, tableTop + 5, { width: 80 })
         .text('Amount', 440, tableTop + 5, { width: 80 });

      // Items table rows
      let y = tableTop + 25;
      doc.font(FONTS.REGULAR);

      // Draw items
      if (data.items && data.items.length > 0) {
        data.items.forEach((item, i) => {
          // Alternate row background
          if (i % 2 === 1) {
            doc.rect(50, y - 5, 500, 20)
               .fillColor(COLORS.LIGHT_GRAY)
               .fillOpacity(0.3)
               .fill()
               .fillOpacity(1);
          }

          doc.fillColor(COLORS.PRIMARY)
             .fontSize(9)
             .text(item.description, 60, y, { width: 230 })
             .text(item.quantity.toString(), 300, y, { width: 40 })
             .text(formatCurrency(item.unit_price), 350, y, { width: 80 })
             .text(formatCurrency(item.total_price), 440, y, { width: 80 });

          y += 20;
        });
      }

      // Draw horizontal line
      doc.moveTo(50, y + 10)
         .lineTo(550, y + 10)
         .stroke();

      // Totals
      y += 20;
      const totalsLabelX = 350;
      const totalsValueX = 440;

      // Subtotal
      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .text('Subtotal:', totalsLabelX, y);

      doc.font(FONTS.BOLD)
         .fontSize(9)
         .text(formatCurrency(data.subtotal || 0), totalsValueX, y, { width: 80 });

      // VAT Amount
      y += 20;
      doc.font(FONTS.REGULAR)
         .fontSize(9)
         .text('VAT Amount:', totalsLabelX, y);

      doc.font(FONTS.BOLD)
         .fontSize(9)
         .text(formatCurrency(data.vat_amount || 0), totalsValueX, y, { width: 80 });

      // Add a line before grand total
      y += 10;
      doc.moveTo(totalsLabelX, y)
         .lineTo(totalsValueX + 80, y)
         .stroke();

      // Grand Total
      y += 15;
      doc.font(FONTS.BOLD)
         .fontSize(9)
         .text('Grand Total:', totalsLabelX, y);

      doc.font(FONTS.BOLD)
         .fontSize(9)
         .text(formatCurrency(data.total_amount || 0), totalsValueX, y, { width: 80 });

      // KRA section
      y += 50;
      if (data.kra_verification_code) {
        doc.fontSize(9)
           .text(`KRA Verification Code: ${data.kra_verification_code}`, 50, y)
           .text(`KRA Fiscal Receipt Number: ${data.kra_fiscal_receipt_number || 'N/A'}`, 50, y + 15);
      }

      // Footer
      doc.fontSize(10)
         .fillColor(COLORS.SECONDARY)
         .text('Thank you for your business!', 0, 700, { align: 'center' });

      // Finalize the PDF
      doc.end();
    } catch (error) {
      logger.error(`Error generating credit note PDF: ${error.message}`);
      reject(error);
    }
  });
}

/**
 * Generate a PDF document based on template type
 * @param {string} templateType - Type of template (e.g., 'invoice', 'purchase-order', 'credit-note')
 * @param {Object} data - Data to populate the template
 * @returns {Promise<Buffer>} PDF buffer
 */
async function generatePDF(templateType, data) {
  try {
    logger.info(`Generating PDF for template type: ${templateType}`);

    switch (templateType) {
      case 'invoice':
        return await generateInvoicePDF(data);
      case 'credit-note':
        return await generateCreditNotePDF(data);
      // Add more template types as needed
      default:
        throw new Error(`Unsupported template type: ${templateType}`);
    }
  } catch (error) {
    logger.error(`Error generating PDF: ${error.message}`);
    throw error;
  }
}

/**
 * Generate a PDF for an invoice (direct function for controllers)
 * @param {Object} invoice - The invoice data
 * @returns {Promise<Buffer>} PDF buffer
 */
async function generateInvoicePdf(invoice) {
  return generatePDF('invoice', invoice);
}

/**
 * Generate a PDF for a credit note (direct function for controllers)
 * @param {Object} creditNote - The credit note data
 * @returns {Promise<Buffer>} PDF buffer
 */
async function generateCreditNotePdf(creditNote) {
  return generatePDF('credit-note', creditNote);
}

module.exports = {
  generatePDF,
  generateInvoicePdf,
  generateCreditNotePdf
};
