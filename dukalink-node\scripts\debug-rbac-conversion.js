const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config();

// Add the project root to the require path
const projectRoot = path.resolve(__dirname, '..');
process.env.NODE_PATH = projectRoot;
require('module').Module._initPaths();

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${new Date().toISOString()} - ${msg}`)
};

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dukalink_api',
  port: process.env.DB_PORT || 3306
};

async function debugRbacConversion() {
  let connection;
  
  try {
    logger.info('🔍 Debugging RBAC grant conversion...');
    
    // Connect to database
    logger.info('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Step 1: Get raw grants from database
    logger.info('=== STEP 1: Getting raw grants from database ===');
    const [rawGrants] = await connection.execute(`
      SELECT role, resource, action, attributes 
      FROM rbac_grants 
      WHERE role = 'float_manager' 
      ORDER BY resource, action
      LIMIT 10
    `);
    
    logger.info(`✓ Found ${rawGrants.length} raw grants for float_manager (showing first 10):`);
    rawGrants.forEach(grant => {
      logger.info(`  - ${grant.role} -> ${grant.action} ${grant.resource} (${typeof grant.attributes})`);
    });
    
    // Step 2: Test the RBAC service conversion manually
    logger.info('=== STEP 2: Testing RBAC service conversion ===');
    
    try {
      const rbacService = require('../src/services/rbac.service');
      
      // Get the raw grants from the service
      const dbGrants = await rbacService.loadGrantsFromDatabase();
      
      logger.info(`✓ RBAC service loaded ${dbGrants.length} total grants`);
      
      // Filter float_manager grants
      const floatManagerGrants = dbGrants.filter(g => g.role === 'float_manager');
      logger.info(`✓ Found ${floatManagerGrants.length} float_manager grants after processing`);
      
      if (floatManagerGrants.length > 0) {
        logger.info('Sample processed float_manager grants:');
        floatManagerGrants.slice(0, 5).forEach(grant => {
          logger.info(`  - ${grant.role} -> ${grant.action} ${grant.resource}`);
          logger.info(`    Attributes: ${JSON.stringify(grant.attributes)}`);
        });
        
        // Step 3: Test the conversion to grants object
        logger.info('=== STEP 3: Testing conversion to grants object ===');
        
        const grantsObject = rbacService.convertGrantsToObject(floatManagerGrants);
        
        if (grantsObject['float_manager']) {
          logger.success('✅ float_manager found in grants object!');
          
          const floatManagerResources = Object.keys(grantsObject['float_manager']);
          logger.info(`✓ float_manager has ${floatManagerResources.length} resources:`);
          floatManagerResources.slice(0, 10).forEach(resource => {
            const actions = Object.keys(grantsObject['float_manager'][resource]);
            logger.info(`  - ${resource}: ${actions.join(', ')}`);
          });
          
          // Check specific approval permissions
          if (grantsObject['float_manager']['banking_approval']) {
            logger.success('✅ banking_approval resource found!');
            const actions = Object.keys(grantsObject['float_manager']['banking_approval']);
            logger.info(`    Actions: ${actions.join(', ')}`);
          } else {
            logger.error('❌ banking_approval resource NOT found');
          }
          
          if (grantsObject['float_manager']['banking_rejection']) {
            logger.success('✅ banking_rejection resource found!');
            const actions = Object.keys(grantsObject['float_manager']['banking_rejection']);
            logger.info(`    Actions: ${actions.join(', ')}`);
          } else {
            logger.error('❌ banking_rejection resource NOT found');
          }
          
        } else {
          logger.error('❌ float_manager NOT found in grants object');
          logger.info('Available roles in grants object:');
          Object.keys(grantsObject).forEach(role => {
            logger.info(`  - ${role}`);
          });
        }
        
        // Step 4: Test AccessControl instantiation
        logger.info('=== STEP 4: Testing AccessControl instantiation ===');
        
        try {
          const CustomAccessControl = require('../src/utils/custom-access-control');
          
          // Test with just float_manager grants
          const testGrants = { 'float_manager': grantsObject['float_manager'] || {} };
          logger.info(`Creating AccessControl with float_manager grants...`);
          
          const testAC = new CustomAccessControl(testGrants);
          const testRoles = Object.keys(testAC.getGrants());
          
          logger.info(`✓ Test AccessControl created with ${testRoles.length} roles:`);
          testRoles.forEach(role => {
            logger.info(`  - ${role}`);
          });
          
          if (testRoles.includes('float_manager')) {
            logger.success('✅ float_manager successfully added to AccessControl!');
            
            // Test the specific permissions
            try {
              const approvalPerm = testAC.can('float_manager').createAny('banking_approval');
              logger.info(`✓ banking_approval test: ${approvalPerm.granted ? 'GRANTED ✅' : 'DENIED ❌'}`);
              
              const rejectionPerm = testAC.can('float_manager').createAny('banking_rejection');
              logger.info(`✓ banking_rejection test: ${rejectionPerm.granted ? 'GRANTED ✅' : 'DENIED ❌'}`);
              
            } catch (permError) {
              logger.error(`Permission test failed: ${permError.message}`);
            }
            
          } else {
            logger.error('❌ float_manager NOT found in test AccessControl');
          }
          
        } catch (acError) {
          logger.error(`AccessControl instantiation failed: ${acError.message}`);
          logger.error(acError.stack);
        }
        
      } else {
        logger.error('❌ No float_manager grants found after RBAC service processing');
      }
      
    } catch (rbacError) {
      logger.error(`RBAC service test failed: ${rbacError.message}`);
      logger.error(rbacError.stack);
    }
    
    // Step 5: Check if there are any role validation issues
    logger.info('=== STEP 5: Checking role validation ===');
    
    // Check if float_manager role exists in roles table
    const [roleCheck] = await connection.execute(`
      SELECT id, name FROM roles WHERE name = 'float_manager'
    `);
    
    if (roleCheck.length > 0) {
      logger.success(`✅ float_manager role exists in roles table (ID: ${roleCheck[0].id})`);
    } else {
      logger.error('❌ float_manager role NOT found in roles table');
      logger.warn('This could be why the RBAC system is excluding it');
    }
    
    logger.info('=== DEBUG SUMMARY ===');
    logger.info('✓ Raw database grants checked');
    logger.info('✓ RBAC service processing tested');
    logger.info('✓ Grants object conversion tested');
    logger.info('✓ AccessControl instantiation tested');
    logger.info('✓ Role validation checked');
    
  } catch (error) {
    logger.error(`Debug failed: ${error.message}`);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info('Database connection closed.');
    }
  }
}

// Execute the debug
if (require.main === module) {
  debugRbacConversion()
    .then(() => {
      logger.success('🎉 RBAC conversion debug completed!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`❌ RBAC conversion debug failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { debugRbacConversion };
