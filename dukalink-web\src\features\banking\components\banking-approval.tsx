"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Banking } from "@/types";
import {
  useApproveBankingTransaction,
  useRejectBankingTransaction,
} from "../hooks/use-banking";
import { CheckCircle, XCircle } from "lucide-react";
import { useCurrentUser } from "@/features/auth/hooks/use-auth";

interface BankingApprovalProps {
  banking: Banking;
  onUpdate: () => void;
}

export function BankingApproval({ banking, onUpdate }: BankingApprovalProps) {
  const { data: user } = useCurrentUser();
  const [rejectionReason, setRejectionReason] = useState("");
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);

  const approveMutation = useApproveBankingTransaction();
  const rejectMutation = useRejectBankingTransaction();

  // Check if user can approve/reject (company_admin, super_admin, or float_manager)
  const canApprove =
    user?.role_name === "company_admin" ||
    user?.role_name === "super_admin" ||
    user?.role_name === "float_manager";

  // Check if transaction is pending
  const isPending = banking.status === "pending";

  const handleApprove = async () => {
    await approveMutation.mutateAsync(banking.id);
    onUpdate();
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) return;

    await rejectMutation.mutateAsync({
      id: banking.id,
      rejectionReason,
    });

    setIsRejectDialogOpen(false);
    setRejectionReason("");
    onUpdate();
  };

  if (!canApprove || !isPending) {
    return null;
  }

  return (
    <div className="flex gap-2">
      <Button
        variant="default"
        onClick={handleApprove}
        disabled={approveMutation.isPending}
      >
        <CheckCircle className="mr-2 h-4 w-4" />
        Approve
      </Button>

      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="destructive" disabled={rejectMutation.isPending}>
            <XCircle className="mr-2 h-4 w-4" />
            Reject
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Banking Transaction</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this banking transaction.
            </DialogDescription>
          </DialogHeader>
          <Textarea
            placeholder="Rejection reason"
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            className="min-h-[100px]"
          />
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRejectDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={!rejectionReason.trim() || rejectMutation.isPending}
            >
              Reject
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
