{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-permissions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCurrentUser } from \"./use-auth\";\r\nimport { usePermissionContext } from \"../context/permission-context\";\r\n\r\n// Define constants for resources and actions\r\nexport const RESOURCES = {\r\n  USERS: \"users\",\r\n  ROLES: \"roles\",\r\n  PERMISSIONS: \"permissions\",\r\n  PRODUCTS: \"products\",\r\n  CATEGORIES: \"categories\",\r\n  BRANDS: \"brands\",\r\n  INVENTORY: \"inventory\",\r\n  STOCK_ITEMS: \"stock_items\",\r\n  BRANCHES: \"branches\",\r\n  EMPLOYEES: \"employees\",\r\n  BANKING: \"banking\",\r\n  EXPENSES: \"expenses\",\r\n  EXPENSE_FIRST_APPROVAL: \"expense_first_approval\",\r\n  EXPENSE_SECOND_APPROVAL: \"expense_second_approval\",\r\n  SALES: \"sales\",\r\n  POS_SESSIONS: \"pos_sessions\",\r\n  CUSTOMERS: \"customers\",\r\n  PROFILE: \"profile\",\r\n  STOCK_LOCATIONS: \"stock_locations\",\r\n};\r\n\r\nexport const ACTIONS = {\r\n  CREATE: \"create\",\r\n  READ: \"read\",\r\n  UPDATE: \"update\",\r\n  DELETE: \"delete\",\r\n};\r\n\r\nexport const SCOPE = {\r\n  ANY: \"any\",\r\n  OWN: \"own\",\r\n};\r\n\r\nexport function usePermissions() {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const {\r\n    permissions,\r\n    isLoading: isPermissionsLoading,\r\n    hasPermission,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n  } = usePermissionContext();\r\n\r\n  // Check if the user is a company admin\r\n  const isCompanyAdmin = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the company_admin role\r\n    return (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"tenant_admin\" ||\r\n      user.role_name === \"super_admin\"\r\n    );\r\n  };\r\n\r\n  // Check if the user is a branch manager\r\n  const isBranchManager = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the branch_manager role\r\n    return user.role_name === \"branch_manager\";\r\n  };\r\n\r\n  // Check if the user can manage stock locations (create, update, delete)\r\n  const canManageStockLocations = (): boolean => {\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) ||\r\n      isCompanyAdmin()\r\n    );\r\n  };\r\n\r\n  // Check if the user can view stock locations\r\n  const canViewStockLocations = (): boolean => {\r\n    // Check for explicit permission or fallback to any authenticated user\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) ||\r\n      !!user\r\n    );\r\n  };\r\n\r\n  // Check if the user can perform an action on a resource\r\n  const can = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    return hasPermission(resource, action, scope);\r\n  };\r\n\r\n  // Check if the user has any of the specified permissions\r\n  const hasAnyPermission = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.some(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  // Check if the user has all of the specified permissions\r\n  const hasAllPermissions = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.every(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  return {\r\n    isCompanyAdmin,\r\n    isBranchManager,\r\n    canManageStockLocations,\r\n    canViewStockLocations,\r\n    can,\r\n    hasPermission,\r\n    hasAnyPermission,\r\n    hasAllPermissions,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n    permissions, // Expose the raw permissions for debugging\r\n    isLoading: isUserLoading || isPermissionsLoading,\r\n    RESOURCES,\r\n    ACTIONS,\r\n    SCOPE,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;AAMO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;IACV,wBAAwB;IACxB,yBAAyB;IACzB,OAAO;IACP,cAAc;IACd,WAAW;IACX,SAAS;IACT,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,QAAQ;IACnB,KAAK;IACL,KAAK;AACP;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EACJ,WAAW,EACX,WAAW,oBAAoB,EAC/B,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,4JAAA,CAAA,uBAAoB,AAAD;IAEvB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,+CAA+C;QAC/C,OACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK;IAEvB;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,gDAAgD;QAChD,OAAO,KAAK,SAAS,KAAK;IAC5B;IAEA,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE;IAEJ;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,sEAAsE;QACtE,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,KAChE,CAAC,CAAC;IAEN;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,UACA,QACA,QAAuB,KAAK;QAE5B,OAAO,cAAc,UAAU,QAAQ;IACzC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,CACvB;QAMA,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC1D,cAAc,UAAU,QAAQ;IAEpC;IAEA,yDAAyD;IACzD,MAAM,oBAAoB,CACxB;QAMA,OAAO,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC3D,cAAc,UAAU,QAAQ;IAEpC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,iBAAiB;QAC5B;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-permissions.ts"], "sourcesContent": ["/**\r\n * Route Permission Mapping\r\n *\r\n * This file maps routes to the permissions required to access them.\r\n * Each key represents a route pattern, and the value is the permission required.\r\n */\r\n\r\nimport { NavigationPermission } from \"./navigation-permissions\";\r\n\r\nexport const routePermissions: Record<string, NavigationPermission> = {\r\n  // Dashboard routes\r\n  \"/dashboard\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/company\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/branch\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/finance\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/operations\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/stock\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/float\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/pos\": { resource: \"dashboard\", action: \"view\" },\r\n\r\n  // User management routes\r\n  \"/users\": { resource: \"users\", action: \"view\" },\r\n  \"/users/create\": { resource: \"users\", action: \"create\" },\r\n  \"/users/[id]\": { resource: \"users\", action: \"view\" },\r\n  \"/users/[id]/edit\": { resource: \"users\", action: \"update\" },\r\n\r\n  // Role management routes\r\n  \"/roles\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/create\": { resource: \"roles\", action: \"create\" },\r\n  \"/roles/[id]\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/[id]/edit\": { resource: \"roles\", action: \"update\" },\r\n\r\n  // RBAC routes\r\n  \"/rbac\": { resource: \"permissions\", action: \"manage\" },\r\n\r\n  // Branch management routes\r\n  \"/branches\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/create\": { resource: \"branches\", action: \"create\" },\r\n  \"/branches/[id]\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/[id]/edit\": { resource: \"branches\", action: \"update\" },\r\n\r\n  // Location management routes\r\n  \"/locations\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/locations/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n  \"/location-guides\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/location-guides/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n\r\n  // Employee management routes\r\n  \"/employees\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/create\": { resource: \"employees\", action: \"create\" },\r\n  \"/employees/[id]\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/[id]/edit\": { resource: \"employees\", action: \"update\" },\r\n\r\n  // Product management routes\r\n  \"/products\": { resource: \"products\", action: \"view\" },\r\n  \"/products/create\": { resource: \"products\", action: \"create\" },\r\n  \"/products/[id]\": { resource: \"products\", action: \"view\" },\r\n  \"/products/[id]/edit\": { resource: \"products\", action: \"update\" },\r\n\r\n  // Category management routes\r\n  \"/categories\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/create\": { resource: \"categories\", action: \"create\" },\r\n  \"/categories/[id]\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/[id]/edit\": { resource: \"categories\", action: \"update\" },\r\n\r\n  // Brand management routes\r\n  \"/brands\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/create\": { resource: \"brands\", action: \"create\" },\r\n  \"/brands/[id]\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/[id]/edit\": { resource: \"brands\", action: \"update\" },\r\n\r\n  // Inventory management routes\r\n  \"/inventory\": { resource: \"inventory\", action: \"view\" },\r\n  \"/inventory/transfers\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/bulk-transfer\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/reports\": { resource: \"inventory\", action: \"report\" },\r\n  \"/inventory/stock-cards\": { resource: \"inventory\", action: \"view\" },\r\n\r\n  // Banking routes\r\n  \"/banking\": { resource: \"banking\", action: \"view\" },\r\n  \"/banking/summary\": { resource: \"banking\", action: \"view\" },\r\n\r\n  // MPESA routes\r\n  \"/mpesa\": { resource: \"mpesa\", action: \"view\" },\r\n  \"/mpesa/transactions\": { resource: \"mpesa\", action: \"view\" },\r\n\r\n  // Float management routes\r\n  \"/float\": { resource: \"float\", action: \"view\" },\r\n  \"/float/movements\": { resource: \"float\", action: \"view\" },\r\n  \"/float/reconciliations\": { resource: \"float\", action: \"view\" },\r\n  \"/float/topups\": { resource: \"float\", action: \"view\" },\r\n\r\n  // DSA routes\r\n  \"/dsa\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/agents\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/assignments\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/reconciliations\": { resource: \"dsa\", action: \"view\" },\r\n\r\n  // Phone repairs routes\r\n  \"/phone-repairs\": { resource: \"phone_repairs\", action: \"view\" },\r\n  \"/phone-repairs/[id]\": { resource: \"phone_repairs\", action: \"view\" },\r\n\r\n  // Expense management routes\r\n  \"/expenses\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/create\": { resource: \"expenses\", action: \"create\" },\r\n  \"/expenses/[id]\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/[id]/edit\": { resource: \"expenses\", action: \"update\" },\r\n  \"/expense-analytics\": { resource: \"expenses\", action: \"view\" },\r\n\r\n  // Credit Note routes\r\n  \"/credit-notes\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/new\": { resource: \"invoices\", action: \"create\" },\r\n  \"/credit-notes/[id]\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/[id]/edit\": { resource: \"invoices\", action: \"update\" },\r\n\r\n  // Report routes\r\n  \"/reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-summary\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-item\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-category\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-employee\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-payment-type\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-banking\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/cash-status\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/running-balances\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-transactions\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/phone-repairs\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/expense-reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/dsa-sales\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/receipts\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/shifts\": { resource: \"reports\", action: \"view\" },\r\n\r\n  // Settings routes\r\n  \"/settings\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/system\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/company\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/payment-methods\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/health\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // Profile route\r\n  \"/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // POS routes\r\n  \"/pos\": { resource: \"pos\", action: \"view\" },\r\n  \"/pos/sessions\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]/edit\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/close\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/shift-closing\": {\r\n    resource: \"pos_sessions\",\r\n    action: \"view\",\r\n  },\r\n\r\n  // Sales routes\r\n  \"/sales\": { resource: \"sales\", action: \"view\" },\r\n  \"/sales/[id]\": { resource: \"sales\", action: \"view\" },\r\n\r\n  // Customer routes\r\n  \"/customers\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/create\": { resource: \"customers\", action: \"create\" },\r\n  \"/customers/[id]\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/[id]/edit\": { resource: \"customers\", action: \"update\" },\r\n\r\n  // Procurement routes\r\n  \"/procurement\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests/new\": { resource: \"procurement\", action: \"create\" },\r\n  \"/procurement/receipts\": { resource: \"procurement\", action: \"view\" },\r\n\r\n  // Supplier routes\r\n  \"/suppliers\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/create\": { resource: \"suppliers\", action: \"create\" },\r\n  \"/suppliers/[id]\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/[id]/edit\": { resource: \"suppliers\", action: \"update\" },\r\n\r\n  // Tenant routes (Super Admin only)\r\n  \"/tenants\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/create\": { resource: \"tenants\", action: \"create\" },\r\n  \"/tenants/[id]\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/[id]/edit\": { resource: \"tenants\", action: \"update\" },\r\n\r\n  // Super Admin routes\r\n  \"/super-admin\": { resource: \"tenants\", action: \"view\" },\r\n\r\n  // Debug routes\r\n  \"/permission-debug\": { resource: \"settings\", action: \"manage\" },\r\n};\r\n\r\n/**\r\n * Get the permission required for a route\r\n * @param route The route to check\r\n * @returns The permission required for the route, or null if no permission is required\r\n */\r\nexport function getRoutePermission(route: string): NavigationPermission | null {\r\n  // First try exact match\r\n  if (routePermissions[route]) {\r\n    return routePermissions[route];\r\n  }\r\n\r\n  // Then try dynamic routes\r\n  for (const [pattern, permission] of Object.entries(routePermissions)) {\r\n    if (pattern.includes(\"[\") && matchDynamicRoute(route, pattern)) {\r\n      return permission;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Check if a route matches a dynamic route pattern\r\n * @param route The route to check\r\n * @param pattern The pattern to match against\r\n * @returns Whether the route matches the pattern\r\n */\r\nfunction matchDynamicRoute(route: string, pattern: string): boolean {\r\n  const routeParts = route.split(\"/\");\r\n  const patternParts = pattern.split(\"/\");\r\n\r\n  if (routeParts.length !== patternParts.length) {\r\n    return false;\r\n  }\r\n\r\n  for (let i = 0; i < patternParts.length; i++) {\r\n    if (patternParts[i].startsWith(\"[\") && patternParts[i].endsWith(\"]\")) {\r\n      // This is a dynamic part, so it matches anything\r\n      continue;\r\n    }\r\n\r\n    if (patternParts[i] !== routeParts[i]) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAIM,MAAM,mBAAyD;IACpE,mBAAmB;IACnB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC7D,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,kBAAkB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,cAAc;IACd,SAAS;QAAE,UAAU;QAAe,QAAQ;IAAS;IAErD,2BAA2B;IAC3B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAClE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,2BAA2B;QAAE,UAAU;QAAa,QAAQ;IAAS;IACrE,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,8BAA8B;QAAE,UAAU;QAAa,QAAQ;IAAS;IAExE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,eAAe;QAAE,UAAU;QAAc,QAAQ;IAAO;IACxD,sBAAsB;QAAE,UAAU;QAAc,QAAQ;IAAS;IACjE,oBAAoB;QAAE,UAAU;QAAc,QAAQ;IAAO;IAC7D,yBAAyB;QAAE,UAAU;QAAc,QAAQ;IAAS;IAEpE,0BAA0B;IAC1B,WAAW;QAAE,UAAU;QAAU,QAAQ;IAAO;IAChD,kBAAkB;QAAE,UAAU;QAAU,QAAQ;IAAS;IACzD,gBAAgB;QAAE,UAAU;QAAU,QAAQ;IAAO;IACrD,qBAAqB;QAAE,UAAU;QAAU,QAAQ;IAAS;IAE5D,8BAA8B;IAC9B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAW;IACpE,4BAA4B;QAAE,UAAU;QAAa,QAAQ;IAAW;IACxE,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAChE,0BAA0B;QAAE,UAAU;QAAa,QAAQ;IAAO;IAElE,iBAAiB;IACjB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,oBAAoB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE1D,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,uBAAuB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAE3D,0BAA0B;IAC1B,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAO;IACxD,0BAA0B;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9D,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAErD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,eAAe;QAAE,UAAU;QAAO,QAAQ;IAAO;IACjD,oBAAoB;QAAE,UAAU;QAAO,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAE1D,uBAAuB;IACvB,kBAAkB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAC9D,uBAAuB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAEnE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAChE,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAE7D,qBAAqB;IACrB,iBAAiB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACxD,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC7D,2BAA2B;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEpE,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,kCAAkC;QAAE,UAAU;QAAW,QAAQ;IAAO;IACxE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,wBAAwB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC9D,6BAA6B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACnE,+BAA+B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACrE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,4BAA4B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClE,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC5D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC3D,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEzD,kBAAkB;IAClB,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,6BAA6B;QAAE,UAAU;QAAY,QAAQ;IAAS;IACtE,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC3D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE3D,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAElD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,iBAAiB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IAC5D,sBAAsB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IACjE,2BAA2B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACxE,4BAA4B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACzE,oCAAoC;QAClC,UAAU;QACV,QAAQ;IACV;IAEA,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IAEnD,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAC1D,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IACnE,6BAA6B;QAAE,UAAU;QAAe,QAAQ;IAAS;IACzE,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAEnE,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,mCAAmC;IACnC,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAC3D,iBAAiB;QAAE,UAAU;QAAW,QAAQ;IAAO;IACvD,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAE9D,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEtD,eAAe;IACf,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;AAChE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,OAAO,gBAAgB,CAAC,MAAM;IAChC;IAEA,0BAA0B;IAC1B,KAAK,MAAM,CAAC,SAAS,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAmB;QACpE,IAAI,QAAQ,QAAQ,CAAC,QAAQ,kBAAkB,OAAO,UAAU;YAC9D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,KAAa,EAAE,OAAe;IACvD,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,eAAe,QAAQ,KAAK,CAAC;IAEnC,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC7C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,IAAI,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;YAEpE;QACF;QAEA,IAAI,YAAY,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/role-guard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\nimport { getRoutePermission } from \"@/lib/route-permissions\";\r\nimport { hasRouteAccess } from \"@/lib/role-utils\"; // Keep for backward compatibility\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\nfunction mapActionToGrantAction(action: string): string {\r\n  switch (action) {\r\n    case \"view\":\r\n      return \"read:any\";\r\n    case \"create\":\r\n      return \"create:any\";\r\n    case \"update\":\r\n      return \"update:any\";\r\n    case \"delete\":\r\n      return \"delete:any\";\r\n    case \"manage\":\r\n      return \"update:any\"; // Manage maps to update:any\r\n    case \"transfer\":\r\n      return \"update:any\"; // Transfer maps to update:any\r\n    case \"report\":\r\n      return \"read:any\"; // Report maps to read:any\r\n    default:\r\n      return `${action}:any`;\r\n  }\r\n}\r\n\r\ninterface RoleGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function RoleGuard({ children }: RoleGuardProps) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const { hasPermission, isLoading: isPermissionsLoading } = usePermissions();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Track if this is the initial mount\r\n  const [isInitialMount, setIsInitialMount] = useState(true);\r\n\r\n  // Effect to mark initial mount as complete\r\n  useEffect(() => {\r\n    setIsInitialMount(false);\r\n  }, []);\r\n\r\n  // Effect to check access rights\r\n  useEffect(() => {\r\n    // Skip check if on auth pages\r\n    if (\r\n      pathname.startsWith(\"/login\") ||\r\n      pathname.startsWith(\"/forgot-password\") ||\r\n      pathname.startsWith(\"/reset-password\")\r\n    ) {\r\n      setLoading(\"role\", false);\r\n      return;\r\n    }\r\n\r\n    // Only show loading indicator on initial mount or when user data is loading\r\n    // and we haven't shown the loading screen before in this session\r\n    if (isInitialMount && isUserLoading && !hasShownLoading.role) {\r\n      setLoading(\"role\", true);\r\n      return;\r\n    } else {\r\n      setLoading(\"role\", false);\r\n    }\r\n\r\n    // If user data and permissions are loaded, check access rights\r\n    if (!isUserLoading && !isPermissionsLoading && user) {\r\n      // Safely access role_name with type checking\r\n      const roleName =\r\n        user && typeof user === \"object\" && \"role_name\" in user\r\n          ? user.role_name\r\n          : \"\";\r\n\r\n      // First try permission-based access control\r\n      const routePermission = getRoutePermission(pathname);\r\n\r\n      let hasAccess = true;\r\n      let accessMethod = \"default\";\r\n\r\n      // For MVP, prioritize role-based access control\r\n      hasAccess = hasRouteAccess(pathname, roleName);\r\n      accessMethod = \"role\";\r\n\r\n      // Add detailed debug logging\r\n      console.log(`[RoleGuard] Route: ${pathname}`);\r\n      console.log(`[RoleGuard] User role: ${roleName}`);\r\n      console.log(`[RoleGuard] Using role-based access (MVP approach)`);\r\n      console.log(\r\n        `[RoleGuard] Access ${hasAccess ? \"GRANTED\" : \"DENIED\"} by role check`\r\n      );\r\n\r\n      // If we have a permission mapping, check it too (for debugging purposes only)\r\n      if (routePermission) {\r\n        // Map the action to the format used in grants (e.g., \"view\" -> \"read:any\")\r\n        const grantAction = mapActionToGrantAction(routePermission.action);\r\n\r\n        // Check if user has the required permission (but don't use the result)\r\n        const permissionAccess = hasPermission(\r\n          routePermission.resource,\r\n          grantAction,\r\n          routePermission.scope\r\n        );\r\n\r\n        // Log the permission check result for debugging\r\n        console.log(\r\n          `[RoleGuard] Permission check (not used): ${\r\n            routePermission.resource\r\n          }:${routePermission.action}:${routePermission.scope || \"any\"}`\r\n        );\r\n        console.log(`[RoleGuard] Mapped to grant action: ${grantAction}`);\r\n        console.log(\r\n          `[RoleGuard] Permission would be ${\r\n            permissionAccess ? \"GRANTED\" : \"DENIED\"\r\n          } (for future reference)`\r\n        );\r\n      }\r\n\r\n      if (!hasAccess) {\r\n        console.log(\r\n          `[RoleGuard] Access denied for ${pathname}, redirecting to dashboard (method: ${accessMethod})`\r\n        );\r\n\r\n        // Redirect to the appropriate dashboard based on role\r\n        if (roleName === \"accountant\" || roleName === \"finance_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to finance dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/finance\");\r\n        } else if (roleName === \"stock_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to stock dashboard`);\r\n          router.replace(\"/dashboard\");\r\n\r\n          // router.replace(\"/dashboard/stock\");\r\n        } else if (\r\n          roleName === \"operations\" ||\r\n          roleName === \"operations_manager\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to operations dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/operations\");\r\n        } else if (roleName === \"float_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to float dashboard`);\r\n          // router.replace(\"/dashboard/float\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (\r\n          roleName === \"pos_operator\" ||\r\n          roleName === \"shop_attendant\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to POS dashboard`);\r\n          // router.replace(\"/dashboard/pos\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (roleName === \"company_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to company dashboard`);\r\n          // router.replace(\"/dashboard/company\");\r\n          router.replace(\"/dashboard\");\r\n        } else {\r\n          console.log(`[RoleGuard] Redirecting to main dashboard`);\r\n          router.replace(\"/dashboard\");\r\n        }\r\n      }\r\n\r\n      // User data is loaded, we can stop checking and mark as shown\r\n      setLoading(\"role\", false);\r\n      markLoadingShown(\"role\");\r\n    } else if (!isUserLoading && !isPermissionsLoading) {\r\n      // No user data and not loading, stop checking\r\n      setLoading(\"role\", false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    user,\r\n    isUserLoading,\r\n    isPermissionsLoading,\r\n    pathname,\r\n    router,\r\n    isInitialMount,\r\n    hasShownLoading.role,\r\n    hasPermission,\r\n  ]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in checking state\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout | null = null;\r\n\r\n    if (isLoading.role) {\r\n      timeoutId = setTimeout(() => {\r\n        setLoading(\"role\", false);\r\n        markLoadingShown(\"role\");\r\n      }, 1500); // 1.5 second timeout for better UX\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.role]);\r\n\r\n  // Show loading state only when actively checking role access\r\n  if (isLoading.role) {\r\n    return <LoadingScreen message=\"Checking access...\" />;\r\n  }\r\n\r\n  // Don't block rendering, the useEffect will handle redirects\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA,qNAAmD,kCAAkC;AACrF;AARA;;;;;;;;;AAaA;;;;CAIC,GACD,SAAS,uBAAuB,MAAc;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,cAAc,4BAA4B;QACnD,KAAK;YACH,OAAO,cAAc,8BAA8B;QACrD,KAAK;YACH,OAAO,YAAY,0BAA0B;QAC/C;YACE,OAAO,GAAG,OAAO,IAAI,CAAC;IAC1B;AACF;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EAAE,aAAa,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;IACpB,GAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,oBACpB;YACA,WAAW,QAAQ;YACnB;QACF;QAEA,4EAA4E;QAC5E,iEAAiE;QACjE,IAAI,kBAAkB,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC5D,WAAW,QAAQ;YACnB;QACF,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,+DAA+D;QAC/D,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,MAAM;YACnD,6CAA6C;YAC7C,MAAM,WACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;YAEN,4CAA4C;YAC5C,MAAM,kBAAkB,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;YAE3C,IAAI,YAAY;YAChB,IAAI,eAAe;YAEnB,gDAAgD;YAChD,YAAY,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;YACrC,eAAe;YAEf,6BAA6B;YAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAC5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;YAChD,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;YAChE,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,YAAY,YAAY,SAAS,cAAc,CAAC;YAGxE,8EAA8E;YAC9E,IAAI,iBAAiB;gBACnB,2EAA2E;gBAC3E,MAAM,cAAc,uBAAuB,gBAAgB,MAAM;gBAEjE,uEAAuE;gBACvE,MAAM,mBAAmB,cACvB,gBAAgB,QAAQ,EACxB,aACA,gBAAgB,KAAK;gBAGvB,gDAAgD;gBAChD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EACxC,gBAAgB,QAAQ,CACzB,CAAC,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,IAAI,OAAO;gBAEhE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,aAAa;gBAChE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAC/B,mBAAmB,YAAY,SAChC,uBAAuB,CAAC;YAE7B;YAEA,IAAI,CAAC,WAAW;gBACd,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,SAAS,oCAAoC,EAAE,aAAa,CAAC,CAAC;gBAGjG,sDAAsD;gBACtD,IAAI,aAAa,gBAAgB,aAAa,mBAAmB;oBAC/D,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;oBAC1D,OAAO,OAAO,CAAC;gBACf,wCAAwC;gBAC1C,OAAO,IAAI,aAAa,eAAe;oBACrC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;oBACxD,OAAO,OAAO,CAAC;gBAEf,sCAAsC;gBACxC,OAAO,IACL,aAAa,gBACb,aAAa,sBACb;oBACA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC;oBAC7D,OAAO,OAAO,CAAC;gBACf,2CAA2C;gBAC7C,OAAO,IAAI,aAAa,iBAAiB;oBACvC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;oBACxD,sCAAsC;oBACtC,OAAO,OAAO,CAAC;gBACjB,OAAO,IACL,aAAa,kBACb,aAAa,kBACb;oBACA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC;oBACtD,oCAAoC;oBACpC,OAAO,OAAO,CAAC;gBACjB,OAAO,IAAI,aAAa,iBAAiB;oBACvC,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;oBAC1D,wCAAwC;oBACxC,OAAO,OAAO,CAAC;gBACjB,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,yCAAyC,CAAC;oBACvD,OAAO,OAAO,CAAC;gBACjB;YACF;YAEA,8DAA8D;YAC9D,WAAW,QAAQ;YACnB,iBAAiB;QACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;YAClD,8CAA8C;YAC9C,WAAW,QAAQ;QACrB;IACA,uDAAuD;IACzD,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,IAAI;QACpB;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAmC;QAEvC,IAAI,UAAU,IAAI,EAAE;YAClB,YAAY,WAAW;gBACrB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB,GAAG,OAAO,mCAAmC;QAC/C;QAEA,OAAO;YACL,IAAI,WAAW,aAAa;QAC9B;IACA,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6DAA6D;IAC7D,IAAI,UAAU,IAAI,EAAE;QAClB,qBAAO,8OAAC,sJAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeftIcon } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\";\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  // Use useMemo to prevent recreating the button on every render\r\n  const button = React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, variant, className, props]);\r\n\r\n  // Use useMemo for tooltipContent to prevent recalculation on every render\r\n  const tooltipContent = React.useMemo(() => {\r\n    if (!tooltip) return null;\r\n    return typeof tooltip === \"string\" ? { children: tooltip } : tooltip;\r\n  }, [tooltip]);\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  // Use useMemo for the hidden prop to prevent recalculation on every render\r\n  const isHidden = React.useMemo(() => {\r\n    return state !== \"collapsed\" || isMobile;\r\n  }, [state, isMobile]);\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={isHidden}\r\n        {...tooltipContent}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, className, showOnHover, props]);\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [className, props]);\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean;\r\n  size?: \"sm\" | \"md\";\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, className, props]);\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,+rBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,+DAA+D;IAC/D,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBAC3B,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;gBAAE;gBAAS;YAAK,IAAI;YAC3D,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAS;QAAW;KAAM;IAEpD,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACnC,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO,OAAO,YAAY,WAAW;YAAE,UAAU;QAAQ,IAAI;IAC/D,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,2EAA2E;IAC3E,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC7B,OAAO,UAAU,eAAe;IAClC,GAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ;gBACP,GAAG,cAAc;;;;;;;;;;;;AAI1B;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;YAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAW;QAAa;KAAM;AAC1C;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAW;KAAM;AACvB;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAW;KAAM;AAC7C", "debugId": null}}, {"offset": {"line": 1906, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/navigation-link.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { ReactNode, MouseEvent, useMemo } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface NavigationLinkProps {\r\n  href: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;\r\n  replace?: boolean;\r\n  prefetch?: boolean;\r\n  scroll?: boolean;\r\n  exact?: boolean;\r\n}\r\n\r\n/**\r\n * NavigationLink component that ensures client-side navigation\r\n * This component wraps Next.js Link component and provides a consistent way to navigate\r\n */\r\nexport function NavigationLink({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(className)}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n      aria-current={isActive ? \"page\" : undefined}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n\r\n/**\r\n * NavigationButton component that looks like a button but navigates like a link\r\n */\r\nexport function NavigationButton({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(\r\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAsBO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;QACb,gBAAc,WAAW,SAAS;kBAEjC;;;;;;AAGP;AAKO,SAAS,iBAAiB,EAC/B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;QAEF,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;kBAEZ;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,uKAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-main.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\nimport { ChevronRight, CircleIcon, type LucideIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: {\r\n    title: string;\r\n    url: string;\r\n    icon?: LucideIcon;\r\n    isActive?: boolean;\r\n    items?: {\r\n      title: string;\r\n      url: string;\r\n    }[];\r\n  }[];\r\n}) {\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          // Special case for Dashboard - make it a direct link\r\n          if (item.title === \"Dashboard\") {\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  isActive={item.isActive}\r\n                  className=\"nav-main-item\"\r\n                >\r\n                  <NavigationLink href={item.url}>\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                  </NavigationLink>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          }\r\n\r\n          // For all other items, use the collapsible dropdown\r\n          return (\r\n            <Collapsible\r\n              key={item.title}\r\n              asChild\r\n              defaultOpen={item.isActive}\r\n              className=\"group/collapsible mb-2\"\r\n            >\r\n              <SidebarMenuItem>\r\n                <CollapsibleTrigger asChild>\r\n                  <SidebarMenuButton\r\n                    tooltip={item.title}\r\n                    isActive={item.isActive}\r\n                    className=\"nav-main-item\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                    <ChevronRight className=\"ml-auto h-2 w-2 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 nav-main-item-chevron\" />\r\n                  </SidebarMenuButton>\r\n                </CollapsibleTrigger>\r\n                <CollapsibleContent>\r\n                  <SidebarMenuSub className=\"nav-main-sub mt-2\">\r\n                    {item.items?.map((subItem) => (\r\n                      <SidebarMenuSubItem key={subItem.title}>\r\n                        <SidebarMenuSubButton asChild>\r\n                          <NavigationLink href={subItem.url} exact={true}>\r\n                            {/* No circle icon for child items */}\r\n                            <span className=\"text-xs\">{subItem.title}</span>\r\n                          </NavigationLink>\r\n                        </SidebarMenuSubButton>\r\n                      </SidebarMenuSubItem>\r\n                    ))}\r\n                  </SidebarMenuSub>\r\n                </CollapsibleContent>\r\n              </SidebarMenuItem>\r\n            </Collapsible>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAKA;AAVA;;;;;;AAoBO,SAAS,QAAQ,EACtB,KAAK,EAYN;IACC,qBACE,8OAAC,mIAAA,CAAA,eAAY;kBACX,cAAA,8OAAC,mIAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,qDAAqD;gBACrD,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAS,KAAK,KAAK;4BACnB,UAAU,KAAK,QAAQ;4BACvB,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,GAAG;;kDAC5B,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAG1B,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAfG,KAAK,KAAK;;;;;gBAqBpC;gBAEA,oDAAoD;gBACpD,qBACE,8OAAC,uIAAA,CAAA,cAAW;oBAEV,OAAO;oBACP,aAAa,KAAK,QAAQ;oBAC1B,WAAU;8BAEV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;;0CACd,8OAAC,uIAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;oCAChB,SAAS,KAAK,KAAK;oCACnB,UAAU,KAAK,QAAQ;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,8OAAC,uIAAA,CAAA,qBAAkB;0CACjB,cAAA,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,8OAAC,mIAAA,CAAA,qBAAkB;sDACjB,cAAA,8OAAC,mIAAA,CAAA,uBAAoB;gDAAC,OAAO;0DAC3B,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oDAAC,MAAM,QAAQ,GAAG;oDAAE,OAAO;8DAExC,cAAA,8OAAC;wDAAK,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;2CAJrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mBA3BzC,KAAK,KAAK;;;;;YAyCrB;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2259, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2311, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2573, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chev<PERSON>UpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground nav-user\"\r\n            >\r\n              <Avatar className=\"h-6 w-6 rounded-lg nav-user-avatar\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg text-xs\">\r\n                  {user.name.charAt(0)}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left leading-tight nav-user-details\">\r\n                <span className=\"truncate text-xs font-medium\">\r\n                  {user.name}\r\n                </span>\r\n                <span className=\"truncate text-[10px]\">{user.email}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto h-3 w-3 nav-user-chevron\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <BadgeCheck className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"text-xs\">Account</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem>\r\n              <LogOut className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"text-xs\">Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AASA;AAdA;;;;;;AAqBO,SAAS,QAAQ,EACtB,IAAI,EAOL;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,8OAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;8CAEpD,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,8OAAC,4IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,8OAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,oBAAiB;0CAChB,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;;sDACf,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,mBAAgB;;kDACf,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 2832, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport \"@/components/ui/sidebar-collapsed.css\";\r\nimport \"@/components/ui/sidebar-compact.css\";\r\nimport {\r\n  Banknote,\r\n  BarChart3 as BarChart3Icon,\r\n  Building as BuildingIcon,\r\n  CreditCard,\r\n  FileText,\r\n  GalleryVerticalEnd,\r\n  Home as HomeIcon,\r\n  Landmark,\r\n  Package,\r\n  Settings,\r\n  ShoppingCart,\r\n  Smartphone,\r\n  UserCheck,\r\n} from \"lucide-react\";\r\n\r\nimport { NavMain } from \"@/components/nav-main\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n// Import usePermissions for future use\r\n// import { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\n// import { getRoutePermission } from \"@/lib/route-permissions\";\r\n// import { navigationPermissions } from \"@/lib/navigation-permissions\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\ntype NavItem = {\r\n  title: string;\r\n  url: string;\r\n  icon?: any;\r\n  isActive?: boolean;\r\n  items?: { title: string; url: string }[];\r\n};\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * This function is commented out for MVP, will be used in the future\r\n * when the permission system is fully implemented\r\n *\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\n// function mapActionToGrantAction(action: string): string {\r\n//   switch (action) {\r\n//     case \"view\":\r\n//       return \"read:any\";\r\n//     case \"create\":\r\n//       return \"create:any\";\r\n//     case \"update\":\r\n//       return \"update:any\";\r\n//     case \"delete\":\r\n//       return \"delete:any\";\r\n//     case \"manage\":\r\n//       return \"update:any\"; // Manage maps to update:any\r\n//     case \"transfer\":\r\n//       return \"update:any\"; // Transfer maps to update:any\r\n//     case \"report\":\r\n//       return \"read:any\"; // Report maps to read:any\r\n//     default:\r\n//       return `${action}:any`;\r\n//   }\r\n// }\r\n\r\nexport function AppSidebar() {\r\n  const { data: user } = useCurrentUser();\r\n  const pathname = usePathname();\r\n  // Permissions will be used in the future\r\n  // const { hasPermission, permissions } = usePermissions();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  // Define all navigation items\r\n  const getAllNavigationItems = (): NavItem[] => [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: HomeIcon,\r\n      isActive: pathname === \"/dashboard\" || pathname === \"/\",\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      url: \"/reports/sales-summary\",\r\n      icon: BarChart3Icon,\r\n      isActive:\r\n        pathname.startsWith(\"/reports\") ||\r\n        pathname.startsWith(\"/expense-analytics\"),\r\n      items: [\r\n        {\r\n          title: \"Sales Summary\",\r\n          url: \"/reports/sales-summary\",\r\n        },\r\n        {\r\n          title: \"Sales by Item\",\r\n          url: \"/reports/sales-by-item\",\r\n        },\r\n        {\r\n          title: \"Sales by Category\",\r\n          url: \"/reports/sales-by-category\",\r\n        },\r\n        {\r\n          title: \"Current Stock Levels\",\r\n          url: \"/reports/current-stock-levels\",\r\n        },\r\n        {\r\n          title: \"Stock History\",\r\n          url: \"/reports/stock-history\",\r\n        },\r\n        {\r\n          title: \"Banking Transactions\",\r\n          url: \"/reports/banking-transactions\",\r\n        },\r\n        {\r\n          title: \"Tax Report\",\r\n          url: \"/reports/tax-report\",\r\n        },\r\n        {\r\n          title: \"Banking Summary\",\r\n          url: \"/banking?tab=summary\",\r\n        },\r\n        {\r\n          title: \"M-Pesa Transactions\",\r\n          url: \"/reports/mpesa-transactions\",\r\n        },\r\n        {\r\n          title: \"Running Balances\",\r\n          url: \"/reports/running-balances\",\r\n        },\r\n        {\r\n          title: \"Cash Status\",\r\n          url: \"/reports/cash-status\",\r\n        },\r\n        {\r\n          title: \"Cash Float\",\r\n          url: \"/reports/cash-float\",\r\n        },\r\n        {\r\n          title: \"DSA Sales\",\r\n          url: \"/reports/dsa-sales\",\r\n        },\r\n        {\r\n          title: \"Receipts\",\r\n          url: \"/reports/receipts\",\r\n        },\r\n        {\r\n          title: \"Shifts\",\r\n          url: \"/reports/shifts\",\r\n        },\r\n        {\r\n          title: \"Phone Repairs\",\r\n          url: \"/reports/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"Stock Valuation\",\r\n          url: \"/inventory/valuation\",\r\n        },\r\n        {\r\n          title: \"Stock Aging\",\r\n          url: \"/reports/stock-aging\",\r\n        },\r\n        {\r\n          title: \"Stock Movement\",\r\n          url: \"/reports/stock-movement\",\r\n        },\r\n        {\r\n          title: \"Expense Analytics\",\r\n          url: \"/expense-analytics\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Administration\",\r\n      url: \"/users\",\r\n      icon: BuildingIcon,\r\n      isActive:\r\n        pathname.startsWith(\"/users\") ||\r\n        pathname.startsWith(\"/roles\") ||\r\n        pathname.startsWith(\"/rbac\") ||\r\n        pathname.startsWith(\"/branches\") ||\r\n        pathname.startsWith(\"/locations\") ||\r\n        pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        // Tenants menu item removed\r\n        {\r\n          title: \"Users\",\r\n          url: \"/users\",\r\n        },\r\n        {\r\n          title: \"Roles\",\r\n          url: \"/roles\",\r\n        },\r\n        {\r\n          title: \"RBAC\",\r\n          url: \"/rbac\",\r\n        },\r\n        {\r\n          title: \"Branches\",\r\n          url: \"/branches\",\r\n        },\r\n        {\r\n          title: \"Locations\",\r\n          url: \"/locations\",\r\n        },\r\n        {\r\n          title: \"Employees\",\r\n          url: \"/employees\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Stock & Inventory\",\r\n      url: \"/products\",\r\n      icon: Package,\r\n      isActive:\r\n        pathname.startsWith(\"/products\") ||\r\n        pathname.startsWith(\"/categories\") ||\r\n        pathname.startsWith(\"/brands\") ||\r\n        pathname.startsWith(\"/inventory\") ||\r\n        pathname.includes(\"/inventory/stock-cards\") ||\r\n        pathname.includes(\"/inventory/purchases\"),\r\n      items: [\r\n        {\r\n          title: \"Products\",\r\n          url: \"/products\",\r\n        },\r\n        {\r\n          title: \"Categories\",\r\n          url: \"/categories\",\r\n        },\r\n        {\r\n          title: \"Brands\",\r\n          url: \"/brands\",\r\n        },\r\n        {\r\n          title: \"Inventory\",\r\n          url: \"/inventory\",\r\n        },\r\n        // Commented out Stock Locations as requested\r\n        // {\r\n        //   title: \"Stock Locations\",\r\n        //   url: \"/inventory/locations\",\r\n        // },\r\n        {\r\n          title: \"Purchases\",\r\n          url: \"/inventory/purchases\",\r\n        },\r\n        {\r\n          title: \"Create Purchase\",\r\n          url: \"/inventory/purchases/new\",\r\n        },\r\n        {\r\n          title: \"Stock Requests\",\r\n          url: \"/inventory/stock-requests\",\r\n        },\r\n        {\r\n          title: \"Stock Transfers\",\r\n          url: \"/inventory/transfers\",\r\n        },\r\n        {\r\n          title: \"Make Stock Transfer\",\r\n          url: \"/inventory/bulk-transfer\",\r\n        },\r\n        {\r\n          title: \"Stock Transfer History\",\r\n          url: \"/inventory/bulk-transfers\",\r\n        },\r\n        {\r\n          title: \"Stock Cards\",\r\n          url: \"/inventory/stock-cards\",\r\n        },\r\n        {\r\n          title: \"Inventory Reports\",\r\n          url: \"/inventory/reports\",\r\n        },\r\n        {\r\n          title: \"Excel Import/Export\",\r\n          url: \"/inventory/excel\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Float Management\",\r\n      url: \"/float\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/float\"),\r\n      items: [\r\n        {\r\n          title: \"Float Dashboard\",\r\n          url: \"/float\",\r\n        },\r\n        {\r\n          title: \"Float Movements\",\r\n          url: \"/float/movements\",\r\n        },\r\n        {\r\n          title: \"Float Reconciliations\",\r\n          url: \"/float/reconciliations\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Banking Management\",\r\n      url: \"/banking\",\r\n      icon: Landmark,\r\n      isActive: pathname.startsWith(\"/banking\"),\r\n      items: [\r\n        {\r\n          title: \"Banking Records\",\r\n          url: \"/banking\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Expenses Management\",\r\n      url: \"/expenses\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/expenses\"),\r\n      items: [\r\n        {\r\n          title: \"Expenses\",\r\n          url: \"/expenses\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Invoice Management\",\r\n      url: \"/invoices\",\r\n      icon: FileText,\r\n      isActive:\r\n        pathname.startsWith(\"/invoices\") ||\r\n        pathname.startsWith(\"/credit-notes\"),\r\n      items: [\r\n        {\r\n          title: \"All Invoices\",\r\n          url: \"/invoices\",\r\n        },\r\n        {\r\n          title: \"Credit Notes\",\r\n          url: \"/credit-notes\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"POS Management\",\r\n      url: \"/pos\",\r\n      icon: CreditCard,\r\n      isActive:\r\n        pathname.startsWith(\"/pos\") ||\r\n        pathname.startsWith(\"/sales\") ||\r\n        pathname.startsWith(\"/customers\"),\r\n      items: [\r\n        {\r\n          title: \"POS Dashboard\",\r\n          url: \"/pos\",\r\n        },\r\n        {\r\n          title: \"POS Sessions\",\r\n          url: \"/pos/sessions\",\r\n        },\r\n        {\r\n          title: \"Cash Balance\",\r\n          url: \"/dashboard/cash-balance\",\r\n        },\r\n        {\r\n          title: \"Sales\",\r\n          url: \"/sales\",\r\n        },\r\n        {\r\n          title: \"Customers\",\r\n          url: \"/customers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"DSA Management\",\r\n      url: \"/dsa\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/dsa\"),\r\n      items: [\r\n        {\r\n          title: \"DSA Dashboard\",\r\n          url: \"/dsa\",\r\n        },\r\n        {\r\n          title: \"DSA Agents\",\r\n          url: \"/dsa/customers\",\r\n        },\r\n        {\r\n          title: \"Stock Assignments\",\r\n          url: \"/dsa/assignments\",\r\n        },\r\n        // Commented out DSA Reconciliations as requested\r\n        // {\r\n        //   title: \"DSA Reconciliations\",\r\n        //   url: \"/dsa/reconciliations\",\r\n        // },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Procurement\",\r\n      url: \"/procurement\",\r\n      icon: ShoppingCart,\r\n      isActive:\r\n        pathname.startsWith(\"/procurement\") ||\r\n        pathname.startsWith(\"/suppliers\"),\r\n      items: [\r\n        {\r\n          title: \"Procurement Dashboard\",\r\n          url: \"/procurement\",\r\n        },\r\n        {\r\n          title: \"Procurement Requests\",\r\n          url: \"/procurement/requests\",\r\n        },\r\n        {\r\n          title: \"Create Request\",\r\n          url: \"/procurement/requests/new\",\r\n        },\r\n        {\r\n          title: \"Procurement Receipts\",\r\n          url: \"/procurement/receipts\",\r\n        },\r\n        {\r\n          title: \"Suppliers\",\r\n          url: \"/suppliers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Phone Repairs\",\r\n      url: \"/phone-repairs\",\r\n      icon: Smartphone,\r\n      isActive:\r\n        pathname === \"/phone-repairs\" || pathname.startsWith(\"/phone-repairs/\")\r\n          ? true\r\n          : false,\r\n      items: [\r\n        {\r\n          title: \"All Repairs\",\r\n          url: \"/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"New Repair\",\r\n          url: \"/phone-repairs/new\",\r\n        },\r\n      ],\r\n    },\r\n    // Settings section completely hidden for production - not implemented\r\n    // {\r\n    //   title: \"Settings\",\r\n    //   url: \"/settings\",\r\n    //   icon: Settings,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/settings\") || pathname.startsWith(\"/profile\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"System Settings\",\r\n    //       url: \"/settings/system\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Company Settings\",\r\n    //       url: \"/settings/company\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Payment Methods\",\r\n    //       url: \"/settings/payment-methods\",\r\n    //     },\r\n    //     {\r\n    //       title: \"System Health\",\r\n    //       url: \"/settings/health\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Profile\",\r\n    //       url: \"/profile\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    // Permissions Demo section removed\r\n  ];\r\n\r\n  // Filter navigation items based on role (MVP approach)\r\n  const getFilteredNavigationItemsByRole = (): NavItem[] => {\r\n    const allItems = getAllNavigationItems();\r\n\r\n    // Create a custom Employees section for branch managers\r\n    const employeesSection: NavItem = {\r\n      title: \"Employees\",\r\n      url: \"/employees\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        {\r\n          title: \"Manage Employees\",\r\n          url: \"/employees\",\r\n        },\r\n        {\r\n          title: \"Add Employee\",\r\n          url: \"/employees/create\",\r\n        },\r\n      ],\r\n    };\r\n\r\n    // For MVP, use role-based filtering as the default approach\r\n    console.log(\r\n      `[Navigation] ${userRoleName} role detected - using role-based filtering (MVP approach)`\r\n    );\r\n\r\n    // Filter navigation items based on user role\r\n    const filteredItems = allItems.filter((item) => {\r\n      // For super_admin and company_admin, show all items\r\n      if (userRoleName === \"super_admin\" || userRoleName === \"company_admin\") {\r\n        console.log(`[Navigation] Admin role detected - showing all items`);\r\n        return true;\r\n      }\r\n\r\n      // For branch_admin, show most items\r\n      if (userRoleName === \"branch_admin\") {\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n        return true;\r\n      }\r\n\r\n      // For accountant, show finance-related items\r\n      if (userRoleName === \"accountant\") {\r\n        // Allow Dashboard\r\n        // Hide Tenants section\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Administration routes for accountant\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Phone Repairs\r\n        if (item.title === \"Phone Repairs\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management for viewing sales data\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide Settings for accountant\r\n        if (item.title === \"Settings\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For auditor, show reporting and read-only sections\r\n      if (userRoleName === \"auditor\") {\r\n        // Allow Reports, Banking, Expenses, Float (read-only)\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\" ||\r\n          item.title === \"Products & Inventory\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For finance_manager, show finance-related sections\r\n      if (userRoleName === \"finance_manager\") {\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For float_manager, show float-related sections\r\n      if (userRoleName === \"float_manager\") {\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Float Management, Banking, POS, and Reports\r\n        if (\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"POS Management\" ||\r\n          item.title === \"Reports\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For operations and operations_manager, show operations-related sections\r\n      if (\r\n        userRoleName === \"operations\" ||\r\n        userRoleName === \"operations_manager\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow everything else\r\n        return true;\r\n      }\r\n\r\n      // For stock_admin, show inventory-related sections\r\n      if (userRoleName === \"stock_admin\") {\r\n        // Allow Products & Inventory\r\n        if (\r\n          item.title === \"Products & Inventory\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Procurement\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // Branch Manager role removed as they don't login via the web\r\n\r\n      // For pos_operator and shop_attendant, hide admin sections\r\n      if (\r\n        userRoleName === \"pos_operator\" ||\r\n        userRoleName === \"shop_attendant\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Float Management\r\n        if (item.title === \"Float Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Banking Management\r\n        if (item.title === \"Banking Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Expenses Management\r\n        if (item.title === \"Expenses Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Products & Inventory (read-only)\r\n        if (item.title === \"Products & Inventory\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For other roles, restrict access to only essential items\r\n      console.log(\r\n        `[Navigation] Unknown role detected: ${userRoleName} - restricting access`\r\n      );\r\n\r\n      // Allow Dashboard only\r\n      if (item.title === \"Dashboard\") {\r\n        return true;\r\n      }\r\n\r\n      // Hide Settings and everything else for unknown roles\r\n      return false;\r\n    });\r\n\r\n    // Filter subitems based on role\r\n    const itemsWithFilteredSubitems = filteredItems.map((item) => {\r\n      // If the item has subitems, filter them based on role\r\n      if (item.items && item.items.length > 0) {\r\n        console.log(\r\n          `[Navigation] Checking subitems for \"${item.title}\" section`\r\n        );\r\n\r\n        const filteredSubItems = item.items.filter((subItem) => {\r\n          // For Administration, filter RBAC items for non-super_admin users\r\n          if (item.title === \"Administration\") {\r\n            // Only super_admin can see RBAC items\r\n            if (subItem.title === \"RBAC\") {\r\n              return userRoleName === \"super_admin\";\r\n            }\r\n            // All other admin roles can see other items\r\n            return true;\r\n          }\r\n\r\n          // For Settings, only show Profile for most roles\r\n          if (item.title === \"Settings\") {\r\n            // Admin roles can see all settings\r\n            if (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\"\r\n            ) {\r\n              return true;\r\n            }\r\n\r\n            // Accountant can see Profile and Payment Methods\r\n            if (userRoleName === \"accountant\") {\r\n              return (\r\n                subItem.title === \"Profile\" ||\r\n                subItem.title === \"Payment Methods\"\r\n              );\r\n            }\r\n\r\n            // Other roles can only see Profile\r\n            return subItem.title === \"Profile\";\r\n          }\r\n\r\n          // For POS Management, restrict Cash Balance to admin roles and float_manager\r\n          if (\r\n            item.title === \"POS Management\" &&\r\n            subItem.title === \"Cash Balance\"\r\n          ) {\r\n            return (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\" ||\r\n              userRoleName === \"accountant\" ||\r\n              userRoleName === \"finance_manager\" ||\r\n              userRoleName === \"float_manager\"\r\n            );\r\n          }\r\n\r\n          // For Products & Inventory, restrict Categories and Brands for non-admin roles\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Categories\") ||\r\n              subItem.title.includes(\"Brands\"))\r\n          ) {\r\n            return (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\" ||\r\n              userRoleName === \"stock_admin\"\r\n            );\r\n          }\r\n\r\n          // For Products & Inventory, restrict inventory management for shop_attendant and pos_operator\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Transfer\") ||\r\n              subItem.title.includes(\"Stock Cards\") ||\r\n              subItem.title.includes(\"Inventory Reports\") ||\r\n              subItem.title.includes(\"Excel\"))\r\n          ) {\r\n            return (\r\n              userRoleName !== \"pos_operator\" &&\r\n              userRoleName !== \"shop_attendant\"\r\n            );\r\n          }\r\n\r\n          // For Reports, restrict certain reports based on role\r\n          if (item.title === \"Reports\") {\r\n            // Finance-related reports\r\n            if (\r\n              subItem.title.includes(\"Banking\") ||\r\n              subItem.title.includes(\"Cash\") ||\r\n              subItem.title.includes(\"Float\") ||\r\n              subItem.title.includes(\"Expense\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n\r\n            // Inventory-related reports\r\n            if (\r\n              subItem.title.includes(\"Stock\") ||\r\n              subItem.title.includes(\"Inventory\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\" ||\r\n                userRoleName === \"operations\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Procurement, restrict certain operations based on role\r\n          if (item.title === \"Procurement\") {\r\n            // Creating and approving procurement requests\r\n            if (subItem.title.includes(\"Create\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Float Management, restrict certain operations based on role\r\n          if (item.title === \"Float Management\") {\r\n            // Float reconciliations\r\n            if (subItem.title.includes(\"Reconciliation\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"float_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // Allow all other subitems by default\r\n          return true;\r\n        });\r\n\r\n        // Update the item's subitems with the filtered list\r\n        item.items = filteredSubItems;\r\n      }\r\n\r\n      return item;\r\n    });\r\n\r\n    // Only return items that have at least one subitem (if they had subitems to begin with)\r\n    const result = itemsWithFilteredSubitems.filter(\r\n      (item) => !item.items || item.items.length > 0\r\n    );\r\n\r\n    // Add the Employees section at the beginning of the filtered items for branch managers\r\n    if (userRoleName === \"branch_manager\") {\r\n      return [employeesSection, ...result];\r\n    }\r\n\r\n    return result;\r\n  };\r\n\r\n  // Filter navigation items based on permissions (for future use)\r\n  // This function is commented out for MVP, will be implemented in the future\r\n  // when the permission system is fully implemented\r\n\r\n  // Sample data for the sidebar\r\n  const data = {\r\n    user: {\r\n      name: user?.name || \"User\",\r\n      email: user?.email || \"<EMAIL>\",\r\n      avatar: \"/placeholder-avatar.jpg\",\r\n    },\r\n    teams: [\r\n      {\r\n        name: teamData.name,\r\n        logo: GalleryVerticalEnd,\r\n        plan: teamData.plan,\r\n      },\r\n    ],\r\n    // For MVP, use role-based filtering\r\n    navMain: getFilteredNavigationItemsByRole(),\r\n    // For future: navMain: getFilteredNavigationItems(),\r\n  };\r\n\r\n  const { state, isMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\" && !isMobile;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"h-full flex flex-col sidebar-compact\",\r\n        isCollapsed && \"sidebar-collapsed\"\r\n      )}\r\n    >\r\n      <div className=\"flex-1 overflow-y-auto py-2\">\r\n        <div className={cn(\"px-3\", isCollapsed && \"px-2\")}>\r\n          <NavMain items={data.navMain} />\r\n        </div>\r\n      </div>\r\n      <div className={cn(\"p-3 border-t\", isCollapsed && \"p-2\")}>\r\n        <NavUser user={data.user} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,0EAA0E;AAC1E,gEAAgE;AAChE,wEAAwE;AACxE;AACA;AA7BA;;;;;;;;;;;AAoEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,yCAAyC;IACzC,2DAA2D;IAE3D,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,wBAAwB,IAAiB;YAC7C;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mMAAA,CAAA,OAAQ;gBACd,UAAU,aAAa,gBAAgB,aAAa;YACtD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,kNAAA,CAAA,YAAa;gBACnB,UACE,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAY;gBAClB,UACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL,4BAA4B;oBAC5B;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,wMAAA,CAAA,UAAO;gBACb,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,QAAQ,CAAC,6BAClB,SAAS,QAAQ,CAAC;gBACpB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,6CAA6C;oBAC7C,IAAI;oBACJ,8BAA8B;oBAC9B,iCAAiC;oBACjC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,WAAQ;gBACd,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,kNAAA,CAAA,aAAU;gBAChB,UACE,SAAS,UAAU,CAAC,WACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,gNAAA,CAAA,YAAS;gBACf,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBAMD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,sNAAA,CAAA,eAAY;gBAClB,UACE,SAAS,UAAU,CAAC,mBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,aAAU;gBAChB,UACE,aAAa,oBAAoB,SAAS,UAAU,CAAC,qBACjD,OACA;gBACN,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;SAgCD;IAED,uDAAuD;IACvD,MAAM,mCAAmC;QACvC,MAAM,WAAW;QAEjB,wDAAwD;QACxD,MAAM,mBAA4B;YAChC,OAAO;YACP,KAAK;YACL,MAAM,gNAAA,CAAA,YAAS;YACf,UAAU,SAAS,UAAU,CAAC;YAC9B,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QAEA,4DAA4D;QAC5D,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,aAAa,0DAA0D,CAAC;QAG1F,6CAA6C;QAC7C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;YACrC,oDAAoD;YACpD,IAAI,iBAAiB,iBAAiB,iBAAiB,iBAAiB;gBACtE,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC;gBAClE,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,iBAAiB,gBAAgB;gBACnC,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBACA,OAAO;YACT;YAEA,6CAA6C;YAC7C,IAAI,iBAAiB,cAAc;gBACjC,kBAAkB;gBAClB,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,4CAA4C;gBAC5C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,oBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,KAAK,KAAK,KAAK,iBAAiB;oBAClC,OAAO;gBACT;gBAEA,8CAA8C;gBAC9C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,+BAA+B;gBAC/B,IAAI,KAAK,KAAK,KAAK,YAAY;oBAC7B,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,WAAW;gBAC9B,sDAAsD;gBACtD,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,oBACf,KAAK,KAAK,KAAK,wBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,mBAAmB;gBACtC,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,kBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,iDAAiD;YACjD,IAAI,iBAAiB,iBAAiB;gBACpC,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,IACE,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,oBACf,KAAK,KAAK,KAAK,WACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,0EAA0E;YAC1E,IACE,iBAAiB,gBACjB,iBAAiB,sBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,OAAO;YACT;YAEA,mDAAmD;YACnD,IAAI,iBAAiB,eAAe;gBAClC,6BAA6B;gBAC7B,IACE,KAAK,KAAK,KAAK,0BACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,eACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,8DAA8D;YAE9D,2DAA2D;YAC3D,IACE,iBAAiB,kBACjB,iBAAiB,kBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,IAAI,KAAK,KAAK,KAAK,oBAAoB;oBACrC,OAAO;gBACT;gBAEA,0BAA0B;gBAC1B,IAAI,KAAK,KAAK,KAAK,sBAAsB;oBACvC,OAAO;gBACT;gBAEA,2BAA2B;gBAC3B,IAAI,KAAK,KAAK,KAAK,uBAAuB;oBACxC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,yCAAyC;gBACzC,IAAI,KAAK,KAAK,KAAK,wBAAwB;oBACzC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,2DAA2D;YAC3D,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,aAAa,qBAAqB,CAAC;YAG5E,uBAAuB;YACvB,IAAI,KAAK,KAAK,KAAK,aAAa;gBAC9B,OAAO;YACT;YAEA,sDAAsD;YACtD,OAAO;QACT;QAEA,gCAAgC;QAChC,MAAM,4BAA4B,cAAc,GAAG,CAAC,CAAC;YACnD,sDAAsD;YACtD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;gBAG9D,MAAM,mBAAmB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,kBAAkB;wBACnC,sCAAsC;wBACtC,IAAI,QAAQ,KAAK,KAAK,QAAQ;4BAC5B,OAAO,iBAAiB;wBAC1B;wBACA,4CAA4C;wBAC5C,OAAO;oBACT;oBAEA,iDAAiD;oBACjD,IAAI,KAAK,KAAK,KAAK,YAAY;wBAC7B,mCAAmC;wBACnC,IACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB;4BACA,OAAO;wBACT;wBAEA,iDAAiD;wBACjD,IAAI,iBAAiB,cAAc;4BACjC,OACE,QAAQ,KAAK,KAAK,aAClB,QAAQ,KAAK,KAAK;wBAEtB;wBAEA,mCAAmC;wBACnC,OAAO,QAAQ,KAAK,KAAK;oBAC3B;oBAEA,6EAA6E;oBAC7E,IACE,KAAK,KAAK,KAAK,oBACf,QAAQ,KAAK,KAAK,gBAClB;wBACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;oBAErB;oBAEA,+EAA+E;oBAC/E,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,iBACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,SAAS,GAClC;wBACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,8FAA8F;oBAC9F,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,eACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,kBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,wBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,QAAQ,GACjC;wBACA,OACE,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,sDAAsD;oBACtD,IAAI,KAAK,KAAK,KAAK,WAAW;wBAC5B,0BAA0B;wBAC1B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,WACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;wBAEA,4BAA4B;wBAC5B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB,wBACjB,iBAAiB,gBACjB,iBAAiB;wBAErB;oBACF;oBAEA,6DAA6D;oBAC7D,IAAI,KAAK,KAAK,KAAK,eAAe;wBAChC,8CAA8C;wBAC9C,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,WAAW;4BACpC,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB;wBAErB;oBACF;oBAEA,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,oBAAoB;wBACrC,wBAAwB;wBACxB,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,mBAAmB;4BAC5C,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;oBACF;oBAEA,sCAAsC;oBACtC,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,KAAK,KAAK,GAAG;YACf;YAEA,OAAO;QACT;QAEA,wFAAwF;QACxF,MAAM,SAAS,0BAA0B,MAAM,CAC7C,CAAC,OAAS,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;QAG/C,uFAAuF;QACvF,IAAI,iBAAiB,kBAAkB;YACrC,OAAO;gBAAC;mBAAqB;aAAO;QACtC;QAEA,OAAO;IACT;IAEA,gEAAgE;IAChE,4EAA4E;IAC5E,kDAAkD;IAElD,8BAA8B;IAC9B,MAAM,OAAO;QACX,MAAM;YACJ,MAAM,MAAM,QAAQ;YACpB,OAAO,MAAM,SAAS;YACtB,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM,SAAS,IAAI;gBACnB,MAAM,sOAAA,CAAA,qBAAkB;gBACxB,MAAM,SAAS,IAAI;YACrB;SACD;QACD,oCAAoC;QACpC,SAAS;IAEX;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACrC,MAAM,cAAc,UAAU,eAAe,CAAC;IAE9C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA,eAAe;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe;8BACxC,cAAA,8OAAC,iIAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;;;;;;0BAGhC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,eAAe;0BAChD,cAAA,8OAAC,iIAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 3577, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/team-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import { GalleryVerticalEnd } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n\r\nexport function TeamHeader() {\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"bg-white text-primary flex aspect-square size-6 items-center justify-center rounded-lg\">\r\n        {/* <GalleryVerticalEnd className=\"size-3\" /> */}\r\n        <Image\r\n          src={\"/images/simbatelecomlogo.png\"}\r\n          alt=\"Simba Telecom Logo\"\r\n          className=\"object-contain\"\r\n          width={16}\r\n          height={16}\r\n        />\r\n      </div>\r\n      <div className=\"grid flex-1 text-left leading-tight\">\r\n        <span className=\"truncate text-sm font-semibold text-primary-foreground\">\r\n          {teamData.name}\r\n        </span>\r\n        <span className=\"truncate text-xs text-primary-foreground/90 font-medium\">\r\n          {teamData.plan}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,qDAAqD;AACrD;AACA;AAJA;;;;AAMO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEpC,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,OAAO;oBACP,QAAQ;;;;;;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;kCAEhB,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;AAKxB", "debugId": null}}, {"offset": {"line": 3679, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/search-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\nexport function SearchButton() {\r\n  const { openSearch } = useSearch();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"sm\"\r\n      onClick={openSearch}\r\n      className=\"relative h-9 w-full justify-start bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64\"\r\n    >\r\n      <span className=\"hidden lg:inline-flex\">Search anything...</span>\r\n      <span className=\"inline-flex lg:hidden\">Search...</span>\r\n      <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\r\n        <span className=\"text-xs\">⌘</span>K\r\n      </kbd>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD;IAE/B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAU;;;;;;oBAAQ;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 3744, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/top-navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronLeft,\r\n  LogOut,\r\n  Settings,\r\n  User,\r\n  PanelLeftIcon,\r\n  PanelRightIcon,\r\n  Home,\r\n} from \"lucide-react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser, useLogout } from \"@/features/auth/hooks/use-auth\";\r\nimport { TeamHeader } from \"@/components/team-header\";\r\nimport { SearchButton } from \"@/components/search-button\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TopNavigation() {\r\n  const { data: user } = useCurrentUser();\r\n  const logout = useLogout();\r\n  const { toggleSidebar, state, isMobile, setOpenMobile } = useSidebar();\r\n  const isExpanded = state === \"expanded\";\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Check if we're on the stock requests page\r\n  const isStockRequestsPage = pathname.includes('/inventory/stock-requests') &&\r\n    !pathname.includes('/create') &&\r\n    !pathname.match(/\\/inventory\\/stock-requests\\/\\d+/);\r\n\r\n  const handleLogout = () => {\r\n    logout.mutate();\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    router.back();\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-30 flex h-14 shrink-0 items-center gap-2 border-b border-primary-foreground/10 bg-primary text-primary-foreground transition-[width,height] ease-linear\">\r\n      <div className=\"flex flex-1 items-center justify-between px-4 w-full\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isMobile && isStockRequestsPage ? (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={handleBackClick}\r\n                aria-label=\"Go back\"\r\n                title=\"Go back\"\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n                <span className=\"sr-only\">Go Back</span>\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={() => (isMobile ? setOpenMobile(true) : toggleSidebar())}\r\n                data-mobile={isMobile ? \"true\" : \"false\"}\r\n                aria-label=\"Toggle sidebar\"\r\n                title={isExpanded ? \"Collapse sidebar\" : \"Expand sidebar\"}\r\n              >\r\n                {isExpanded ? (\r\n                  <PanelLeftIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <PanelRightIcon className=\"h-5 w-5\" />\r\n                )}\r\n                <span className=\"sr-only\">Toggle Sidebar</span>\r\n              </Button>\r\n            )}\r\n            <TeamHeader />\r\n\r\n            <div className=\"h-6 border-r border-primary-foreground/20 mx-2\"></div>\r\n\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n              asChild\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <Home className=\"h-4 w-4 mr-1\" />\r\n                Dashboard\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-center\">\r\n          <SearchButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative h-8 w-8 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n            aria-label=\"Notifications\"\r\n          >\r\n            <Bell className=\"h-5 w-5\" />\r\n            <span className=\"absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-[10px] font-bold text-primary\">\r\n              3\r\n            </span>\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 gap-2 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                aria-label=\"Account\"\r\n              >\r\n                <Avatar className=\"h-7 w-7\">\r\n                  <AvatarImage\r\n                    src=\"/placeholder-avatar.jpg\"\r\n                    alt={user?.name || \"User\"}\r\n                  />\r\n                  <AvatarFallback className=\"text-xs font-semibold\">\r\n                    {user?.name ? user.name.charAt(0) : \"U\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span className=\"hidden text-sm font-semibold md:inline-block\">\r\n                  {user?.name || \"User\"}\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-primary-foreground/80\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56 rounded-lg\">\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-semibold leading-none\">\r\n                    {user?.name || \"User\"}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || \"<EMAIL>\"}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  <User className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Profile</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Settings</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span className=\"text-sm\">Log out</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;AACA;AA/BA;;;;;;;;;;;;AAiCO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACnE,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gCAC5C,CAAC,SAAS,QAAQ,CAAC,cACnB,CAAC,SAAS,KAAK,CAAC;IAElB,MAAM,eAAe;QACnB,OAAO,MAAM;IACf;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI;IACb;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,YAAY,oCACX,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,OAAM;;kDAEN,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;qDAG5B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAO,WAAW,cAAc,QAAQ;gCACjD,eAAa,WAAW,SAAS;gCACjC,cAAW;gCACX,OAAO,aAAa,qBAAqB;;oCAExC,2BACC,8OAAC,oNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,8OAAC,sNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,8OAAC,oIAAA,CAAA,aAAU;;;;;0CAEX,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,mMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,sIAAA,CAAA,eAAY;;;;;;;;;;8BAGf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAA6H;;;;;;;;;;;;sCAK/I,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAW;;0DAEX,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDACV,KAAI;wDACJ,KAAK,MAAM,QAAQ;;;;;;kEAErB,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGxC,8OAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG3B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,oBAAiB;;8DAChB,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 4179, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 4311, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/breadcrumbs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\n\r\nexport function Breadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  // Get the current page name from the pathname\r\n  const getPageName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length === 0) return \"Dashboard\";\r\n\r\n    // Get the last segment of the path and capitalize it\r\n    const lastSegment = path[path.length - 1];\r\n    return (\r\n      lastSegment.charAt(0).toUpperCase() +\r\n      lastSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  // Get the parent path for breadcrumb\r\n  const getParentPath = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Remove the last segment to get the parent path\r\n    return \"/\" + path.slice(0, path.length - 1).join(\"/\");\r\n  };\r\n\r\n  // Get the parent name for breadcrumb\r\n  const getParentName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Get the second-to-last segment and capitalize it\r\n    const parentSegment = path[path.length - 2];\r\n    return (\r\n      parentSegment.charAt(0).toUpperCase() +\r\n      parentSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  const parentPath = getParentPath();\r\n  const parentName = getParentName();\r\n  const pageName = getPageName();\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <Breadcrumb>\r\n        <BreadcrumbList>\r\n          {parentPath && parentName && (\r\n            <>\r\n              <BreadcrumbItem className=\"hidden md:block\">\r\n                <BreadcrumbLink asChild>\r\n                  <NavigationLink\r\n                    href={parentPath}\r\n                    className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\r\n                  >\r\n                    {parentName}\r\n                  </NavigationLink>\r\n                </BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n              <BreadcrumbSeparator className=\"hidden md:block h-3 w-3\" />\r\n            </>\r\n          )}\r\n          <BreadcrumbItem>\r\n            <BreadcrumbPage className=\"text-sm font-semibold\">\r\n              {pageName}\r\n            </BreadcrumbPage>\r\n          </BreadcrumbItem>\r\n        </BreadcrumbList>\r\n      </Breadcrumb>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AAXA;;;;;AAaO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,cAAc;QAClB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,qDAAqD;QACrD,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,OACE,YAAY,MAAM,CAAC,GAAG,WAAW,KACjC,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEvC;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,iDAAiD;QACjD,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;IACnD;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC3C,OACE,cAAc,MAAM,CAAC,GAAG,WAAW,KACnC,cAAc,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEzC;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;sBACT,cAAA,8OAAC,sIAAA,CAAA,iBAAc;;oBACZ,cAAc,4BACb;;0CACE,8OAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCACb,MAAM;wCACN,WAAU;kDAET;;;;;;;;;;;;;;;;0CAIP,8OAAC,sIAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;kCAGnC,8OAAC,sIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4427, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/main-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, Suspense, useEffect } from \"react\";\r\nimport { RoleGuard } from \"./role-guard\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SidebarProvider, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { AppSidebar } from \"@/components/app-sidebar\";\r\nimport { TopNavigation } from \"@/components/top-navigation\";\r\nimport { Breadcrumbs } from \"@/components/breadcrumbs\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\n\r\ninterface MainLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function MainLayout({ children }: MainLayoutProps) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Use useEffect for redirects to avoid hydration issues\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.main) {\r\n      setLoading(\"main\", true);\r\n    } else {\r\n      setLoading(\"main\", false);\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    // After auth state is initialized, we can hide the loading screen\r\n    setLoading(\"main\", false);\r\n\r\n    // If we have an access token, mark that we've shown loading\r\n    // This prevents showing loading on subsequent navigations\r\n    if (accessToken) {\r\n      markLoadingShown(\"main\");\r\n    }\r\n\r\n    // We're disabling client-side redirects to avoid redirect loops\r\n    // The middleware will handle redirects instead\r\n    if (!accessToken) {\r\n      // We're not redirecting here anymore\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [accessToken, isInitialized, hasShownLoading.main]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.main) {\r\n        setLoading(\"main\", false);\r\n        markLoadingShown(\"main\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.main]);\r\n\r\n  // Don't render anything if not authenticated\r\n  if (!accessToken) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <RoleGuard>\r\n        <MainLayoutContent>{children}</MainLayoutContent>\r\n      </RoleGuard>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nfunction MainLayoutContent({ children }: { children: ReactNode }) {\r\n  const { state, openMobile, setOpenMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full bg-gray-50\">\r\n      <TopNavigation />\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] overflow-hidden\">\r\n        {/* Desktop sidebar */}\r\n        <aside\r\n          className={cn(\r\n            \"border-r bg-white transition-all duration-300 shrink-0 hidden md:block\",\r\n            isCollapsed ? \"w-16\" : \"w-64\"\r\n          )}\r\n        >\r\n          <AppSidebar />\r\n        </aside>\r\n\r\n        {/* Mobile sidebar */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={openMobile} onOpenChange={setOpenMobile}>\r\n            <SheetContent\r\n              side=\"left\"\r\n              className=\"p-0 w-[280px] border-r bg-white\"\r\n            >\r\n              <div className=\"h-full overflow-y-auto\">\r\n                <AppSidebar />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        <main className=\"flex flex-col w-full overflow-y-auto overflow-x-hidden\">\r\n          <div className=\"p-6\">\r\n            <Suspense fallback={<div>Loading content...</div>}>\r\n              <Breadcrumbs />\r\n              {children}\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;AAoBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iDAAiD;QACjD,iCAAiC;QACjC,gEAAgE;QAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC3C,WAAW,QAAQ;QACrB,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,IAAI,CAAC,eAAe;YAClB,QAAQ,uCAAuC;QACjD;QAEA,kEAAkE;QAClE,WAAW,QAAQ;QAEnB,4DAA4D;QAC5D,0DAA0D;QAC1D,IAAI,aAAa;YACf,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,+CAA+C;QAC/C,IAAI,CAAC,aAAa;QAChB,qCAAqC;QACvC;IACA,uDAAuD;IACzD,GAAG;QAAC;QAAa;QAAe,gBAAgB,IAAI;KAAC;IAErD,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,UAAU,IAAI,EAAE;gBAClB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB;QACF,GAAG,OAAO,mCAAmC;QAE7C,OAAO,IAAM,aAAa;IAC1B,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6CAA6C;IAC7C,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,8IAAA,CAAA,YAAS;sBACR,cAAA,8OAAC;0BAAmB;;;;;;;;;;;;;;;;AAI5B;AAEA,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IAC9D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,cAAc,UAAU;IAE9B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,SAAS;kCAGzB,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;sCACrC,cAAA,8OAAC,iIAAA,CAAA,eAAY;gCACX,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,wBAAU,8OAAC;8CAAI;;;;;;;kDACvB,8OAAC,iIAAA,CAAA,cAAW;;;;;oCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4639, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4703, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4821, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4918, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/cash-status/breakdown-tables.tsx"], "sourcesContent": ["import {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { formatCurrency } from '@/lib/utils';\r\nimport { CashStatusReportResponse } from '@/services/report-service';\r\n\r\ninterface BreakdownTablesProps {\r\n  data: CashStatusReportResponse['data'] | null;\r\n  view: 'region' | 'branch' | 'details';\r\n  isLoading: boolean;\r\n}\r\n\r\nexport function BreakdownTables({ data, view, isLoading }: BreakdownTablesProps) {\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <Card className=\"animate-pulse\">\r\n          <CardHeader>\r\n            <CardTitle>\r\n              <div className=\"h-6 w-48 bg-muted rounded\"></div>\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"h-64 bg-muted rounded\"></div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!data) {\r\n    return (\r\n      <div className=\"text-center py-8\">\r\n        <p className=\"text-muted-foreground\">No data available. Please select a date range and refresh.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (view === 'region' && data.byRegion) {\r\n    return (\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Cash Status by Region</CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Table>\r\n            <TableHeader>\r\n              <TableRow>\r\n                <TableHead>Region</TableHead>\r\n                <TableHead className=\"text-right\">Opening Cash</TableHead>\r\n                <TableHead className=\"text-right\">Cash Inflows</TableHead>\r\n                <TableHead className=\"text-right\">Cash Outflows</TableHead>\r\n                <TableHead className=\"text-right\">Net Change</TableHead>\r\n                <TableHead className=\"text-right\">Closing Cash</TableHead>\r\n              </TableRow>\r\n            </TableHeader>\r\n            <TableBody>\r\n              {data.byRegion.map((region) => (\r\n                <TableRow key={region.region_id}>\r\n                  <TableCell className=\"font-medium\">{region.region_name}</TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(region.summary.totalOpeningCash)}</TableCell>\r\n                  <TableCell className=\"text-right text-green-600\">{formatCurrency(region.summary.totalInflows)}</TableCell>\r\n                  <TableCell className=\"text-right text-red-600\">{formatCurrency(region.summary.totalOutflows)}</TableCell>\r\n                  <TableCell className={`text-right ${region.summary.netCashChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n                    {formatCurrency(region.summary.netCashChange)}\r\n                  </TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(region.summary.totalClosingCash)}</TableCell>\r\n                </TableRow>\r\n              ))}\r\n            </TableBody>\r\n          </Table>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  if (view === 'branch' && data.byBranch) {\r\n    return (\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Cash Status by Branch</CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Table>\r\n            <TableHeader>\r\n              <TableRow>\r\n                <TableHead>Branch</TableHead>\r\n                <TableHead>Region</TableHead>\r\n                <TableHead className=\"text-right\">Opening Cash</TableHead>\r\n                <TableHead className=\"text-right\">Cash Inflows</TableHead>\r\n                <TableHead className=\"text-right\">Cash Outflows</TableHead>\r\n                <TableHead className=\"text-right\">Net Change</TableHead>\r\n                <TableHead className=\"text-right\">Closing Cash</TableHead>\r\n              </TableRow>\r\n            </TableHeader>\r\n            <TableBody>\r\n              {data.byBranch.map((branch) => (\r\n                <TableRow key={branch.branch_id}>\r\n                  <TableCell className=\"font-medium\">{branch.branch_name}</TableCell>\r\n                  <TableCell>{branch.region_name}</TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(branch.summary.totalOpeningCash)}</TableCell>\r\n                  <TableCell className=\"text-right text-green-600\">{formatCurrency(branch.summary.totalInflows)}</TableCell>\r\n                  <TableCell className=\"text-right text-red-600\">{formatCurrency(branch.summary.totalOutflows)}</TableCell>\r\n                  <TableCell className={`text-right ${branch.summary.netCashChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n                    {formatCurrency(branch.summary.netCashChange)}\r\n                  </TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(branch.summary.totalClosingCash)}</TableCell>\r\n                </TableRow>\r\n              ))}\r\n            </TableBody>\r\n          </Table>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  if (view === 'details') {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        {/* Cash Inflows Breakdown */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Cash Inflows Breakdown</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Table>\r\n              <TableHeader>\r\n                <TableRow>\r\n                  <TableHead>Source</TableHead>\r\n                  <TableHead className=\"text-right\">Amount</TableHead>\r\n                  <TableHead className=\"text-right\">Transactions</TableHead>\r\n                  <TableHead className=\"text-right\">% of Total</TableHead>\r\n                </TableRow>\r\n              </TableHeader>\r\n              <TableBody>\r\n                <TableRow>\r\n                  <TableCell className=\"font-medium\">Cash Sales</TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(data.cashInflows.cashSales?.total || 0)}</TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {data.cashInflows.cashSales?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0}\r\n                  </TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {data.cashInflows.total ?\r\n                      `${(((data.cashInflows.cashSales?.total || 0) / data.cashInflows.total) * 100).toFixed(1)}%` :\r\n                      '0%'}\r\n                  </TableCell>\r\n                </TableRow>\r\n                <TableRow>\r\n                  <TableCell className=\"font-medium\">MPESA Deposits</TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(data.cashInflows.mpesaDeposits?.total || 0)}</TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {data.cashInflows.mpesaDeposits?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0}\r\n                  </TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {data.cashInflows.total ?\r\n                      `${(((data.cashInflows.mpesaDeposits?.total || 0) / data.cashInflows.total) * 100).toFixed(1)}%` :\r\n                      '0%'}\r\n                  </TableCell>\r\n                </TableRow>\r\n\r\n                <TableRow className=\"font-bold\">\r\n                  <TableCell>Total Inflows</TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(data.cashInflows.total || 0)}</TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {(data.cashInflows.cashSales?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0) +\r\n                     (data.cashInflows.mpesaDeposits?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0)}\r\n                  </TableCell>\r\n                  <TableCell className=\"text-right\">100%</TableCell>\r\n                </TableRow>\r\n              </TableBody>\r\n            </Table>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Cash Outflows Breakdown */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Cash Outflows Breakdown</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Table>\r\n              <TableHeader>\r\n                <TableRow>\r\n                  <TableHead>Source</TableHead>\r\n                  <TableHead className=\"text-right\">Amount</TableHead>\r\n                  <TableHead className=\"text-right\">Transactions</TableHead>\r\n                  <TableHead className=\"text-right\">% of Total</TableHead>\r\n                </TableRow>\r\n              </TableHeader>\r\n              <TableBody>\r\n                <TableRow>\r\n                  <TableCell className=\"font-medium\">Expenses</TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(data.cashOutflows.expenses?.total || 0)}</TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {data.cashOutflows.expenses?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0}\r\n                  </TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {data.cashOutflows.total ?\r\n                      `${(((data.cashOutflows.expenses?.total || 0) / data.cashOutflows.total) * 100).toFixed(1)}%` :\r\n                      '0%'}\r\n                  </TableCell>\r\n                </TableRow>\r\n                <TableRow>\r\n                  <TableCell className=\"font-medium\">Banking Deposits</TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(data.cashOutflows.bankingDeposits?.total || 0)}</TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {data.cashOutflows.bankingDeposits?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0}\r\n                  </TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {data.cashOutflows.total ?\r\n                      `${(((data.cashOutflows.bankingDeposits?.total || 0) / data.cashOutflows.total) * 100).toFixed(1)}%` :\r\n                      '0%'}\r\n                  </TableCell>\r\n                </TableRow>\r\n\r\n                <TableRow className=\"font-bold\">\r\n                  <TableCell>Total Outflows</TableCell>\r\n                  <TableCell className=\"text-right\">{formatCurrency(data.cashOutflows.total || 0)}</TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    {(data.cashOutflows.expenses?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0) +\r\n                     (data.cashOutflows.bankingDeposits?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0)}\r\n                  </TableCell>\r\n                  <TableCell className=\"text-right\">100%</TableCell>\r\n                </TableRow>\r\n              </TableBody>\r\n            </Table>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"text-center py-8\">\r\n      <p className=\"text-muted-foreground\">Please select a view to see detailed breakdown.</p>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AACA;;;;;AASO,SAAS,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAwB;IAC7E,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCACR,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;kCAGnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;IAG3C;IAEA,IAAI,SAAS,YAAY,KAAK,QAAQ,EAAE;QACtC,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;;;;;;8BAEb,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,iIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;sDACP,8OAAC,iIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;sDAClC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;sDAClC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;sDAClC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;sDAClC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;;;;;;;;;;;;0CAGtC,8OAAC,iIAAA,CAAA,YAAS;0CACP,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,uBAClB,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAe,OAAO,WAAW;;;;;;0DACtD,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,gBAAgB;;;;;;0DACjF,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA6B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,YAAY;;;;;;0DAC5F,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA2B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa;;;;;;0DAC3F,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAW,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI,IAAI,mBAAmB,gBAAgB;0DACxG,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa;;;;;;0DAE9C,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,gBAAgB;;;;;;;uCARpE,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgB7C;IAEA,IAAI,SAAS,YAAY,KAAK,QAAQ,EAAE;QACtC,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;;;;;;8BAEb,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,iIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;sDACP,8OAAC,iIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,iIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;sDAClC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;sDAClC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;sDAClC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;sDAClC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;;;;;;;;;;;;0CAGtC,8OAAC,iIAAA,CAAA,YAAS;0CACP,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,uBAClB,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAe,OAAO,WAAW;;;;;;0DACtD,8OAAC,iIAAA,CAAA,YAAS;0DAAE,OAAO,WAAW;;;;;;0DAC9B,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,gBAAgB;;;;;;0DACjF,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA6B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,YAAY;;;;;;0DAC5F,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA2B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa;;;;;;0DAC3F,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAW,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI,IAAI,mBAAmB,gBAAgB;0DACxG,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa;;;;;;0DAE9C,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,gBAAgB;;;;;;;uCATpE,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiB7C;IAEA,IAAI,SAAS,WAAW;QACtB,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kDACJ,8OAAC,iIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;8DACP,8OAAC,iIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;8DAClC,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;8DAClC,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,8OAAC,iIAAA,CAAA,YAAS;;0DACR,8OAAC,iIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;kEACnC,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW,CAAC,SAAS,EAAE,SAAS;;;;;;kEACvF,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,KAAK,WAAW,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM;;;;;;kEAEtG,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,KAAK,WAAW,CAAC,KAAK,GACrB,GAAG,CAAC,AAAC,CAAC,KAAK,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,KAAK,WAAW,CAAC,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAC5F;;;;;;;;;;;;0DAGN,8OAAC,iIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;kEACnC,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW,CAAC,aAAa,EAAE,SAAS;;;;;;kEAC3F,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,KAAK,WAAW,CAAC,aAAa,EAAE,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM;;;;;;kEAE1G,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,KAAK,WAAW,CAAC,KAAK,GACrB,GAAG,CAAC,AAAC,CAAC,KAAK,WAAW,CAAC,aAAa,EAAE,SAAS,CAAC,IAAI,KAAK,WAAW,CAAC,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAChG;;;;;;;;;;;;0DAIN,8OAAC,iIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW,CAAC,KAAK,IAAI;;;;;;kEAC5E,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,CAAC,KAAK,WAAW,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM,CAAC,IACrG,CAAC,KAAK,WAAW,CAAC,aAAa,EAAE,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM,CAAC;;;;;;kEAE5G,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ5C,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kDACJ,8OAAC,iIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;8DACP,8OAAC,iIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;8DAClC,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;8DAClC,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,8OAAC,iIAAA,CAAA,YAAS;;0DACR,8OAAC,iIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;kEACnC,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY,CAAC,QAAQ,EAAE,SAAS;;;;;;kEACvF,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,KAAK,YAAY,CAAC,QAAQ,EAAE,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM;;;;;;kEAEtG,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,KAAK,YAAY,CAAC,KAAK,GACtB,GAAG,CAAC,AAAC,CAAC,KAAK,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAC7F;;;;;;;;;;;;0DAGN,8OAAC,iIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;kEACnC,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY,CAAC,eAAe,EAAE,SAAS;;;;;;kEAC9F,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,KAAK,YAAY,CAAC,eAAe,EAAE,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM;;;;;;kEAE7G,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,KAAK,YAAY,CAAC,KAAK,GACtB,GAAG,CAAC,AAAC,CAAC,KAAK,YAAY,CAAC,eAAe,EAAE,SAAS,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACpG;;;;;;;;;;;;0DAIN,8OAAC,iIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY,CAAC,KAAK,IAAI;;;;;;kEAC7E,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,CAAC,KAAK,YAAY,CAAC,QAAQ,EAAE,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM,CAAC,IACrG,CAAC,KAAK,YAAY,CAAC,eAAe,EAAE,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM,CAAC;;;;;;kEAE/G,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQlD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAE,WAAU;sBAAwB;;;;;;;;;;;AAG3C", "debugId": null}}, {"offset": {"line": 5780, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { DayPicker } from \"react-day-picker\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DayPicker>) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row gap-2\",\r\n        month: \"flex flex-col gap-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center w-full\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"flex items-center gap-1\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-x-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: cn(\r\n          \"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md\",\r\n          props.mode === \"range\"\r\n            ? \"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md\"\r\n            : \"[&:has([aria-selected])]:rounded-md\"\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"size-8 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_start:\r\n          \"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_range_end:\r\n          \"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground aria-selected:text-muted-foreground\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Calendar }\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AASA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACoC;IACvC,qBACE,8OAAC,8JAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YAC<PERSON>,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACL,mKACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBACE;YACF,eACE;YACF,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAE5D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;QAE/D;QACC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5860, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 5929, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 6154, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\r\nimport apiClient from './api-client';\r\nimport { getCookie } from './cookies';\r\n\r\nexport const api = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add a request interceptor\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    // Get the token from cookies\r\n    const token = typeof window !== 'undefined' ? getCookie('accessToken') : null;\r\n\r\n    // If token exists, add it to the headers\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // Add cache-busting query parameter for GET requests\r\n    // This prevents 304 Not Modified responses without causing CORS issues\r\n    if (config.method?.toLowerCase() === 'get') {\r\n      config.params = {\r\n        ...config.params,\r\n        _t: new Date().getTime()\r\n      };\r\n    }\r\n\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add a response interceptor\r\napi.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Handle unauthorized errors (401)\r\n    if (error.response && error.response.status === 401) {\r\n      // Use the same auth error handler as the main API client\r\n      apiClient.handleAuthError(\"Your session has expired. Please log in again.\");\r\n    }\r\n\r\n    // Enhanced error logging for server errors (500)\r\n    if (error.response && error.response.status === 500) {\r\n      console.error('Server error (500) details:', {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        params: error.config?.params,\r\n        data: error.config?.data,\r\n        responseData: error.response?.data\r\n      });\r\n\r\n      // Add more context to the error\r\n      error.message = `Server error (500): ${error.message}. URL: ${error.config?.url}`;\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,OAAO;IACP,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,4BAA4B;AAC5B,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,6BAA6B;IAC7B,MAAM,QAAQ,6EAA2D;IAEzE,yCAAyC;IACzC,uCAAW;;IAEX;IAEA,qDAAqD;IACrD,uEAAuE;IACvE,IAAI,OAAO,MAAM,EAAE,kBAAkB,OAAO;QAC1C,OAAO,MAAM,GAAG;YACd,GAAG,OAAO,MAAM;YAChB,IAAI,IAAI,OAAO,OAAO;QACxB;IACF;IAEA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,6BAA6B;AAC7B,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,mCAAmC;IACnC,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;QACnD,yDAAyD;QACzD,2HAAA,CAAA,UAAS,CAAC,eAAe,CAAC;IAC5B;IAEA,iDAAiD;IACjD,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;QACnD,QAAQ,KAAK,CAAC,+BAA+B;YAC3C,KAAK,MAAM,MAAM,EAAE;YACnB,QAAQ,MAAM,MAAM,EAAE;YACtB,QAAQ,MAAM,MAAM,EAAE;YACtB,MAAM,MAAM,MAAM,EAAE;YACpB,cAAc,MAAM,QAAQ,EAAE;QAChC;QAEA,gCAAgC;QAChC,MAAM,OAAO,GAAG,CAAC,oBAAoB,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE,MAAM,MAAM,EAAE,KAAK;IACnF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 6218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/hooks/useBranches.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { api } from '@/lib/api';\r\nimport { toast } from 'sonner';\r\n\r\ninterface Region {\r\n  id: number;\r\n  name: string;\r\n  code?: string;\r\n}\r\n\r\nexport interface Branch {\r\n  id: number;\r\n  name: string;\r\n  location?: string;\r\n  region_id?: number;\r\n  level?: number;\r\n  tenant_id?: number;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n  deleted_at?: string | null;\r\n  Region?: Region;\r\n}\r\n\r\nexport interface BranchesResponse {\r\n  status?: string;\r\n  data: Branch[];\r\n}\r\n\r\nexport const useBranches = () => {\r\n  const [branches, setBranches] = useState<Branch[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(true); // Start with loading true\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchBranches = async () => {\r\n      setLoading(true);\r\n      setError(null);\r\n      try {\r\n        console.log('Fetching branches...');\r\n        // Make sure we're using the correct API endpoint\r\n        const response = await api.get<any>('/branches');\r\n        console.log('Branches response:', response);\r\n\r\n        let branchesData: Branch[] = [];\r\n\r\n        // Handle different response formats\r\n        if (Array.isArray(response)) {\r\n          // If response is an array, use it directly\r\n          branchesData = response;\r\n        } else if (response && Array.isArray(response.data)) {\r\n          // If response has a data property that's an array, use that\r\n          branchesData = response.data;\r\n        } else if (response && typeof response === 'object') {\r\n          // If response is an object, try to extract branches\r\n          branchesData = response;\r\n        }\r\n\r\n        // Ensure each branch has a Region property\r\n        const processedData = branchesData.map(branch => {\r\n          // If Region is missing, create a default one based on region_id\r\n          if (!branch.Region && branch.region_id) {\r\n            return {\r\n              ...branch,\r\n              Region: {\r\n                id: branch.region_id,\r\n                name: `Region ${branch.region_id}`,\r\n              }\r\n            };\r\n          }\r\n          return branch;\r\n        });\r\n\r\n        setBranches(processedData);\r\n        console.log('Processed branches data:', processedData);\r\n      } catch (err: any) {\r\n        console.error('Error fetching branches:', err);\r\n        setError(err.message || 'Failed to fetch branches');\r\n        toast.error('Failed to fetch branches');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchBranches();\r\n  }, []);\r\n\r\n  return {\r\n    data: { data: branches },\r\n    loading,\r\n    error,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AA0BO,MAAM,cAAc;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,OAAO,0BAA0B;IACjF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,WAAW;YACX,SAAS;YACT,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,iDAAiD;gBACjD,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,GAAG,CAAM;gBACpC,QAAQ,GAAG,CAAC,sBAAsB;gBAElC,IAAI,eAAyB,EAAE;gBAE/B,oCAAoC;gBACpC,IAAI,MAAM,OAAO,CAAC,WAAW;oBAC3B,2CAA2C;oBAC3C,eAAe;gBACjB,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACnD,4DAA4D;oBAC5D,eAAe,SAAS,IAAI;gBAC9B,OAAO,IAAI,YAAY,OAAO,aAAa,UAAU;oBACnD,oDAAoD;oBACpD,eAAe;gBACjB;gBAEA,2CAA2C;gBAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC,CAAA;oBACrC,gEAAgE;oBAChE,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,SAAS,EAAE;wBACtC,OAAO;4BACL,GAAG,MAAM;4BACT,QAAQ;gCACN,IAAI,OAAO,SAAS;gCACpB,MAAM,CAAC,OAAO,EAAE,OAAO,SAAS,EAAE;4BACpC;wBACF;oBACF;oBACA,OAAO;gBACT;gBAEA,YAAY;gBACZ,QAAQ,GAAG,CAAC,4BAA4B;YAC1C,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,SAAS,IAAI,OAAO,IAAI;gBACxB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,OAAO;QACL,MAAM;YAAE,MAAM;QAAS;QACvB;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 6292, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/cash-status/report-filters.tsx"], "sourcesContent": ["import { useState, useMemo } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { format } from 'date-fns';\r\nimport { CalendarIcon, RefreshCw } from 'lucide-react';\r\nimport { useBranches } from '@/features/branches/hooks/useBranches';\r\n\r\ninterface ReportFiltersProps {\r\n  startDate: Date;\r\n  endDate: Date;\r\n  regionId: number | null;\r\n  branchId: number | null;\r\n  onStartDateChange: (date: Date) => void;\r\n  onEndDateChange: (date: Date) => void;\r\n  onRegionChange: (regionId: number | null) => void;\r\n  onBranchChange: (branchId: number | null) => void;\r\n  onRefresh: () => void;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport function ReportFilters({\r\n  startDate,\r\n  endDate,\r\n  regionId,\r\n  branchId,\r\n  onStartDateChange,\r\n  onEndDateChange,\r\n  onRegionChange,\r\n  onBranchChange,\r\n  onRefresh,\r\n  isLoading\r\n}: ReportFiltersProps) {\r\n  const [startOpen, setStartOpen] = useState(false);\r\n  const [endOpen, setEndOpen] = useState(false);\r\n\r\n  const { data: branchesData, loading: branchesLoading, error: branchesError } = useBranches();\r\n\r\n  // Log for debugging\r\n  console.log('Branches data:', branchesData);\r\n  console.log('Branches loading:', branchesLoading);\r\n  console.log('Branches error:', branchesError);\r\n\r\n  // Extract regions from branches\r\n  const regions = useMemo(() => {\r\n    if (!branchesData?.data || branchesData.data.length === 0) {\r\n      console.log('No branches data available for regions');\r\n      return [];\r\n    }\r\n\r\n    // Create a map of unique regions\r\n    const regionMap = new Map();\r\n\r\n    branchesData.data.forEach(branch => {\r\n      if (branch.Region && branch.Region.id && branch.Region.name) {\r\n        regionMap.set(branch.Region.id, branch.Region);\r\n      } else if (branch.region_id) {\r\n        // If Region object is missing but region_id exists, create a placeholder\r\n        regionMap.set(branch.region_id, {\r\n          id: branch.region_id,\r\n          name: `Region ${branch.region_id}`\r\n        });\r\n      }\r\n    });\r\n\r\n    // Convert map to array and sort by name\r\n    const regionArray = Array.from(regionMap.values())\r\n      .sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    console.log('Extracted regions:', regionArray);\r\n    return regionArray;\r\n  }, [branchesData?.data]);\r\n\r\n  // Log regions for debugging\r\n  console.log('Extracted regions:', regions);\r\n\r\n  // Filter branches by selected region\r\n  const filteredBranches = useMemo(() => {\r\n    if (!branchesData?.data || branchesData.data.length === 0) {\r\n      console.log('No branches data available for filtering');\r\n      return [];\r\n    }\r\n\r\n    const filtered = branchesData.data\r\n      .filter(branch => {\r\n        if (!regionId) return true; // Show all branches when no region is selected\r\n\r\n        // Check if branch has Region object with matching id\r\n        if (branch.Region && branch.Region.id === regionId) return true;\r\n\r\n        // Fallback to region_id if Region object is missing\r\n        return branch.region_id === regionId;\r\n      })\r\n      .sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    console.log(`Filtered branches for region ${regionId}:`, filtered);\r\n    return filtered;\r\n  }, [branchesData?.data, regionId]);\r\n\r\n  // Log filtered branches for debugging\r\n  console.log('Filtered branches:', filteredBranches);\r\n\r\n  return (\r\n    <div className=\"p-4 bg-muted/30 rounded-lg\">\r\n      <div className=\"flex flex-wrap gap-3 items-end\">\r\n        <div className=\"w-full sm:w-auto flex-1 min-w-[150px] max-w-[200px]\">\r\n          <label className=\"text-sm font-medium mb-1 block\">Start Date</label>\r\n          <Popover open={startOpen} onOpenChange={setStartOpen}>\r\n            <PopoverTrigger asChild>\r\n              <Button\r\n                variant=\"outline\"\r\n                className=\"w-full justify-start text-left font-normal\"\r\n              >\r\n                <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                {startDate ? format(startDate, 'MMM d, yyyy') : 'Select date'}\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"w-auto p-0\">\r\n              <Calendar\r\n                mode=\"single\"\r\n                selected={startDate}\r\n                onSelect={(date) => {\r\n                  if (date) {\r\n                    onStartDateChange(date);\r\n                    setStartOpen(false);\r\n                  }\r\n                }}\r\n                initialFocus\r\n              />\r\n            </PopoverContent>\r\n          </Popover>\r\n        </div>\r\n\r\n        <div className=\"w-full sm:w-auto flex-1 min-w-[150px] max-w-[200px]\">\r\n          <label className=\"text-sm font-medium mb-1 block\">End Date</label>\r\n          <Popover open={endOpen} onOpenChange={setEndOpen}>\r\n            <PopoverTrigger asChild>\r\n              <Button\r\n                variant=\"outline\"\r\n                className=\"w-full justify-start text-left font-normal\"\r\n              >\r\n                <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                {endDate ? format(endDate, 'MMM d, yyyy') : 'Select date'}\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"w-auto p-0\">\r\n              <Calendar\r\n                mode=\"single\"\r\n                selected={endDate}\r\n                onSelect={(date) => {\r\n                  if (date) {\r\n                    onEndDateChange(date);\r\n                    setEndOpen(false);\r\n                  }\r\n                }}\r\n                initialFocus\r\n              />\r\n            </PopoverContent>\r\n          </Popover>\r\n        </div>\r\n\r\n        <div className=\"w-full sm:w-auto flex-1 min-w-[150px] max-w-[200px]\">\r\n          <label className=\"text-sm font-medium mb-1 block\">Region</label>\r\n          <Select\r\n            value={regionId?.toString() || 'all'}\r\n            onValueChange={(value) => {\r\n              console.log(`Region selection changed to: ${value}`);\r\n              const newRegionId = value !== 'all' ? parseInt(value) : null;\r\n              onRegionChange(newRegionId);\r\n              // Reset branch when region changes\r\n              onBranchChange(null);\r\n            }}\r\n            disabled={branchesLoading || regions.length === 0}\r\n          >\r\n            <SelectTrigger>\r\n              <SelectValue placeholder={branchesLoading ? \"Loading...\" : \"All Regions\"} />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"all\">All Regions</SelectItem>\r\n              {regions.map((region) => (\r\n                <SelectItem key={region.id} value={region.id.toString()}>\r\n                  {region.name}\r\n                </SelectItem>\r\n              ))}\r\n              {regions.length === 0 && !branchesLoading && (\r\n                <SelectItem value=\"none\" disabled>No regions available</SelectItem>\r\n              )}\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n\r\n        <div className=\"w-full sm:w-auto flex-1 min-w-[150px] max-w-[200px]\">\r\n          <label className=\"text-sm font-medium mb-1 block\">Branch</label>\r\n          <Select\r\n            value={branchId?.toString() || 'all'}\r\n            onValueChange={(value) => {\r\n              console.log(`Branch selection changed to: ${value}`);\r\n              const newBranchId = value !== 'all' ? parseInt(value) : null;\r\n              onBranchChange(newBranchId);\r\n            }}\r\n            disabled={branchesLoading || filteredBranches.length === 0}\r\n          >\r\n            <SelectTrigger>\r\n              <SelectValue placeholder={branchesLoading ? \"Loading...\" : \"All Branches\"} />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"all\">All Branches</SelectItem>\r\n              {filteredBranches.map((branch) => (\r\n                <SelectItem key={branch.id} value={branch.id.toString()}>\r\n                  {branch.name}\r\n                </SelectItem>\r\n              ))}\r\n              {filteredBranches.length === 0 && !branchesLoading && (\r\n                <SelectItem value=\"none\" disabled>\r\n                  {regionId ? \"No branches in this region\" : \"No branches available\"}\r\n                </SelectItem>\r\n              )}\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n\r\n        <div className=\"w-full sm:w-auto\">\r\n          <Button onClick={onRefresh} disabled={isLoading} className=\"w-full sm:w-auto\">\r\n            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />\r\n            {isLoading ? 'Loading...' : 'Refresh'}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAKA;AAOA;AACA;AAAA;AACA;;;;;;;;;;AAeO,SAAS,cAAc,EAC5B,SAAS,EACT,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,iBAAiB,EACjB,eAAe,EACf,cAAc,EACd,cAAc,EACd,SAAS,EACT,SAAS,EACU;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EAAE,MAAM,YAAY,EAAE,SAAS,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,cAAW,AAAD;IAEzF,oBAAoB;IACpB,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,qBAAqB;IACjC,QAAQ,GAAG,CAAC,mBAAmB;IAE/B,gCAAgC;IAChC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACtB,IAAI,CAAC,cAAc,QAAQ,aAAa,IAAI,CAAC,MAAM,KAAK,GAAG;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO,EAAE;QACX;QAEA,iCAAiC;QACjC,MAAM,YAAY,IAAI;QAEtB,aAAa,IAAI,CAAC,OAAO,CAAC,CAAA;YACxB,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE;gBAC3D,UAAU,GAAG,CAAC,OAAO,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM;YAC/C,OAAO,IAAI,OAAO,SAAS,EAAE;gBAC3B,yEAAyE;gBACzE,UAAU,GAAG,CAAC,OAAO,SAAS,EAAE;oBAC9B,IAAI,OAAO,SAAS;oBACpB,MAAM,CAAC,OAAO,EAAE,OAAO,SAAS,EAAE;gBACpC;YACF;QACF;QAEA,wCAAwC;QACxC,MAAM,cAAc,MAAM,IAAI,CAAC,UAAU,MAAM,IAC5C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QAE7C,QAAQ,GAAG,CAAC,sBAAsB;QAClC,OAAO;IACT,GAAG;QAAC,cAAc;KAAK;IAEvB,4BAA4B;IAC5B,QAAQ,GAAG,CAAC,sBAAsB;IAElC,qCAAqC;IACrC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,CAAC,cAAc,QAAQ,aAAa,IAAI,CAAC,MAAM,KAAK,GAAG;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO,EAAE;QACX;QAEA,MAAM,WAAW,aAAa,IAAI,CAC/B,MAAM,CAAC,CAAA;YACN,IAAI,CAAC,UAAU,OAAO,MAAM,+CAA+C;YAE3E,qDAAqD;YACrD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU,OAAO;YAE3D,oDAAoD;YACpD,OAAO,OAAO,SAAS,KAAK;QAC9B,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QAE7C,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC,EAAE;QACzD,OAAO;IACT,GAAG;QAAC,cAAc;QAAM;KAAS;IAEjC,sCAAsC;IACtC,QAAQ,GAAG,CAAC,sBAAsB;IAElC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCAAiC;;;;;;sCAClD,8OAAC,mIAAA,CAAA,UAAO;4BAAC,MAAM;4BAAW,cAAc;;8CACtC,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CACvB,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,iBAAiB;;;;;;;;;;;;8CAGpD,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wCACP,MAAK;wCACL,UAAU;wCACV,UAAU,CAAC;4CACT,IAAI,MAAM;gDACR,kBAAkB;gDAClB,aAAa;4CACf;wCACF;wCACA,YAAY;;;;;;;;;;;;;;;;;;;;;;;8BAMpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCAAiC;;;;;;sCAClD,8OAAC,mIAAA,CAAA,UAAO;4BAAC,MAAM;4BAAS,cAAc;;8CACpC,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CACvB,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,iBAAiB;;;;;;;;;;;;8CAGhD,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wCACP,MAAK;wCACL,UAAU;wCACV,UAAU,CAAC;4CACT,IAAI,MAAM;gDACR,gBAAgB;gDAChB,WAAW;4CACb;wCACF;wCACA,YAAY;;;;;;;;;;;;;;;;;;;;;;;8BAMpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCAAiC;;;;;;sCAClD,8OAAC,kIAAA,CAAA,SAAM;4BACL,OAAO,UAAU,cAAc;4BAC/B,eAAe,CAAC;gCACd,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO;gCACnD,MAAM,cAAc,UAAU,QAAQ,SAAS,SAAS;gCACxD,eAAe;gCACf,mCAAmC;gCACnC,eAAe;4BACjB;4BACA,UAAU,mBAAmB,QAAQ,MAAM,KAAK;;8CAEhD,8OAAC,kIAAA,CAAA,gBAAa;8CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wCAAC,aAAa,kBAAkB,eAAe;;;;;;;;;;;8CAE7D,8OAAC,kIAAA,CAAA,gBAAa;;sDACZ,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAM;;;;;;wCACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;0DAClD,OAAO,IAAI;+CADG,OAAO,EAAE;;;;;wCAI3B,QAAQ,MAAM,KAAK,KAAK,CAAC,iCACxB,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;4CAAO,QAAQ;sDAAC;;;;;;;;;;;;;;;;;;;;;;;;8BAM1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCAAiC;;;;;;sCAClD,8OAAC,kIAAA,CAAA,SAAM;4BACL,OAAO,UAAU,cAAc;4BAC/B,eAAe,CAAC;gCACd,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO;gCACnD,MAAM,cAAc,UAAU,QAAQ,SAAS,SAAS;gCACxD,eAAe;4BACjB;4BACA,UAAU,mBAAmB,iBAAiB,MAAM,KAAK;;8CAEzD,8OAAC,kIAAA,CAAA,gBAAa;8CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wCAAC,aAAa,kBAAkB,eAAe;;;;;;;;;;;8CAE7D,8OAAC,kIAAA,CAAA,gBAAa;;sDACZ,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAM;;;;;;wCACvB,iBAAiB,GAAG,CAAC,CAAC,uBACrB,8OAAC,kIAAA,CAAA,aAAU;gDAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;0DAClD,OAAO,IAAI;+CADG,OAAO,EAAE;;;;;wCAI3B,iBAAiB,MAAM,KAAK,KAAK,CAAC,iCACjC,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;4CAAO,QAAQ;sDAC9B,WAAW,+BAA+B;;;;;;;;;;;;;;;;;;;;;;;;8BAOrD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAW,UAAU;wBAAW,WAAU;;0CACzD,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAW,CAAC,aAAa,EAAE,YAAY,iBAAiB,IAAI;;;;;;4BACtE,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 6726, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/cash-status/summary-cards.tsx"], "sourcesContent": ["import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { ArrowDownIcon, ArrowUpIcon, CircleDollarSignIcon, WalletIcon } from 'lucide-react';\r\nimport { formatCurrency } from '@/lib/utils';\r\n\r\ninterface SummaryCardsProps {\r\n  summary: {\r\n    totalOpeningCash: number;\r\n    totalClosingCash: number;\r\n    totalInflows: number;\r\n    totalOutflows: number;\r\n    netCashChange: number;\r\n  } | null;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport function SummaryCards({ summary, isLoading }: SummaryCardsProps) {\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\r\n        {Array.from({ length: 4 }).map((_, index) => (\r\n          <Card key={index} className=\"animate-pulse\">\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                <div className=\"h-4 w-24 bg-muted rounded\"></div>\r\n              </CardTitle>\r\n              <div className=\"h-4 w-4 bg-muted rounded-full\"></div>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                <div className=\"h-8 w-32 bg-muted rounded\"></div>\r\n              </div>\r\n              <div className=\"text-xs text-muted-foreground mt-2\">\r\n                <div className=\"h-3 w-40 bg-muted rounded\"></div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!summary) {\r\n    return (\r\n      <div className=\"text-center py-8\">\r\n        <p className=\"text-muted-foreground\">No data available. Please select a date range and refresh.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const isPositiveChange = summary.netCashChange >= 0;\r\n\r\n  return (\r\n    <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\r\n      <Card>\r\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n          <CardTitle className=\"text-sm font-medium\">Opening Cash</CardTitle>\r\n          <WalletIcon className=\"h-4 w-4 text-muted-foreground\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"text-2xl font-bold\">{formatCurrency(summary.totalOpeningCash)}</div>\r\n          <div className=\"text-xs text-muted-foreground\">Starting cash balance</div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      <Card>\r\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n          <CardTitle className=\"text-sm font-medium\">Cash Inflows</CardTitle>\r\n          <ArrowDownIcon className=\"h-4 w-4 text-green-500\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"text-2xl font-bold text-green-600\">{formatCurrency(summary.totalInflows)}</div>\r\n          <div className=\"text-xs text-muted-foreground\">Total cash received</div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      <Card>\r\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n          <CardTitle className=\"text-sm font-medium\">Cash Outflows</CardTitle>\r\n          <ArrowUpIcon className=\"h-4 w-4 text-red-500\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"text-2xl font-bold text-red-600\">{formatCurrency(summary.totalOutflows)}</div>\r\n          <div className=\"text-xs text-muted-foreground\">Total cash spent</div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      <Card>\r\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n          <CardTitle className=\"text-sm font-medium\">Net Change</CardTitle>\r\n          {isPositiveChange ? (\r\n            <ArrowUpIcon className=\"h-4 w-4 text-green-500\" />\r\n          ) : (\r\n            <ArrowDownIcon className=\"h-4 w-4 text-red-500\" />\r\n          )}\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className={`text-2xl font-bold ${isPositiveChange ? 'text-green-600' : 'text-red-600'}`}>\r\n            {formatCurrency(summary.netCashChange)}\r\n          </div>\r\n          <div className=\"text-xs text-muted-foreground\">Net cash flow</div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;;;;;AAaO,SAAS,aAAa,EAAE,OAAO,EAAE,SAAS,EAAqB;IACpE,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC,gIAAA,CAAA,OAAI;oBAAa,WAAU;;sCAC1B,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CACnB,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC,gIAAA,CAAA,cAAW;;8CACV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;mBAZV;;;;;;;;;;IAmBnB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;IAG3C;IAEA,MAAM,mBAAmB,QAAQ,aAAa,IAAI;IAElD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,0MAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;kCAExB,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CAAsB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,gBAAgB;;;;;;0CAC5E,8OAAC;gCAAI,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;0BAInD,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,oNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;;kCAE3B,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CAAqC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;;;;;;0CACvF,8OAAC;gCAAI,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;0BAInD,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,gNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;kCAEzB,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CAAmC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;;;;;;0CACtF,8OAAC;gCAAI,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;0BAInD,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;4BAC1C,iCACC,8OAAC,gNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,oNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;;kCAG7B,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAW,CAAC,mBAAmB,EAAE,mBAAmB,mBAAmB,gBAAgB;0CACzF,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}, {"offset": {"line": 7090, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/services/report-service.ts"], "sourcesContent": ["import { api } from '@/lib/api';\r\n\r\nexport interface CashStatusReportParams {\r\n  start_date: string;\r\n  end_date: string;\r\n  region_id?: number;\r\n  branch_id?: number;\r\n  include_breakdown?: boolean;\r\n}\r\n\r\nexport interface CashStatusReportResponse {\r\n  status: string;\r\n  data: {\r\n    summary: {\r\n      totalOpeningCash: number;\r\n      totalClosingCash: number;\r\n      totalInflows: number;\r\n      totalOutflows: number;\r\n      netCashChange: number;\r\n    };\r\n    cashPosition: Array<{\r\n      branch_id: number;\r\n      branch_name: string;\r\n      region_id: number;\r\n      region_name: string;\r\n      total_opening_cash: number;\r\n      total_closing_cash: number;\r\n      net_cash_change: number;\r\n    }>;\r\n    cashInflows: {\r\n      cashSales: {\r\n        data: Array<{\r\n          branch_id: number;\r\n          branch_name: string;\r\n          region_id: number;\r\n          region_name: string;\r\n          amount: number;\r\n          transaction_count: number;\r\n        }>;\r\n        total: number;\r\n      };\r\n      dsaReconciliations: {\r\n        data: Array<{\r\n          branch_id: number;\r\n          branch_name: string;\r\n          region_id: number;\r\n          region_name: string;\r\n          amount: number;\r\n          transaction_count: number;\r\n        }>;\r\n        total: number;\r\n      };\r\n      floatReturns: {\r\n        data: Array<{\r\n          branch_id: number;\r\n          branch_name: string;\r\n          region_id: number;\r\n          region_name: string;\r\n          amount: number;\r\n          transaction_count: number;\r\n        }>;\r\n        total: number;\r\n      };\r\n      total: number;\r\n    };\r\n    cashOutflows: {\r\n      expenses: {\r\n        data: Array<{\r\n          branch_id: number;\r\n          branch_name: string;\r\n          region_id: number;\r\n          region_name: string;\r\n          amount: number;\r\n          transaction_count: number;\r\n        }>;\r\n        total: number;\r\n      };\r\n      bankingDeposits: {\r\n        data: Array<{\r\n          branch_id: number;\r\n          branch_name: string;\r\n          region_id: number;\r\n          region_name: string;\r\n          amount: number;\r\n          transaction_count: number;\r\n        }>;\r\n        total: number;\r\n      };\r\n      floatAllocations: {\r\n        data: Array<{\r\n          branch_id: number;\r\n          branch_name: string;\r\n          region_id: number;\r\n          region_name: string;\r\n          amount: number;\r\n          transaction_count: number;\r\n        }>;\r\n        total: number;\r\n      };\r\n      total: number;\r\n    };\r\n    byRegion?: Array<{\r\n      region_id: number;\r\n      region_name: string;\r\n      summary: {\r\n        totalOpeningCash: number;\r\n        totalClosingCash: number;\r\n        totalInflows: number;\r\n        totalOutflows: number;\r\n        netCashChange: number;\r\n      };\r\n    }>;\r\n    byBranch?: Array<{\r\n      branch_id: number;\r\n      branch_name: string;\r\n      region_id: number;\r\n      region_name: string;\r\n      summary: {\r\n        totalOpeningCash: number;\r\n        totalClosingCash: number;\r\n        totalInflows: number;\r\n        totalOutflows: number;\r\n        netCashChange: number;\r\n      };\r\n    }>;\r\n  };\r\n}\r\n\r\nexport interface RunningBalancesReportParams {\r\n  start_date: string;\r\n  end_date: string;\r\n  region_id?: number;\r\n  branch_id?: number;\r\n}\r\n\r\nexport interface RunningBalancesReportResponse {\r\n  status: string;\r\n  data: {\r\n    summary: {\r\n      total_cash_balance: number;\r\n      total_mpesa_float_balance: number;\r\n      total_banking_balance: number;\r\n      total_sales: number;\r\n      total_expenses: number;\r\n      net_position: number;\r\n    };\r\n    dailyBalances: Array<{\r\n      date: string;\r\n      cash_balance: number;\r\n      mpesa_float_balance: number;\r\n      banking_balance: number;\r\n      sales_total: number;\r\n      expenses_total: number;\r\n      net_position: number;\r\n    }>;\r\n    cashBalances: Array<any>;\r\n    mpesaFloatBalances: Array<any>;\r\n    bankingBalances: Array<any>;\r\n    salesByPaymentMethod: Array<any>;\r\n    expenses: Array<any>;\r\n  };\r\n}\r\n\r\nexport const reportService = {\r\n  getRunningBalancesReport: async (params: RunningBalancesReportParams): Promise<RunningBalancesReportResponse> => {\r\n    try {\r\n      // Log the full URL being requested\r\n      const url = `/reports/running-balances`;\r\n      console.log(`Making request to ${url} with params:`, JSON.stringify(params, null, 2));\r\n\r\n      // Validate required parameters\r\n      if (!params.start_date || !params.end_date) {\r\n        console.error('Missing required date parameters:', params);\r\n        throw new Error('Start date and end date are required for running balances report');\r\n      }\r\n\r\n      // Ensure dates are properly formatted\r\n      const formattedParams = {\r\n        ...params,\r\n        start_date: params.start_date.split('T')[0], // Ensure we only send the date part\r\n        end_date: params.end_date.split('T')[0]\r\n      };\r\n\r\n      console.log(`Making request with formatted params:`, JSON.stringify(formattedParams, null, 2));\r\n\r\n      const response = await api.get(url, {\r\n        params: formattedParams,\r\n        // Add timeout to prevent hanging requests\r\n        timeout: 60000, // Increased timeout to 60 seconds\r\n        headers: {\r\n          'Accept': 'application/json'\r\n        }\r\n      });\r\n\r\n      // Validate the response\r\n      if (!response.data || !response.data.data) {\r\n        console.error('Invalid response format:', response.data);\r\n        throw new Error('Invalid response format from server');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching running balances report:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  getCashStatusReport: async (params: CashStatusReportParams): Promise<CashStatusReportResponse> => {\r\n    try {\r\n      // Note: We don't need to add cache-busting here anymore\r\n      // It's now handled globally in the API interceptor\r\n\r\n      // Log the full URL being requested\r\n      const url = `/reports/cash-status`;\r\n      console.log(`Making request to ${url} with params:`, JSON.stringify(params, null, 2));\r\n\r\n      // Validate required parameters\r\n      if (!params.start_date || !params.end_date) {\r\n        console.error('Missing required date parameters:', params);\r\n        throw new Error('Start date and end date are required for cash status report');\r\n      }\r\n\r\n      // Ensure dates are properly formatted\r\n      const formattedParams = {\r\n        ...params,\r\n        start_date: params.start_date.split('T')[0], // Ensure we only send the date part\r\n        end_date: params.end_date.split('T')[0]\r\n      };\r\n\r\n      console.log(`Making request with formatted params:`, JSON.stringify(formattedParams, null, 2));\r\n\r\n      const response = await api.get(url, {\r\n        params: formattedParams,\r\n        // Add timeout to prevent hanging requests\r\n        timeout: 60000, // Increased timeout to 60 seconds\r\n        headers: {\r\n          'Accept': 'application/json'\r\n        }\r\n      });\r\n\r\n      // Validate the response\r\n      if (!response.data || !response.data.data) {\r\n        console.error('Invalid response format:', response.data);\r\n        throw new Error('Invalid response format from server');\r\n      }\r\n\r\n      // Check if summary contains all zeros\r\n      const summary = response.data.data.summary;\r\n      if (summary &&\r\n          summary.totalOpeningCash === 0 &&\r\n          summary.totalClosingCash === 0 &&\r\n          summary.totalInflows === 0 &&\r\n          summary.totalOutflows === 0) {\r\n        console.warn('Cash status report returned all zeros. This might indicate no data for the selected period.');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error in getCashStatusReport service:', error);\r\n\r\n      // Enhanced error logging\r\n      if (error.response) {\r\n        // The request was made and the server responded with a status code\r\n        // that falls out of the range of 2xx\r\n        console.error('Server responded with error:', {\r\n          status: error.response.status,\r\n          statusText: error.response.statusText,\r\n          data: error.response.data,\r\n          headers: error.response.headers\r\n        });\r\n\r\n        // If we have a more specific error message from the server, use it\r\n        if (error.response.data && error.response.data.message) {\r\n          throw new Error(`Server error: ${error.response.data.message}`);\r\n        }\r\n      } else if (error.request) {\r\n        // The request was made but no response was received\r\n        console.error('No response received from server:', error.request);\r\n        throw new Error('No response received from server. Please check your network connection.');\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAmKO,MAAM,gBAAgB;IAC3B,0BAA0B,OAAO;QAC/B,IAAI;YACF,mCAAmC;YACnC,MAAM,MAAM,CAAC,yBAAyB,CAAC;YACvC,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,aAAa,CAAC,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM;YAElF,+BAA+B;YAC/B,IAAI,CAAC,OAAO,UAAU,IAAI,CAAC,OAAO,QAAQ,EAAE;gBAC1C,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,MAAM,IAAI,MAAM;YAClB;YAEA,sCAAsC;YACtC,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,YAAY,OAAO,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3C,UAAU,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACzC;YAEA,QAAQ,GAAG,CAAC,CAAC,qCAAqC,CAAC,EAAE,KAAK,SAAS,CAAC,iBAAiB,MAAM;YAE3F,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,KAAK;gBAClC,QAAQ;gBACR,0CAA0C;gBAC1C,SAAS;gBACT,SAAS;oBACP,UAAU;gBACZ;YACF;YAEA,wBAAwB;YACxB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE;gBACzC,QAAQ,KAAK,CAAC,4BAA4B,SAAS,IAAI;gBACvD,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACR;IACF;IAEA,qBAAqB,OAAO;QAC1B,IAAI;YACF,wDAAwD;YACxD,mDAAmD;YAEnD,mCAAmC;YACnC,MAAM,MAAM,CAAC,oBAAoB,CAAC;YAClC,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,aAAa,CAAC,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM;YAElF,+BAA+B;YAC/B,IAAI,CAAC,OAAO,UAAU,IAAI,CAAC,OAAO,QAAQ,EAAE;gBAC1C,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,MAAM,IAAI,MAAM;YAClB;YAEA,sCAAsC;YACtC,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,YAAY,OAAO,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3C,UAAU,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACzC;YAEA,QAAQ,GAAG,CAAC,CAAC,qCAAqC,CAAC,EAAE,KAAK,SAAS,CAAC,iBAAiB,MAAM;YAE3F,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,KAAK;gBAClC,QAAQ;gBACR,0CAA0C;gBAC1C,SAAS;gBACT,SAAS;oBACP,UAAU;gBACZ;YACF;YAEA,wBAAwB;YACxB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE;gBACzC,QAAQ,KAAK,CAAC,4BAA4B,SAAS,IAAI;gBACvD,MAAM,IAAI,MAAM;YAClB;YAEA,sCAAsC;YACtC,MAAM,UAAU,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;YAC1C,IAAI,WACA,QAAQ,gBAAgB,KAAK,KAC7B,QAAQ,gBAAgB,KAAK,KAC7B,QAAQ,YAAY,KAAK,KACzB,QAAQ,aAAa,KAAK,GAAG;gBAC/B,QAAQ,IAAI,CAAC;YACf;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YAEvD,yBAAyB;YACzB,IAAI,MAAM,QAAQ,EAAE;gBAClB,mEAAmE;gBACnE,qCAAqC;gBACrC,QAAQ,KAAK,CAAC,gCAAgC;oBAC5C,QAAQ,MAAM,QAAQ,CAAC,MAAM;oBAC7B,YAAY,MAAM,QAAQ,CAAC,UAAU;oBACrC,MAAM,MAAM,QAAQ,CAAC,IAAI;oBACzB,SAAS,MAAM,QAAQ,CAAC,OAAO;gBACjC;gBAEA,mEAAmE;gBACnE,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBACtD,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBAChE;YACF,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,oDAAoD;gBACpD,QAAQ,KAAK,CAAC,qCAAqC,MAAM,OAAO;gBAChE,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 7201, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/hooks/useCashStatusReport.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\r\nimport { reportService, CashStatusReportParams, CashStatusReportResponse } from '@/services/report-service';\r\nimport { useToast } from '@/components/ui/use-toast';\r\n\r\ninterface UseCashStatusReportProps {\r\n  startDate: string;\r\n  endDate: string;\r\n  regionId?: number;\r\n  branchId?: number;\r\n  includeBreakdown?: boolean;\r\n  enabled?: boolean;\r\n}\r\n\r\nexport function useCashStatusReport({\r\n  startDate,\r\n  endDate,\r\n  regionId,\r\n  branchId,\r\n  includeBreakdown = true,\r\n  enabled = true\r\n}: UseCashStatusReportProps) {\r\n  const [data, setData] = useState<CashStatusReportResponse['data'] | null>(null);\r\n  const [isLoading, setIsLoading] = useState<boolean>(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { toast } = useToast();\r\n\r\n  const fetchReport = useCallback(async () => {\r\n    if (!startDate || !endDate) {\r\n      console.log('Missing required date parameters, skipping fetch');\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Make sure we're using the correct parameter names expected by the backend\r\n      const params: CashStatusReportParams = {\r\n        start_date: startDate,\r\n        end_date: endDate,\r\n        include_breakdown: includeBreakdown\r\n      };\r\n\r\n      // Only add region_id if it's a valid number and not null/undefined\r\n      if (regionId !== null && regionId !== undefined) {\r\n        params.region_id = regionId;\r\n        console.log(`Adding region filter: ${regionId}`);\r\n      }\r\n\r\n      // Only add branch_id if it's a valid number and not null/undefined\r\n      if (branchId !== null && branchId !== undefined) {\r\n        params.branch_id = branchId;\r\n        console.log(`Adding branch filter: ${branchId}`);\r\n      }\r\n\r\n      console.log('Sending params to backend:', JSON.stringify(params, null, 2));\r\n      console.log('Date parameters:', {\r\n        startDate: typeof startDate === 'string' ? startDate : 'not a string',\r\n        endDate: typeof endDate === 'string' ? endDate : 'not a string',\r\n        startDateFormat: startDate.includes('T') ? 'includes time' : 'date only',\r\n        endDateFormat: endDate.includes('T') ? 'includes time' : 'date only'\r\n      });\r\n\r\n      // Add a small delay to ensure console logs are visible\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      const response = await reportService.getCashStatusReport(params);\r\n      console.log('Received response from backend:', JSON.stringify(response, null, 2));\r\n\r\n      // Check if we have valid data\r\n      if (response && response.data) {\r\n        // Log summary values to debug\r\n        console.log('Summary values:', {\r\n          totalOpeningCash: response.data.summary?.totalOpeningCash,\r\n          totalClosingCash: response.data.summary?.totalClosingCash,\r\n          totalInflows: response.data.summary?.totalInflows,\r\n          totalOutflows: response.data.summary?.totalOutflows,\r\n          netCashChange: response.data.summary?.netCashChange\r\n        });\r\n\r\n        setData(response.data);\r\n      } else {\r\n        console.error('Invalid response format or empty data received');\r\n        setError(new Error('Invalid response format or empty data received'));\r\n        toast({\r\n          title: 'Warning',\r\n          description: 'Received empty data from server. There might be no data for the selected period.',\r\n          variant: 'warning'\r\n        });\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching cash status report:', err);\r\n\r\n      // Extract more detailed error information\r\n      let errorMessage = 'Failed to fetch cash status report';\r\n\r\n      if (err instanceof Error) {\r\n        errorMessage = err.message;\r\n        console.error('Error details:', {\r\n          name: err.name,\r\n          message: err.message,\r\n          stack: err.stack\r\n        });\r\n\r\n        // Check for axios specific error properties\r\n        const axiosError = err as any;\r\n        if (axiosError.isAxiosError) {\r\n          console.error('Axios error details:', {\r\n            status: axiosError.response?.status,\r\n            statusText: axiosError.response?.statusText,\r\n            data: axiosError.response?.data,\r\n            config: {\r\n              url: axiosError.config?.url,\r\n              method: axiosError.config?.method,\r\n              params: axiosError.config?.params,\r\n              headers: axiosError.config?.headers\r\n            }\r\n          });\r\n\r\n          // Use server error message if available\r\n          if (axiosError.response?.data?.message) {\r\n            const serverMessage = axiosError.response.data.message;\r\n\r\n            // Handle specific database connection errors\r\n            if (serverMessage.includes('Database connection is not available') ||\r\n                serverMessage.includes('database connection') ||\r\n                serverMessage.includes('sequelize')) {\r\n              errorMessage = `Database connection error: ${serverMessage}. Please contact your system administrator.`;\r\n\r\n              // Log additional information that might help diagnose the issue\r\n              console.error('Database connection error detected. Server details:', {\r\n                message: serverMessage,\r\n                status: axiosError.response.status,\r\n                timestamp: new Date().toISOString()\r\n              });\r\n            } else {\r\n              errorMessage = `Server error: ${serverMessage}`;\r\n            }\r\n          } else if (axiosError.response?.status === 500) {\r\n            errorMessage = 'Server error: The server encountered an internal error. Please try again later or contact support.';\r\n          }\r\n        }\r\n      }\r\n\r\n      setError(err instanceof Error ? err : new Error(errorMessage));\r\n\r\n      toast({\r\n        title: 'Error',\r\n        description: errorMessage,\r\n        variant: 'destructive'\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [startDate, endDate, regionId, branchId, includeBreakdown, toast]);\r\n\r\n  // Use a ref to track if we've already attempted to fetch with these parameters\r\n  const [hasAttempted, setHasAttempted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // Reset the attempt flag when parameters change\r\n    console.log('Parameters changed, resetting attempt flag');\r\n    console.log('New parameters:', { startDate, endDate, regionId, branchId, includeBreakdown });\r\n    setHasAttempted(false);\r\n  }, [startDate, endDate, regionId, branchId, includeBreakdown]);\r\n\r\n  useEffect(() => {\r\n    if (enabled && !hasAttempted) {\r\n      console.log('Fetching report with parameters:', { startDate, endDate, regionId, branchId, includeBreakdown });\r\n      setHasAttempted(true);\r\n      fetchReport();\r\n    }\r\n  }, [enabled, hasAttempted, fetchReport, startDate, endDate, regionId, branchId, includeBreakdown]);\r\n\r\n  return {\r\n    data,\r\n    isLoading,\r\n    error,\r\n    refetch: fetchReport\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAWO,SAAS,oBAAoB,EAClC,SAAS,EACT,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,mBAAmB,IAAI,EACvB,UAAU,IAAI,EACW;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2C;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,aAAa,CAAC,SAAS;YAC1B,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,4EAA4E;YAC5E,MAAM,SAAiC;gBACrC,YAAY;gBACZ,UAAU;gBACV,mBAAmB;YACrB;YAEA,mEAAmE;YACnE,IAAI,aAAa,QAAQ,aAAa,WAAW;gBAC/C,OAAO,SAAS,GAAG;gBACnB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,UAAU;YACjD;YAEA,mEAAmE;YACnE,IAAI,aAAa,QAAQ,aAAa,WAAW;gBAC/C,OAAO,SAAS,GAAG;gBACnB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,UAAU;YACjD;YAEA,QAAQ,GAAG,CAAC,8BAA8B,KAAK,SAAS,CAAC,QAAQ,MAAM;YACvE,QAAQ,GAAG,CAAC,oBAAoB;gBAC9B,WAAW,OAAO,cAAc,WAAW,YAAY;gBACvD,SAAS,OAAO,YAAY,WAAW,UAAU;gBACjD,iBAAiB,UAAU,QAAQ,CAAC,OAAO,kBAAkB;gBAC7D,eAAe,QAAQ,QAAQ,CAAC,OAAO,kBAAkB;YAC3D;YAEA,uDAAuD;YACvD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,WAAW,MAAM,oIAAA,CAAA,gBAAa,CAAC,mBAAmB,CAAC;YACzD,QAAQ,GAAG,CAAC,mCAAmC,KAAK,SAAS,CAAC,UAAU,MAAM;YAE9E,8BAA8B;YAC9B,IAAI,YAAY,SAAS,IAAI,EAAE;gBAC7B,8BAA8B;gBAC9B,QAAQ,GAAG,CAAC,mBAAmB;oBAC7B,kBAAkB,SAAS,IAAI,CAAC,OAAO,EAAE;oBACzC,kBAAkB,SAAS,IAAI,CAAC,OAAO,EAAE;oBACzC,cAAc,SAAS,IAAI,CAAC,OAAO,EAAE;oBACrC,eAAe,SAAS,IAAI,CAAC,OAAO,EAAE;oBACtC,eAAe,SAAS,IAAI,CAAC,OAAO,EAAE;gBACxC;gBAEA,QAAQ,SAAS,IAAI;YACvB,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,SAAS,IAAI,MAAM;gBACnB,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sCAAsC;YAEpD,0CAA0C;YAC1C,IAAI,eAAe;YAEnB,IAAI,eAAe,OAAO;gBACxB,eAAe,IAAI,OAAO;gBAC1B,QAAQ,KAAK,CAAC,kBAAkB;oBAC9B,MAAM,IAAI,IAAI;oBACd,SAAS,IAAI,OAAO;oBACpB,OAAO,IAAI,KAAK;gBAClB;gBAEA,4CAA4C;gBAC5C,MAAM,aAAa;gBACnB,IAAI,WAAW,YAAY,EAAE;oBAC3B,QAAQ,KAAK,CAAC,wBAAwB;wBACpC,QAAQ,WAAW,QAAQ,EAAE;wBAC7B,YAAY,WAAW,QAAQ,EAAE;wBACjC,MAAM,WAAW,QAAQ,EAAE;wBAC3B,QAAQ;4BACN,KAAK,WAAW,MAAM,EAAE;4BACxB,QAAQ,WAAW,MAAM,EAAE;4BAC3B,QAAQ,WAAW,MAAM,EAAE;4BAC3B,SAAS,WAAW,MAAM,EAAE;wBAC9B;oBACF;oBAEA,wCAAwC;oBACxC,IAAI,WAAW,QAAQ,EAAE,MAAM,SAAS;wBACtC,MAAM,gBAAgB,WAAW,QAAQ,CAAC,IAAI,CAAC,OAAO;wBAEtD,6CAA6C;wBAC7C,IAAI,cAAc,QAAQ,CAAC,2CACvB,cAAc,QAAQ,CAAC,0BACvB,cAAc,QAAQ,CAAC,cAAc;4BACvC,eAAe,CAAC,2BAA2B,EAAE,cAAc,2CAA2C,CAAC;4BAEvG,gEAAgE;4BAChE,QAAQ,KAAK,CAAC,uDAAuD;gCACnE,SAAS;gCACT,QAAQ,WAAW,QAAQ,CAAC,MAAM;gCAClC,WAAW,IAAI,OAAO,WAAW;4BACnC;wBACF,OAAO;4BACL,eAAe,CAAC,cAAc,EAAE,eAAe;wBACjD;oBACF,OAAO,IAAI,WAAW,QAAQ,EAAE,WAAW,KAAK;wBAC9C,eAAe;oBACjB;gBACF;YACF;YAEA,SAAS,eAAe,QAAQ,MAAM,IAAI,MAAM;YAEhD,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAW;QAAS;QAAU;QAAU;QAAkB;KAAM;IAEpE,+EAA+E;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gDAAgD;QAChD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,mBAAmB;YAAE;YAAW;YAAS;YAAU;YAAU;QAAiB;QAC1F,gBAAgB;IAClB,GAAG;QAAC;QAAW;QAAS;QAAU;QAAU;KAAiB;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,CAAC,cAAc;YAC5B,QAAQ,GAAG,CAAC,oCAAoC;gBAAE;gBAAW;gBAAS;gBAAU;gBAAU;YAAiB;YAC3G,gBAAgB;YAChB;QACF;IACF,GAAG;QAAC;QAAS;QAAc;QAAa;QAAW;QAAS;QAAU;QAAU;KAAiB;IAEjG,OAAO;QACL;QACA;QACA;QACA,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 7387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/excel-export.ts"], "sourcesContent": ["import * as XLSX from 'xlsx';\r\nimport { saveAs } from 'file-saver';\r\nimport { formatCurrency } from './utils';\r\n\r\n/**\r\n * Format a date for display in Excel\r\n */\r\nfunction formatExcelDate(date: Date | string): string {\r\n  if (!date) return '';\r\n  const d = typeof date === 'string' ? new Date(date) : date;\r\n  return d.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric'\r\n  });\r\n}\r\n\r\n/**\r\n * Exports data to an Excel file\r\n * @param data The data to export\r\n * @param fileName The name of the file to save (without extension)\r\n * @param sheetName The name of the sheet in the Excel file\r\n * @param excludeColumns Array of column keys to exclude from the export\r\n */\r\nexport function exportToExcel<T extends Record<string, any>>(\r\n  data: T[],\r\n  fileName: string,\r\n  sheetName: string = 'Sheet1',\r\n  excludeColumns: string[] = ['tenant_id']\r\n): void {\r\n  if (!data || data.length === 0) {\r\n    console.warn('No data to export');\r\n    return;\r\n  }\r\n\r\n  try {\r\n    // Filter out excluded columns\r\n    const filteredData = data.map(item => {\r\n      const filteredItem: Record<string, any> = {};\r\n      Object.keys(item).forEach(key => {\r\n        // Check if the key or its lowercase/uppercase variant is in the excluded columns\r\n        const keyLower = key.toLowerCase();\r\n        const isExcluded = excludeColumns.some(\r\n          col => col === key || col.toLowerCase() === keyLower\r\n        );\r\n\r\n        if (!isExcluded) {\r\n          // Handle nested objects\r\n          if (item[key] && typeof item[key] === 'object' && !Array.isArray(item[key]) && item[key] !== null) {\r\n            // For objects like Branch, Role, etc., just use their name property if available\r\n            if (item[key].name) {\r\n              filteredItem[key] = item[key].name;\r\n            } else {\r\n              // Otherwise stringify the object\r\n              filteredItem[key] = JSON.stringify(item[key]);\r\n            }\r\n          } else if (Array.isArray(item[key])) {\r\n            // For arrays, join the values or stringify\r\n            if (item[key].length > 0 && typeof item[key][0] === 'object' && item[key][0].name) {\r\n              // If array of objects with name property, join the names\r\n              filteredItem[key] = item[key].map((i: any) => i.name).join(', ');\r\n            } else {\r\n              // Otherwise stringify the array\r\n              filteredItem[key] = JSON.stringify(item[key]);\r\n            }\r\n          } else if (item[key] instanceof Date) {\r\n            // Format dates in a more readable format: YYYY-MM-DD HH:MM:SS\r\n            const date = new Date(item[key]);\r\n            filteredItem[key] = date.toLocaleString();\r\n          } else if (typeof item[key] === 'string' && /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}/.test(item[key])) {\r\n            // Handle ISO date strings\r\n            const date = new Date(item[key]);\r\n            if (!isNaN(date.getTime())) {\r\n              filteredItem[key] = date.toLocaleString();\r\n            } else {\r\n              filteredItem[key] = item[key];\r\n            }\r\n          } else if (typeof item[key] === 'boolean') {\r\n            // Format booleans as 'Yes' or 'No' for better readability\r\n            filteredItem[key] = item[key] ? 'Yes' : 'No';\r\n          } else {\r\n            // For other primitive values, use as is\r\n            filteredItem[key] = item[key];\r\n          }\r\n        }\r\n      });\r\n      return filteredItem;\r\n    });\r\n\r\n    // Format column headers to be more readable\r\n    const formattedData = filteredData.map(item => {\r\n      const formattedItem: Record<string, any> = {};\r\n\r\n      // Define the order of columns (prioritize important fields)\r\n      const priorityKeys = ['id', 'name', 'email', 'phone', 'status', 'role', 'branch', 'created_at'];\r\n\r\n      // First add priority keys in order\r\n      priorityKeys.forEach(priorityKey => {\r\n        Object.keys(item).forEach(key => {\r\n          if (key.toLowerCase() === priorityKey) {\r\n            // Convert snake_case to Title Case\r\n            const formattedKey = key\r\n              .split('_')\r\n              .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n              .join(' ');\r\n            formattedItem[formattedKey] = item[key];\r\n          }\r\n        });\r\n      });\r\n\r\n      // Then add remaining keys\r\n      Object.keys(item).forEach(key => {\r\n        if (!priorityKeys.includes(key.toLowerCase()) && !formattedItem[key]) {\r\n          // Convert snake_case to Title Case\r\n          const formattedKey = key\r\n            .split('_')\r\n            .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n            .join(' ');\r\n          formattedItem[formattedKey] = item[key];\r\n        }\r\n      });\r\n\r\n      return formattedItem;\r\n    });\r\n\r\n    // Create a worksheet\r\n    const worksheet = XLSX.utils.json_to_sheet(formattedData);\r\n\r\n    // Create a workbook\r\n    const workbook = XLSX.utils.book_new();\r\n    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);\r\n\r\n    // Generate Excel file\r\n    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });\r\n    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });\r\n\r\n    // Save the file\r\n    saveAs(blob, `${fileName}.xlsx`);\r\n  } catch (error) {\r\n    console.error('Error exporting to Excel:', error);\r\n  }\r\n}\r\n\r\n/**\r\n * Export cash status report to Excel\r\n * @param data The cash status report data\r\n * @param dateRange The date range for the report\r\n * @returns The filename of the exported Excel file\r\n */\r\nexport function exportCashStatusToExcel(data: any, dateRange: { startDate: Date; endDate: Date }): string {\r\n  if (!data) {\r\n    console.warn('No data to export');\r\n    return '';\r\n  }\r\n\r\n  try {\r\n    // Create a new workbook\r\n    const workbook = XLSX.utils.book_new();\r\n\r\n    // Format the date range for the filename\r\n    const startDateStr = formatExcelDate(dateRange.startDate);\r\n    const endDateStr = formatExcelDate(dateRange.endDate);\r\n\r\n    // Create a summary sheet\r\n    if (data.summary) {\r\n      const summaryData = [{\r\n        'Report Period': `${startDateStr} to ${endDateStr}`,\r\n        'Opening Cash': formatCurrency(data.summary.totalOpeningCash || 0),\r\n        'Cash Inflows': formatCurrency(data.summary.totalInflows || 0),\r\n        'Cash Outflows': formatCurrency(data.summary.totalOutflows || 0),\r\n        'Net Change': formatCurrency(data.summary.netCashChange || 0),\r\n      }];\r\n\r\n      const summarySheet = XLSX.utils.json_to_sheet(summaryData);\r\n      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');\r\n    }\r\n\r\n    // Create Cash Inflows sheet\r\n    if (data.cashInflows) {\r\n      // Prepare data for Cash Inflows\r\n      const inflowsData = [];\r\n\r\n      // Add section header for Cash Sales\r\n      if (data.cashInflows.cashSales?.data?.length > 0) {\r\n        inflowsData.push({ 'Category': 'CASH SALES', 'Amount': formatCurrency(data.cashInflows.cashSales.total || 0) });\r\n        inflowsData.push({ 'Category': '', 'Amount': '' }); // Empty row for spacing\r\n\r\n        // Add column headers\r\n        inflowsData.push({\r\n          'Category': 'Branch',\r\n          'Region': 'Region',\r\n          'Amount': 'Amount',\r\n          'Transactions': 'Transactions'\r\n        });\r\n\r\n        // Add data rows\r\n        data.cashInflows.cashSales.data.forEach((item: any) => {\r\n          inflowsData.push({\r\n            'Category': item.branch_name || '',\r\n            'Region': item.region_name || '',\r\n            'Amount': formatCurrency(item.amount || 0),\r\n            'Transactions': item.transaction_count || 0\r\n          });\r\n        });\r\n\r\n        // Add spacing\r\n        inflowsData.push({ 'Category': '', 'Amount': '' });\r\n      }\r\n\r\n      // Add section header for MPESA Deposits\r\n      if (data.cashInflows.mpesaDeposits?.data?.length > 0) {\r\n        inflowsData.push({ 'Category': 'MPESA DEPOSITS', 'Amount': formatCurrency(data.cashInflows.mpesaDeposits.total || 0) });\r\n        inflowsData.push({ 'Category': '', 'Amount': '' }); // Empty row for spacing\r\n\r\n        // Add column headers\r\n        inflowsData.push({\r\n          'Category': 'Branch',\r\n          'Region': 'Region',\r\n          'Amount': 'Amount',\r\n          'Transactions': 'Transactions'\r\n        });\r\n\r\n        // Add data rows\r\n        data.cashInflows.mpesaDeposits.data.forEach((item: any) => {\r\n          inflowsData.push({\r\n            'Category': item.branch_name || '',\r\n            'Region': item.region_name || '',\r\n            'Amount': formatCurrency(item.amount || 0),\r\n            'Transactions': item.transaction_count || 0\r\n          });\r\n        });\r\n      }\r\n\r\n      // Create the sheet if we have data\r\n      if (inflowsData.length > 0) {\r\n        const inflowsSheet = XLSX.utils.json_to_sheet(inflowsData);\r\n        XLSX.utils.book_append_sheet(workbook, inflowsSheet, 'Cash Inflows');\r\n      }\r\n    }\r\n\r\n    // Create Cash Outflows sheet\r\n    if (data.cashOutflows) {\r\n      // Prepare data for Cash Outflows\r\n      const outflowsData = [];\r\n\r\n      // Add section header for Expenses\r\n      if (data.cashOutflows.expenses?.data?.length > 0) {\r\n        outflowsData.push({ 'Category': 'EXPENSES', 'Amount': formatCurrency(data.cashOutflows.expenses.total || 0) });\r\n        outflowsData.push({ 'Category': '', 'Amount': '' }); // Empty row for spacing\r\n\r\n        // Add column headers\r\n        outflowsData.push({\r\n          'Category': 'Branch',\r\n          'Region': 'Region',\r\n          'Amount': 'Amount',\r\n          'Transactions': 'Transactions'\r\n        });\r\n\r\n        // Add data rows\r\n        data.cashOutflows.expenses.data.forEach((item: any) => {\r\n          outflowsData.push({\r\n            'Category': item.branch_name || '',\r\n            'Region': item.region_name || '',\r\n            'Amount': formatCurrency(item.amount || 0),\r\n            'Transactions': item.transaction_count || 0\r\n          });\r\n        });\r\n\r\n        // Add spacing\r\n        outflowsData.push({ 'Category': '', 'Amount': '' });\r\n      }\r\n\r\n      // Add section header for Banking Deposits\r\n      if (data.cashOutflows.bankingDeposits?.data?.length > 0) {\r\n        outflowsData.push({ 'Category': 'BANKING DEPOSITS', 'Amount': formatCurrency(data.cashOutflows.bankingDeposits.total || 0) });\r\n        outflowsData.push({ 'Category': '', 'Amount': '' }); // Empty row for spacing\r\n\r\n        // Add column headers\r\n        outflowsData.push({\r\n          'Category': 'Branch',\r\n          'Region': 'Region',\r\n          'Amount': 'Amount',\r\n          'Transactions': 'Transactions'\r\n        });\r\n\r\n        // Add data rows\r\n        data.cashOutflows.bankingDeposits.data.forEach((item: any) => {\r\n          outflowsData.push({\r\n            'Category': item.branch_name || '',\r\n            'Region': item.region_name || '',\r\n            'Amount': formatCurrency(item.amount || 0),\r\n            'Transactions': item.transaction_count || 0\r\n          });\r\n        });\r\n      }\r\n\r\n      // Create the sheet if we have data\r\n      if (outflowsData.length > 0) {\r\n        const outflowsSheet = XLSX.utils.json_to_sheet(outflowsData);\r\n        XLSX.utils.book_append_sheet(workbook, outflowsSheet, 'Cash Outflows');\r\n      }\r\n    }\r\n\r\n    // Create By Region sheet\r\n    if (data.byRegion && data.byRegion.length > 0) {\r\n      const regionData = data.byRegion.map((region: any) => ({\r\n        'Region': region.name || '',\r\n        'Opening Cash': formatCurrency(region.summary.totalOpeningCash || 0),\r\n        'Cash Inflows': formatCurrency(region.summary.totalInflows || 0),\r\n        'Cash Outflows': formatCurrency(region.summary.totalOutflows || 0),\r\n        'Net Change': formatCurrency(region.summary.netCashChange || 0),\r\n      }));\r\n\r\n      const regionSheet = XLSX.utils.json_to_sheet(regionData);\r\n      XLSX.utils.book_append_sheet(workbook, regionSheet, 'By Region');\r\n    }\r\n\r\n    // Create By Branch sheet\r\n    if (data.byBranch && data.byBranch.length > 0) {\r\n      const branchData = data.byBranch.map((branch: any) => ({\r\n        'Branch': branch.name || '',\r\n        'Region': branch.region_name || '',\r\n        'Opening Cash': formatCurrency(branch.summary.totalOpeningCash || 0),\r\n        'Cash Inflows': formatCurrency(branch.summary.totalInflows || 0),\r\n        'Cash Outflows': formatCurrency(branch.summary.totalOutflows || 0),\r\n        'Net Change': formatCurrency(branch.summary.netCashChange || 0),\r\n      }));\r\n\r\n      const branchSheet = XLSX.utils.json_to_sheet(branchData);\r\n      XLSX.utils.book_append_sheet(workbook, branchSheet, 'By Branch');\r\n    }\r\n\r\n    // Generate filename\r\n    const filename = `Cash_Status_Report_${startDateStr.replace(/,/g, '')}_to_${endDateStr.replace(/,/g, '')}.xlsx`;\r\n\r\n    // Generate Excel file\r\n    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });\r\n    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });\r\n\r\n    // Save the file\r\n    saveAs(blob, filename);\r\n\r\n    return filename;\r\n  } catch (error) {\r\n    console.error('Error exporting Cash Status Report to Excel:', error);\r\n    return '';\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA;;CAEC,GACD,SAAS,gBAAgB,IAAmB;IAC1C,IAAI,CAAC,MAAM,OAAO;IAClB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AASO,SAAS,cACd,IAAS,EACT,QAAgB,EAChB,YAAoB,QAAQ,EAC5B,iBAA2B;IAAC;CAAY;IAExC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,QAAQ,IAAI,CAAC;QACb;IACF;IAEA,IAAI;QACF,8BAA8B;QAC9B,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;YAC5B,MAAM,eAAoC,CAAC;YAC3C,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;gBACxB,iFAAiF;gBACjF,MAAM,WAAW,IAAI,WAAW;gBAChC,MAAM,aAAa,eAAe,IAAI,CACpC,CAAA,MAAO,QAAQ,OAAO,IAAI,WAAW,OAAO;gBAG9C,IAAI,CAAC,YAAY;oBACf,wBAAwB;oBACxB,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM;wBACjG,iFAAiF;wBACjF,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;4BAClB,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;wBACpC,OAAO;4BACL,iCAAiC;4BACjC,YAAY,CAAC,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI;wBAC9C;oBACF,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG;wBACnC,2CAA2C;wBAC3C,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE;4BACjF,yDAAyD;4BACzD,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAW,EAAE,IAAI,EAAE,IAAI,CAAC;wBAC7D,OAAO;4BACL,gCAAgC;4BAChC,YAAY,CAAC,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI;wBAC9C;oBACF,OAAO,IAAI,IAAI,CAAC,IAAI,YAAY,MAAM;wBACpC,8DAA8D;wBAC9D,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI;wBAC/B,YAAY,CAAC,IAAI,GAAG,KAAK,cAAc;oBACzC,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,uCAAuC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;wBAClG,0BAA0B;wBAC1B,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI;wBAC/B,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK;4BAC1B,YAAY,CAAC,IAAI,GAAG,KAAK,cAAc;wBACzC,OAAO;4BACL,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;wBAC/B;oBACF,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW;wBACzC,0DAA0D;wBAC1D,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,QAAQ;oBAC1C,OAAO;wBACL,wCAAwC;wBACxC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;oBAC/B;gBACF;YACF;YACA,OAAO;QACT;QAEA,4CAA4C;QAC5C,MAAM,gBAAgB,aAAa,GAAG,CAAC,CAAA;YACrC,MAAM,gBAAqC,CAAC;YAE5C,4DAA4D;YAC5D,MAAM,eAAe;gBAAC;gBAAM;gBAAQ;gBAAS;gBAAS;gBAAU;gBAAQ;gBAAU;aAAa;YAE/F,mCAAmC;YACnC,aAAa,OAAO,CAAC,CAAA;gBACnB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;oBACxB,IAAI,IAAI,WAAW,OAAO,aAAa;wBACrC,mCAAmC;wBACnC,MAAM,eAAe,IAClB,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;wBACR,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI;oBACzC;gBACF;YACF;YAEA,0BAA0B;YAC1B,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;gBACxB,IAAI,CAAC,aAAa,QAAQ,CAAC,IAAI,WAAW,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE;oBACpE,mCAAmC;oBACnC,MAAM,eAAe,IAClB,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;oBACR,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI;gBACzC;YACF;YAEA,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,YAAY,6HAAA,CAAA,QAAU,CAAC,aAAa,CAAC;QAE3C,oBAAoB;QACpB,MAAM,WAAW,6HAAA,CAAA,QAAU,CAAC,QAAQ;QACpC,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,WAAW;QAElD,sBAAsB;QACtB,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,QAAU,AAAD,EAAE,UAAU;YAAE,UAAU;YAAQ,MAAM;QAAQ;QAC3E,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAA2B;QAExE,gBAAgB;QAChB,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG,SAAS,KAAK,CAAC;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;AACF;AAQO,SAAS,wBAAwB,IAAS,EAAE,SAA6C;IAC9F,IAAI,CAAC,MAAM;QACT,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,wBAAwB;QACxB,MAAM,WAAW,6HAAA,CAAA,QAAU,CAAC,QAAQ;QAEpC,yCAAyC;QACzC,MAAM,eAAe,gBAAgB,UAAU,SAAS;QACxD,MAAM,aAAa,gBAAgB,UAAU,OAAO;QAEpD,yBAAyB;QACzB,IAAI,KAAK,OAAO,EAAE;YAChB,MAAM,cAAc;gBAAC;oBACnB,iBAAiB,GAAG,aAAa,IAAI,EAAE,YAAY;oBACnD,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,gBAAgB,IAAI;oBAChE,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,YAAY,IAAI;oBAC5D,iBAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa,IAAI;oBAC9D,cAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa,IAAI;gBAC7D;aAAE;YAEF,MAAM,eAAe,6HAAA,CAAA,QAAU,CAAC,aAAa,CAAC;YAC9C,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,cAAc;QACvD;QAEA,4BAA4B;QAC5B,IAAI,KAAK,WAAW,EAAE;YACpB,gCAAgC;YAChC,MAAM,cAAc,EAAE;YAEtB,oCAAoC;YACpC,IAAI,KAAK,WAAW,CAAC,SAAS,EAAE,MAAM,SAAS,GAAG;gBAChD,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAc,UAAU,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW,CAAC,SAAS,CAAC,KAAK,IAAI;gBAAG;gBAC7G,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG,IAAI,wBAAwB;gBAE5E,qBAAqB;gBACrB,YAAY,IAAI,CAAC;oBACf,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,gBAAgB;gBAClB;gBAEA,gBAAgB;gBAChB,KAAK,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvC,YAAY,IAAI,CAAC;wBACf,YAAY,KAAK,WAAW,IAAI;wBAChC,UAAU,KAAK,WAAW,IAAI;wBAC9B,UAAU,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,IAAI;wBACxC,gBAAgB,KAAK,iBAAiB,IAAI;oBAC5C;gBACF;gBAEA,cAAc;gBACd,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG;YAClD;YAEA,wCAAwC;YACxC,IAAI,KAAK,WAAW,CAAC,aAAa,EAAE,MAAM,SAAS,GAAG;gBACpD,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAkB,UAAU,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW,CAAC,aAAa,CAAC,KAAK,IAAI;gBAAG;gBACrH,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG,IAAI,wBAAwB;gBAE5E,qBAAqB;gBACrB,YAAY,IAAI,CAAC;oBACf,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,gBAAgB;gBAClB;gBAEA,gBAAgB;gBAChB,KAAK,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC3C,YAAY,IAAI,CAAC;wBACf,YAAY,KAAK,WAAW,IAAI;wBAChC,UAAU,KAAK,WAAW,IAAI;wBAC9B,UAAU,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,IAAI;wBACxC,gBAAgB,KAAK,iBAAiB,IAAI;oBAC5C;gBACF;YACF;YAEA,mCAAmC;YACnC,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,eAAe,6HAAA,CAAA,QAAU,CAAC,aAAa,CAAC;gBAC9C,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,cAAc;YACvD;QACF;QAEA,6BAA6B;QAC7B,IAAI,KAAK,YAAY,EAAE;YACrB,iCAAiC;YACjC,MAAM,eAAe,EAAE;YAEvB,kCAAkC;YAClC,IAAI,KAAK,YAAY,CAAC,QAAQ,EAAE,MAAM,SAAS,GAAG;gBAChD,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAY,UAAU,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI;gBAAG;gBAC5G,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG,IAAI,wBAAwB;gBAE7E,qBAAqB;gBACrB,aAAa,IAAI,CAAC;oBAChB,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,gBAAgB;gBAClB;gBAEA,gBAAgB;gBAChB,KAAK,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvC,aAAa,IAAI,CAAC;wBAChB,YAAY,KAAK,WAAW,IAAI;wBAChC,UAAU,KAAK,WAAW,IAAI;wBAC9B,UAAU,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,IAAI;wBACxC,gBAAgB,KAAK,iBAAiB,IAAI;oBAC5C;gBACF;gBAEA,cAAc;gBACd,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG;YACnD;YAEA,0CAA0C;YAC1C,IAAI,KAAK,YAAY,CAAC,eAAe,EAAE,MAAM,SAAS,GAAG;gBACvD,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAoB,UAAU,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY,CAAC,eAAe,CAAC,KAAK,IAAI;gBAAG;gBAC3H,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG,IAAI,wBAAwB;gBAE7E,qBAAqB;gBACrB,aAAa,IAAI,CAAC;oBAChB,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,gBAAgB;gBAClB;gBAEA,gBAAgB;gBAChB,KAAK,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC9C,aAAa,IAAI,CAAC;wBAChB,YAAY,KAAK,WAAW,IAAI;wBAChC,UAAU,KAAK,WAAW,IAAI;wBAC9B,UAAU,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,IAAI;wBACxC,gBAAgB,KAAK,iBAAiB,IAAI;oBAC5C;gBACF;YACF;YAEA,mCAAmC;YACnC,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,MAAM,gBAAgB,6HAAA,CAAA,QAAU,CAAC,aAAa,CAAC;gBAC/C,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,eAAe;YACxD;QACF;QAEA,yBAAyB;QACzB,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7C,MAAM,aAAa,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACrD,UAAU,OAAO,IAAI,IAAI;oBACzB,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,gBAAgB,IAAI;oBAClE,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,YAAY,IAAI;oBAC9D,iBAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI;oBAChE,cAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI;gBAC/D,CAAC;YAED,MAAM,cAAc,6HAAA,CAAA,QAAU,CAAC,aAAa,CAAC;YAC7C,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,aAAa;QACtD;QAEA,yBAAyB;QACzB,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7C,MAAM,aAAa,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACrD,UAAU,OAAO,IAAI,IAAI;oBACzB,UAAU,OAAO,WAAW,IAAI;oBAChC,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,gBAAgB,IAAI;oBAClE,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,YAAY,IAAI;oBAC9D,iBAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI;oBAChE,cAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI;gBAC/D,CAAC;YAED,MAAM,cAAc,6HAAA,CAAA,QAAU,CAAC,aAAa,CAAC;YAC7C,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,aAAa;QACtD;QAEA,oBAAoB;QACpB,MAAM,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,WAAW,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC;QAE/G,sBAAsB;QACtB,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,QAAU,AAAD,EAAE,UAAU;YAAE,UAAU;YAAQ,MAAM;QAAQ;QAC3E,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAA2B;QAExE,gBAAgB;QAChB,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QAEb,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 7734, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/reports/cash-status/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { BreakdownTables } from \"@/features/reports/components/cash-status/breakdown-tables\";\r\nimport { ReportFilters } from \"@/features/reports/components/cash-status/report-filters\";\r\nimport { SummaryCards } from \"@/features/reports/components/cash-status/summary-cards\";\r\nimport { useCashStatusReport } from \"@/features/reports/hooks/useCashStatusReport\";\r\nimport { exportCashStatusToExcel } from \"@/lib/excel-export\";\r\nimport { format } from \"date-fns\";\r\nimport { Download, Printer, RefreshCw } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport default function CashStatusReportPage() {\r\n  // Get date range for the last 30 days by default instead of current month\r\n  const today = new Date();\r\n  const thirtyDaysAgo = new Date(today);\r\n  thirtyDaysAgo.setDate(today.getDate() - 30);\r\n\r\n  // State for filters\r\n  const [startDate, setStartDate] = useState<Date>(thirtyDaysAgo);\r\n  const [endDate, setEndDate] = useState<Date>(today);\r\n  const [regionId, setRegionId] = useState<number | null>(null);\r\n  const [branchId, setBranchId] = useState<number | null>(null);\r\n  const [view, setView] = useState<\"region\" | \"branch\" | \"details\">(\"details\");\r\n\r\n  // Enhanced setters with logging\r\n  const handleRegionChange = (id: number | null) => {\r\n    console.log(`Setting regionId to: ${id}`);\r\n    setRegionId(id);\r\n    // Reset branch when region changes\r\n    if (branchId !== null) {\r\n      console.log(\"Resetting branchId because region changed\");\r\n      setBranchId(null);\r\n    }\r\n  };\r\n\r\n  const handleBranchChange = (id: number | null) => {\r\n    console.log(`Setting branchId to: ${id}`);\r\n    setBranchId(id);\r\n  };\r\n\r\n  // Fetch report data\r\n  const { data, isLoading, error, refetch } = useCashStatusReport({\r\n    startDate: format(startDate, \"yyyy-MM-dd\"),\r\n    endDate: format(endDate, \"yyyy-MM-dd\"),\r\n    regionId,\r\n    branchId,\r\n    includeBreakdown: true,\r\n  });\r\n\r\n  // Log filter state for debugging\r\n  console.log(\"Current filters:\", {\r\n    startDate: format(startDate, \"yyyy-MM-dd\"),\r\n    endDate: format(endDate, \"yyyy-MM-dd\"),\r\n    regionId,\r\n    branchId,\r\n  });\r\n\r\n  // Handle print\r\n  const handlePrint = () => {\r\n    window.print();\r\n  };\r\n\r\n  // Handle export\r\n  const handleExport = () => {\r\n    if (!data) {\r\n      toast.error(\"No data available to export\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Export the data\r\n      const filename = exportCashStatusToExcel(data, { startDate, endDate });\r\n\r\n      // Show success toast\r\n      toast.success(`Exported to ${filename}`);\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n      toast.error(\"Failed to export data. Please try again.\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold tracking-tight\">\r\n              Cash Status Report\r\n            </h1>\r\n            <p className=\"text-muted-foreground\">\r\n              View cash position and movements across all locations\r\n            </p>\r\n          </div>\r\n          <div className=\"flex gap-2\">\r\n            <Button variant=\"outline\" onClick={handlePrint}>\r\n              <Printer className=\"mr-2 h-4 w-4\" />\r\n              Print\r\n            </Button>\r\n            <Button variant=\"outline\" onClick={handleExport}>\r\n              <Download className=\"mr-2 h-4 w-4\" />\r\n              Export\r\n            </Button>\r\n            <Button\r\n              variant=\"default\"\r\n              onClick={() => {\r\n                // Force a hard refresh of the page to bypass cache\r\n                window.location.href =\r\n                  window.location.href.split(\"?\")[0] +\r\n                  \"?t=\" +\r\n                  new Date().getTime();\r\n              }}\r\n            >\r\n              <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n              Force Refresh\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <ReportFilters\r\n          startDate={startDate}\r\n          endDate={endDate}\r\n          regionId={regionId}\r\n          branchId={branchId}\r\n          onStartDateChange={setStartDate}\r\n          onEndDateChange={setEndDate}\r\n          onRegionChange={handleRegionChange}\r\n          onBranchChange={handleBranchChange}\r\n          onRefresh={refetch}\r\n          isLoading={isLoading}\r\n        />\r\n\r\n        {error ? (\r\n          <div className=\"bg-destructive/10 text-destructive p-4 rounded-md\">\r\n            <div className=\"flex items-start\">\r\n              <div className=\"flex-1\">\r\n                <p className=\"font-medium text-lg\">Error loading report</p>\r\n                <p className=\"text-sm mt-1\">{error.message}</p>\r\n\r\n                {/* Show helpful information for database errors */}\r\n                {error.message.includes(\"Database connection\") ||\r\n                  (error.message.includes(\"database connection\") && (\r\n                    <div className=\"mt-4 border-t border-destructive/20 pt-4\">\r\n                      <p className=\"font-medium text-sm\">\r\n                        Database Connection Issue:\r\n                      </p>\r\n                      <p className=\"text-xs mt-2\">\r\n                        The application is unable to connect to the database.\r\n                        This could be due to:\r\n                      </p>\r\n                      <ul className=\"list-disc list-inside mt-1 text-xs\">\r\n                        <li>The database server is not running</li>\r\n                        <li>Database credentials are incorrect</li>\r\n                        <li>Network connectivity issues</li>\r\n                        <li>The backend server needs to be restarted</li>\r\n                      </ul>\r\n                      <p className=\"text-xs mt-2 font-medium\">\r\n                        Recommended actions:\r\n                      </p>\r\n                      <ul className=\"list-disc list-inside mt-1 text-xs\">\r\n                        <li>Contact your system administrator</li>\r\n                        <li>Check if the backend server is running</li>\r\n                        <li>Try restarting the backend server</li>\r\n                        <li>Verify database connection settings</li>\r\n                      </ul>\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n              <div className=\"ml-4\">\r\n                <Button onClick={() => refetch()} className=\"px-4 py-2\">\r\n                  <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n                  Try Again\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <SummaryCards\r\n              summary={data?.summary || null}\r\n              isLoading={isLoading}\r\n            />\r\n\r\n            <Tabs\r\n              value={view}\r\n              onValueChange={(v) => setView(v as any)}\r\n              className=\"mt-6\"\r\n            >\r\n              <TabsList>\r\n                <TabsTrigger value=\"details\">Detailed Breakdown</TabsTrigger>\r\n                <TabsTrigger value=\"region\" disabled={!data?.byRegion?.length}>\r\n                  By Region\r\n                </TabsTrigger>\r\n                <TabsTrigger value=\"branch\" disabled={!data?.byBranch?.length}>\r\n                  By Branch\r\n                </TabsTrigger>\r\n              </TabsList>\r\n\r\n              <TabsContent value=\"details\" className=\"mt-6\">\r\n                <BreakdownTables\r\n                  data={data}\r\n                  view=\"details\"\r\n                  isLoading={isLoading}\r\n                />\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"region\" className=\"mt-6\">\r\n                <BreakdownTables\r\n                  data={data}\r\n                  view=\"region\"\r\n                  isLoading={isLoading}\r\n                />\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"branch\" className=\"mt-6\">\r\n                <BreakdownTables\r\n                  data={data}\r\n                  view=\"branch\"\r\n                  isLoading={isLoading}\r\n                />\r\n              </TabsContent>\r\n            </Tabs>\r\n          </>\r\n        )}\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAee,SAAS;IACtB,0EAA0E;IAC1E,MAAM,QAAQ,IAAI;IAClB,MAAM,gBAAgB,IAAI,KAAK;IAC/B,cAAc,OAAO,CAAC,MAAM,OAAO,KAAK;IAExC,oBAAoB;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAElE,gCAAgC;IAChC,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QACxC,YAAY;QACZ,mCAAmC;QACnC,IAAI,aAAa,MAAM;YACrB,QAAQ,GAAG,CAAC;YACZ,YAAY;QACd;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QACxC,YAAY;IACd;IAEA,oBAAoB;IACpB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,sBAAmB,AAAD,EAAE;QAC9D,WAAW,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;QAC7B,SAAS,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QACzB;QACA;QACA,kBAAkB;IACpB;IAEA,iCAAiC;IACjC,QAAQ,GAAG,CAAC,oBAAoB;QAC9B,WAAW,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;QAC7B,SAAS,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QACzB;QACA;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,OAAO,KAAK;IACd;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,kBAAkB;YAClB,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM;gBAAE;gBAAW;YAAQ;YAEpE,qBAAqB;YACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,UAAU;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC,+IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;;sDACjC,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGtC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;;sDACjC,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,mDAAmD;wCACnD,OAAO,QAAQ,CAAC,IAAI,GAClB,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAClC,QACA,IAAI,OAAO,OAAO;oCACtB;;sDAEA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAM5C,8OAAC,gLAAA,CAAA,gBAAa;oBACZ,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,mBAAmB;oBACnB,iBAAiB;oBACjB,gBAAgB;oBAChB,gBAAgB;oBAChB,WAAW;oBACX,WAAW;;;;;;gBAGZ,sBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB,MAAM,OAAO;;;;;;oCAGzC,MAAM,OAAO,CAAC,QAAQ,CAAC,0BACrB,MAAM,OAAO,CAAC,QAAQ,CAAC,wCACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DAGnC,8OAAC;gDAAE,WAAU;0DAAe;;;;;;0DAI5B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;0DAEN,8OAAC;gDAAE,WAAU;0DAA2B;;;;;;0DAGxC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM;oCAAW,WAAU;;sDAC1C,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;yCAO9C;;sCACE,8OAAC,+KAAA,CAAA,eAAY;4BACX,SAAS,MAAM,WAAW;4BAC1B,WAAW;;;;;;sCAGb,8OAAC,gIAAA,CAAA,OAAI;4BACH,OAAO;4BACP,eAAe,CAAC,IAAM,QAAQ;4BAC9B,WAAU;;8CAEV,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAU;;;;;;sDAC7B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAS,UAAU,CAAC,MAAM,UAAU;sDAAQ;;;;;;sDAG/D,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAS,UAAU,CAAC,MAAM,UAAU;sDAAQ;;;;;;;;;;;;8CAKjE,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,8OAAC,kLAAA,CAAA,kBAAe;wCACd,MAAM;wCACN,MAAK;wCACL,WAAW;;;;;;;;;;;8CAIf,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;8CACpC,cAAA,8OAAC,kLAAA,CAAA,kBAAe;wCACd,MAAM;wCACN,MAAK;wCACL,WAAW;;;;;;;;;;;8CAIf,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;8CACpC,cAAA,8OAAC,kLAAA,CAAA,kBAAe;wCACd,MAAM;wCACN,MAAK;wCACL,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7B", "debugId": null}}]}