(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/features/reports/api/reports-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
/**
 * Reports Service
 * Handles API calls for reports
 */ const reportsService = {
    /**
   * Get sales data for reports
   */ getSalesReport: async (filters)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/sales", {
            params: filters
        });
        return response;
    },
    /**
   * Get sales by item data
   */ getSalesByItemReport: async (filters)=>{
        // This uses the same endpoint as getSalesReport but is separated for clarity
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/sales", {
            params: filters
        });
        return response;
    },
    /**
   * Get sales by category data
   */ getSalesByCategoryReport: async (filters)=>{
        // This uses the same endpoint as getSalesReport but is separated for clarity
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/sales", {
            params: filters
        });
        return response;
    },
    /**
   * Get sales by employee data
   */ getSalesByEmployeeReport: async (filters)=>{
        // This uses the same endpoint as getSalesReport but is separated for clarity
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/sales", {
            params: filters
        });
        return response;
    },
    /**
   * Get sales by payment type data
   */ getSalesByPaymentTypeReport: async (filters)=>{
        // This uses the same endpoint as getSalesReport but is separated for clarity
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/sales", {
            params: filters
        });
        return response;
    },
    /**
   * Get POS sessions (shifts) data
   */ getShiftsReport: async (filters)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/pos-sessions", {
            params: {
                ...filters,
                include_sales_totals: true
            }
        });
        return response;
    },
    /**
   * Get phone repairs data for reports
   */ getPhoneRepairsReport: async (filters)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/phone-repairs", {
            params: filters
        });
        return response;
    },
    /**
   * Get tax report data
   */ getTaxReport: async (filters)=>{
        // This uses the same endpoint as getSalesReport but is separated for clarity
        // We need to include SaleItems to get VAT information
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/sales", {
            params: {
                ...filters,
                include_items: true
            }
        });
        return response;
    },
    /**
   * Get MPESA transactions data for reports
   */ getMpesaTransactionsReport: async (filters)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/mpesa-transactions", {
            params: {
                ...filters,
                limit: 1000
            }
        });
        return response.data;
    }
};
const __TURBOPACK__default__export__ = reportsService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/hooks/use-reports.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDsaSalesReport": (()=>useDsaSalesReport),
    "useMpesaBankingReport": (()=>useMpesaBankingReport),
    "useMpesaTransactionsReport": (()=>useMpesaTransactionsReport),
    "usePhoneRepairsReport": (()=>usePhoneRepairsReport),
    "useSalesByCategoryReport": (()=>useSalesByCategoryReport),
    "useSalesByEmployeeReport": (()=>useSalesByEmployeeReport),
    "useSalesByItemReport": (()=>useSalesByItemReport),
    "useSalesByPaymentTypeReport": (()=>useSalesByPaymentTypeReport),
    "useSalesSummaryReport": (()=>useSalesSummaryReport),
    "useShiftsReport": (()=>useShiftsReport),
    "useTaxReport": (()=>useTaxReport)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/reports.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/parseISO.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$banking$2f$api$2f$banking$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/banking/api/banking-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/reports/api/reports-service.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature(), _s9 = __turbopack_context__.k.signature(), _s10 = __turbopack_context__.k.signature();
;
;
;
;
;
const useSalesSummaryReport = (filters)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "sales-summary",
            filters
        ],
        queryFn: {
            "useSalesSummaryReport.useQuery": async ()=>{
                // Create a copy of filters without employee_id for the API call
                // We now pass region_id to the API since we've updated the backend to support it
                const { employee_id, ...apiFilters } = filters || {};
                // Fetch sales data with region filter (API now supports region_id)
                const salesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSalesReport(apiFilters);
                // Apply employee filter client-side
                let filteredSalesData = salesData;
                // Apply payment method filter client-side if API doesn't handle it correctly
                if (apiFilters.payment_method_id) {
                    filteredSalesData = filteredSalesData.filter({
                        "useSalesSummaryReport.useQuery": (sale)=>sale.payment_method_id === apiFilters.payment_method_id
                    }["useSalesSummaryReport.useQuery"]);
                }
                // Apply employee filter client-side
                if (employee_id) {
                    filteredSalesData = filteredSalesData.filter({
                        "useSalesSummaryReport.useQuery": (sale)=>sale.employee_id === employee_id || sale.sale_employee_id === employee_id
                    }["useSalesSummaryReport.useQuery"]);
                }
                const summaryData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processSalesSummary"])(filteredSalesData);
                // Process data for charts
                const chartData = processChartData(filteredSalesData);
                return {
                    salesData: filteredSalesData,
                    summaryData,
                    chartData
                };
            }
        }["useSalesSummaryReport.useQuery"]
    });
};
_s(useSalesSummaryReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useSalesByItemReport = (filters)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "sales-by-item",
            filters
        ],
        queryFn: {
            "useSalesByItemReport.useQuery": async ()=>{
                // Create a copy of filters without employee_id for the API call
                // We now pass region_id to the API since we've updated the backend to support it
                const { employee_id, ...apiFilters } = filters || {};
                // Fetch sales data with region filter (API now supports region_id)
                const salesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSalesByItemReport(apiFilters);
                // Apply employee filter client-side if needed
                let filteredSalesData = salesData;
                if (employee_id) {
                    filteredSalesData = filteredSalesData.filter({
                        "useSalesByItemReport.useQuery": (sale)=>sale.employee_id === employee_id || sale.sale_employee_id === employee_id
                    }["useSalesByItemReport.useQuery"]);
                }
                const itemsData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processSalesByItem"])(filteredSalesData);
                // Sort by total sales descending
                itemsData.sort({
                    "useSalesByItemReport.useQuery": (a, b)=>b.totalSales - a.totalSales
                }["useSalesByItemReport.useQuery"]);
                // Process data for charts
                const chartData = processItemChartData(itemsData);
                return {
                    salesData: filteredSalesData,
                    itemsData,
                    chartData
                };
            }
        }["useSalesByItemReport.useQuery"]
    });
};
_s1(useSalesByItemReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useSalesByCategoryReport = (filters)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "sales-by-category",
            filters
        ],
        queryFn: {
            "useSalesByCategoryReport.useQuery": async ()=>{
                // Create a copy of filters without employee_id for the API call
                // We now pass region_id to the API since we've updated the backend to support it
                const { employee_id, ...apiFilters } = filters || {};
                // Fetch sales data with region filter (API now supports region_id)
                const salesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSalesByCategoryReport(apiFilters);
                // Process category data
                const categoryMap = new Map();
                let totalSales = 0;
                salesData.forEach({
                    "useSalesByCategoryReport.useQuery": (sale)=>{
                        if (!sale.SaleItems) return;
                        sale.SaleItems.forEach({
                            "useSalesByCategoryReport.useQuery": (item)=>{
                                if (!item.Product) return;
                                // Get category information from the Product object
                                // The backend now includes the ProductCategory model in the Product
                                const categoryId = item.Product.category_id || 0;
                                // Access the ProductCategory directly (no alias)
                                const categoryName = item.Product.ProductCategory?.name || "Uncategorized";
                                const saleAmount = parseFloat(item.total_price);
                                const cost = parseFloat(item.buying_price) * item.quantity;
                                const profit = saleAmount - cost;
                                totalSales += saleAmount;
                                const existingCategory = categoryMap.get(categoryId);
                                if (existingCategory) {
                                    existingCategory.quantity += item.quantity;
                                    existingCategory.totalSales += saleAmount;
                                    existingCategory.profit += profit;
                                } else {
                                    categoryMap.set(categoryId, {
                                        categoryId,
                                        categoryName,
                                        quantity: item.quantity,
                                        totalSales: saleAmount,
                                        profit,
                                        percentage: 0
                                    });
                                }
                            }
                        }["useSalesByCategoryReport.useQuery"]);
                    }
                }["useSalesByCategoryReport.useQuery"]);
                // Calculate percentages
                const categoryData = Array.from(categoryMap.values());
                categoryData.forEach({
                    "useSalesByCategoryReport.useQuery": (category)=>{
                        category.percentage = totalSales > 0 ? category.totalSales / totalSales * 100 : 0;
                    }
                }["useSalesByCategoryReport.useQuery"]);
                // Sort by total sales descending
                categoryData.sort({
                    "useSalesByCategoryReport.useQuery": (a, b)=>b.totalSales - a.totalSales
                }["useSalesByCategoryReport.useQuery"]);
                // Process data for charts
                const chartData = processCategoryChartData(categoryData);
                return {
                    salesData,
                    categoryData,
                    chartData
                };
            }
        }["useSalesByCategoryReport.useQuery"]
    });
};
_s2(useSalesByCategoryReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useSalesByEmployeeReport = (filters)=>{
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "sales-by-employee",
            filters
        ],
        queryFn: {
            "useSalesByEmployeeReport.useQuery": async ()=>{
                // Create a copy of filters without employee_id for the API call
                // We now pass region_id to the API since we've updated the backend to support it
                const { employee_id, ...apiFilters } = filters || {};
                // Fetch sales data with region filter (API now supports region_id)
                const salesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSalesByEmployeeReport(apiFilters);
                // Apply employee filter client-side if needed
                let filteredSalesData = salesData;
                if (employee_id) {
                    filteredSalesData = filteredSalesData.filter({
                        "useSalesByEmployeeReport.useQuery": (sale)=>sale.employee_id === employee_id || sale.sale_employee_id === employee_id || sale.user_id === employee_id
                    }["useSalesByEmployeeReport.useQuery"]);
                }
                // Process employee data
                const employeeMap = new Map();
                filteredSalesData.forEach({
                    "useSalesByEmployeeReport.useQuery": (sale)=>{
                        if (!sale.User) return;
                        const userId = sale.user_id;
                        const userName = sale.User.name || "Unknown";
                        const saleAmount = parseFloat(sale.total_amount);
                        // Calculate profit
                        let cost = 0;
                        if (sale.SaleItems) {
                            cost = sale.SaleItems.reduce({
                                "useSalesByEmployeeReport.useQuery": (sum, item)=>sum + parseFloat(item.buying_price) * item.quantity
                            }["useSalesByEmployeeReport.useQuery"], 0);
                        }
                        const profit = saleAmount - cost;
                        const existingEmployee = employeeMap.get(userId);
                        if (existingEmployee) {
                            existingEmployee.totalSales += saleAmount;
                            existingEmployee.totalTransactions += 1;
                            existingEmployee.profit += profit;
                        } else {
                            employeeMap.set(userId, {
                                userId,
                                userName,
                                totalSales: saleAmount,
                                totalTransactions: 1,
                                averageSale: saleAmount,
                                profit
                            });
                        }
                    }
                }["useSalesByEmployeeReport.useQuery"]);
                // Calculate average sale
                const employeeData = Array.from(employeeMap.values());
                employeeData.forEach({
                    "useSalesByEmployeeReport.useQuery": (employee)=>{
                        employee.averageSale = employee.totalTransactions > 0 ? employee.totalSales / employee.totalTransactions : 0;
                    }
                }["useSalesByEmployeeReport.useQuery"]);
                // Sort by total sales descending
                employeeData.sort({
                    "useSalesByEmployeeReport.useQuery": (a, b)=>b.totalSales - a.totalSales
                }["useSalesByEmployeeReport.useQuery"]);
                // Process data for charts
                const chartData = processEmployeeChartData(employeeData);
                return {
                    salesData: filteredSalesData,
                    employeeData,
                    chartData
                };
            }
        }["useSalesByEmployeeReport.useQuery"]
    });
};
_s3(useSalesByEmployeeReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useSalesByPaymentTypeReport = (filters)=>{
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "sales-by-payment-type",
            filters
        ],
        queryFn: {
            "useSalesByPaymentTypeReport.useQuery": async ()=>{
                const salesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSalesByPaymentTypeReport(filters);
                // Process payment type data
                const paymentMap = new Map();
                let totalSales = 0;
                salesData.forEach({
                    "useSalesByPaymentTypeReport.useQuery": (sale)=>{
                        if (!sale.PaymentMethod) return;
                        const paymentMethodId = sale.payment_method_id;
                        const paymentMethodName = sale.PaymentMethod.name || "Unknown";
                        const saleAmount = parseFloat(sale.total_amount);
                        totalSales += saleAmount;
                        const existingPayment = paymentMap.get(paymentMethodId);
                        if (existingPayment) {
                            existingPayment.totalSales += saleAmount;
                            existingPayment.totalTransactions += 1;
                        } else {
                            paymentMap.set(paymentMethodId, {
                                paymentMethodId,
                                paymentMethodName,
                                totalSales: saleAmount,
                                totalTransactions: 1,
                                averageSale: saleAmount,
                                percentage: 0
                            });
                        }
                    }
                }["useSalesByPaymentTypeReport.useQuery"]);
                // Calculate percentages and average sale
                const paymentData = Array.from(paymentMap.values());
                paymentData.forEach({
                    "useSalesByPaymentTypeReport.useQuery": (payment)=>{
                        payment.percentage = totalSales > 0 ? payment.totalSales / totalSales * 100 : 0;
                        payment.averageSale = payment.totalTransactions > 0 ? payment.totalSales / payment.totalTransactions : 0;
                    }
                }["useSalesByPaymentTypeReport.useQuery"]);
                // Sort by total sales descending
                paymentData.sort({
                    "useSalesByPaymentTypeReport.useQuery": (a, b)=>b.totalSales - a.totalSales
                }["useSalesByPaymentTypeReport.useQuery"]);
                // Process data for charts
                const chartData = processPaymentChartData(paymentData);
                return {
                    salesData,
                    paymentData,
                    chartData
                };
            }
        }["useSalesByPaymentTypeReport.useQuery"]
    });
};
_s4(useSalesByPaymentTypeReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useShiftsReport = (filters)=>{
    _s5();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "shifts",
            filters
        ],
        queryFn: {
            "useShiftsReport.useQuery": async ()=>{
                const shiftsData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getShiftsReport(filters);
                // Process shifts data
                const processedShifts = shiftsData.map({
                    "useShiftsReport.useQuery.processedShifts": (shift)=>{
                        // Use the total sales and transaction count from the backend if available
                        // Otherwise, fall back to calculating from Sales array (for backward compatibility)
                        let totalSales = 0;
                        let totalTransactions = 0;
                        if (shift.total_sales !== undefined && shift.transaction_count !== undefined) {
                            // Use the values provided by the backend
                            totalSales = parseFloat(shift.total_sales);
                            totalTransactions = parseInt(shift.transaction_count);
                            console.log(`Using backend totals for shift ${shift.id}: sales=${totalSales}, transactions=${totalTransactions}`);
                        } else if (shift.Sales) {
                            // Fall back to calculating from Sales array
                            totalSales = shift.Sales.reduce({
                                "useShiftsReport.useQuery.processedShifts": (sum, sale)=>sum + parseFloat(sale.total_amount)
                            }["useShiftsReport.useQuery.processedShifts"], 0);
                            totalTransactions = shift.Sales.length;
                            console.log(`Calculated totals for shift ${shift.id}: sales=${totalSales}, transactions=${totalTransactions}`);
                        }
                        // ✅ Expenses calculation
                        const expenses = shift.Expenses ? shift.Expenses.reduce({
                            "useShiftsReport.useQuery.processedShifts": (sum, expense)=>{
                                return expense.status === "approved" ? sum + parseFloat(expense.amount) : sum;
                            }
                        }["useShiftsReport.useQuery.processedShifts"], 0) : 0;
                        return {
                            id: shift.id,
                            startTime: shift.start_time,
                            endTime: shift.end_time,
                            status: shift.status,
                            openingBalance: shift.opening_balance ? parseFloat(shift.opening_balance) : 0,
                            closingBalance: shift.closing_balance ? parseFloat(shift.closing_balance) : null,
                            totalSales,
                            totalTransactions,
                            cashPaidIn: shift.cash_paid_in ? parseFloat(shift.cash_paid_in) : null,
                            cashPaidOut: shift.cash_paid_out ? parseFloat(shift.cash_paid_out) : null,
                            discrepancies: shift.discrepancies ? parseFloat(shift.discrepancies) : null,
                            userId: shift.user_id,
                            userName: shift.User?.name || "Unknown",
                            branchId: shift.branch_id,
                            branchName: shift.Branch?.name || "Unknown",
                            reconciliation: shift.reconciliation || null,
                            expenses
                        };
                    }
                }["useShiftsReport.useQuery.processedShifts"]);
                // Sort by start time descending
                processedShifts.sort({
                    "useShiftsReport.useQuery": (a, b)=>new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
                }["useShiftsReport.useQuery"]);
                return {
                    shiftsData: processedShifts
                };
            }
        }["useShiftsReport.useQuery"]
    });
};
_s5(useShiftsReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
/**
 * Process sales data into chart data
 */ function processChartData(salesData) {
    // Group sales by date
    const salesByDate = new Map();
    salesData.forEach((sale)=>{
        const date = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseISO"])(sale.created_at), "yyyy-MM-dd");
        const amount = parseFloat(sale.total_amount);
        const existingAmount = salesByDate.get(date) || 0;
        salesByDate.set(date, existingAmount + amount);
    });
    // Sort dates
    const sortedDates = Array.from(salesByDate.keys()).sort();
    // Create chart data
    return {
        labels: sortedDates.map((date)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseISO"])(date), "MMM dd")),
        datasets: [
            {
                label: "Sales",
                data: sortedDates.map((date)=>salesByDate.get(date) || 0),
                backgroundColor: "rgba(59, 130, 246, 0.5)",
                borderColor: "rgb(59, 130, 246)",
                borderWidth: 2
            }
        ]
    };
}
const useMpesaTransactionsReport = (filters)=>{
    _s6();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "mpesa-transactions",
            filters
        ],
        queryFn: {
            "useMpesaTransactionsReport.useQuery": async ()=>{
                const transactionsData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getMpesaTransactionsReport(filters);
                // Process transactions data
                const depositsData = transactionsData.filter({
                    "useMpesaTransactionsReport.useQuery.depositsData": (tx)=>tx.type === 'deposit'
                }["useMpesaTransactionsReport.useQuery.depositsData"]);
                const withdrawalsData = transactionsData.filter({
                    "useMpesaTransactionsReport.useQuery.withdrawalsData": (tx)=>tx.type === 'withdrawal'
                }["useMpesaTransactionsReport.useQuery.withdrawalsData"]);
                // Calculate summary metrics
                const totalDeposits = depositsData.reduce({
                    "useMpesaTransactionsReport.useQuery.totalDeposits": (sum, tx)=>sum + parseFloat(tx.amount)
                }["useMpesaTransactionsReport.useQuery.totalDeposits"], 0);
                const totalWithdrawals = withdrawalsData.reduce({
                    "useMpesaTransactionsReport.useQuery.totalWithdrawals": (sum, tx)=>sum + parseFloat(tx.amount)
                }["useMpesaTransactionsReport.useQuery.totalWithdrawals"], 0);
                const totalTransactions = transactionsData.length;
                const netCashflow = totalDeposits - totalWithdrawals;
                // Process data for charts - group by date
                const depositsByDate = new Map();
                const withdrawalsByDate = new Map();
                transactionsData.forEach({
                    "useMpesaTransactionsReport.useQuery": (tx)=>{
                        const date = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseISO"])(tx.transaction_date), "yyyy-MM-dd");
                        const amount = parseFloat(tx.amount);
                        if (tx.type === 'deposit') {
                            const existingAmount = depositsByDate.get(date) || 0;
                            depositsByDate.set(date, existingAmount + amount);
                        } else if (tx.type === 'withdrawal') {
                            const existingAmount = withdrawalsByDate.get(date) || 0;
                            withdrawalsByDate.set(date, existingAmount + amount);
                        }
                    }
                }["useMpesaTransactionsReport.useQuery"]);
                // Sort dates
                const allDates = new Set([
                    ...Array.from(depositsByDate.keys()),
                    ...Array.from(withdrawalsByDate.keys())
                ]);
                const sortedDates = Array.from(allDates).sort();
                // Create chart data
                const chartData = {
                    labels: sortedDates.map({
                        "useMpesaTransactionsReport.useQuery": (date)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseISO"])(date), "MMM dd")
                    }["useMpesaTransactionsReport.useQuery"]),
                    datasets: [
                        {
                            label: "Deposits",
                            data: sortedDates.map({
                                "useMpesaTransactionsReport.useQuery": (date)=>depositsByDate.get(date) || 0
                            }["useMpesaTransactionsReport.useQuery"]),
                            backgroundColor: "rgba(16, 185, 129, 0.5)",
                            borderColor: "rgb(16, 185, 129)",
                            borderWidth: 2
                        },
                        {
                            label: "Withdrawals",
                            data: sortedDates.map({
                                "useMpesaTransactionsReport.useQuery": (date)=>withdrawalsByDate.get(date) || 0
                            }["useMpesaTransactionsReport.useQuery"]),
                            backgroundColor: "rgba(239, 68, 68, 0.5)",
                            borderColor: "rgb(239, 68, 68)",
                            borderWidth: 2
                        }
                    ]
                };
                // Process data for summary table
                const summaryData = sortedDates.map({
                    "useMpesaTransactionsReport.useQuery.summaryData": (date)=>{
                        const formattedDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseISO"])(date), "MMM dd, yyyy");
                        const deposits = depositsByDate.get(date) || 0;
                        const withdrawals = withdrawalsByDate.get(date) || 0;
                        const net = deposits - withdrawals;
                        return {
                            date: formattedDate,
                            rawDate: date,
                            deposits,
                            withdrawals,
                            net
                        };
                    }
                }["useMpesaTransactionsReport.useQuery.summaryData"]);
                // Sort by date descending (newest first)
                summaryData.sort({
                    "useMpesaTransactionsReport.useQuery": (a, b)=>new Date(b.rawDate).getTime() - new Date(a.rawDate).getTime()
                }["useMpesaTransactionsReport.useQuery"]);
                // Process data for branch summary
                const branchMap = new Map();
                transactionsData.forEach({
                    "useMpesaTransactionsReport.useQuery": (tx)=>{
                        if (!tx.Branch) return;
                        const branchId = tx.branch_id;
                        const branchName = tx.Branch.name;
                        const amount = parseFloat(tx.amount);
                        const existingBranch = branchMap.get(branchId);
                        if (existingBranch) {
                            if (tx.type === 'deposit') {
                                existingBranch.deposits += amount;
                            } else if (tx.type === 'withdrawal') {
                                existingBranch.withdrawals += amount;
                            }
                            existingBranch.transactionCount += 1;
                            existingBranch.net = existingBranch.deposits - existingBranch.withdrawals;
                        } else {
                            branchMap.set(branchId, {
                                branchId,
                                branchName,
                                deposits: tx.type === 'deposit' ? amount : 0,
                                withdrawals: tx.type === 'withdrawal' ? amount : 0,
                                net: tx.type === 'deposit' ? amount : -amount,
                                transactionCount: 1
                            });
                        }
                    }
                }["useMpesaTransactionsReport.useQuery"]);
                const branchData = Array.from(branchMap.values());
                // Sort by total transaction amount descending
                branchData.sort({
                    "useMpesaTransactionsReport.useQuery": (a, b)=>b.deposits + b.withdrawals - (a.deposits + a.withdrawals)
                }["useMpesaTransactionsReport.useQuery"]);
                return {
                    transactionsData,
                    depositsData,
                    withdrawalsData,
                    summaryData,
                    branchData,
                    chartData,
                    metrics: {
                        totalDeposits,
                        totalWithdrawals,
                        totalTransactions,
                        netCashflow
                    }
                };
            }
        }["useMpesaTransactionsReport.useQuery"]
    });
};
_s6(useMpesaTransactionsReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
/**
 * Process items data into chart data
 */ function processItemChartData(itemsData) {
    // Take top 10 items
    const topItems = itemsData.slice(0, 10);
    return {
        labels: topItems.map((item)=>item.productName),
        datasets: [
            {
                label: "Sales",
                data: topItems.map((item)=>item.totalSales),
                backgroundColor: "rgba(16, 185, 129, 0.5)",
                borderColor: "rgb(16, 185, 129)",
                borderWidth: 2
            }
        ]
    };
}
/**
 * Process category data into chart data
 */ function processCategoryChartData(categoryData) {
    // Take top 10 categories
    const topCategories = categoryData.slice(0, 10);
    return {
        labels: topCategories.map((category)=>category.categoryName),
        datasets: [
            {
                label: "Sales",
                data: topCategories.map((category)=>category.totalSales),
                backgroundColor: [
                    "rgba(59, 130, 246, 0.5)",
                    "rgba(16, 185, 129, 0.5)",
                    "rgba(245, 158, 11, 0.5)",
                    "rgba(239, 68, 68, 0.5)",
                    "rgba(139, 92, 246, 0.5)",
                    "rgba(236, 72, 153, 0.5)",
                    "rgba(6, 182, 212, 0.5)",
                    "rgba(168, 85, 247, 0.5)",
                    "rgba(234, 179, 8, 0.5)",
                    "rgba(249, 115, 22, 0.5)"
                ],
                borderColor: [
                    "rgb(59, 130, 246)",
                    "rgb(16, 185, 129)",
                    "rgb(245, 158, 11)",
                    "rgb(239, 68, 68)",
                    "rgb(139, 92, 246)",
                    "rgb(236, 72, 153)",
                    "rgb(6, 182, 212)",
                    "rgb(168, 85, 247)",
                    "rgb(234, 179, 8)",
                    "rgb(249, 115, 22)"
                ],
                borderWidth: 2
            }
        ]
    };
}
const useTaxReport = (filters)=>{
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "tax-report",
            filters
        ],
        queryFn: {
            "useTaxReport.useQuery": async ()=>{
                // Create a copy of filters without employee_id for the API call
                const { ...apiFilters } = filters || {};
                // Fetch sales data with region filter
                const salesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTaxReport(apiFilters);
                // Process tax data
                const vatRateMap = new Map();
                let totalVatAmount = 0;
                let totalTaxableSales = 0;
                let totalNonTaxableSales = 0;
                let totalTransactions = 0;
                // Group sales by date for chart data
                const vatByDate = new Map();
                const taxableSalesByDate = new Map();
                salesData.forEach({
                    "useTaxReport.useQuery": (sale)=>{
                        const saleAmount = parseFloat(sale.total_amount);
                        const vatAmount = parseFloat(sale.total_vat_amount || "0");
                        const taxableAmount = parseFloat(sale.total_excluding_vat || "0");
                        const date = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseISO"])(sale.created_at), "yyyy-MM-dd");
                        // Add to date maps for charts
                        const existingVatAmount = vatByDate.get(date) || 0;
                        vatByDate.set(date, existingVatAmount + vatAmount);
                        const existingTaxableAmount = taxableSalesByDate.get(date) || 0;
                        taxableSalesByDate.set(date, existingTaxableAmount + taxableAmount);
                        // Update totals
                        totalVatAmount += vatAmount;
                        if (vatAmount > 0) {
                            totalTaxableSales += taxableAmount;
                            totalTransactions += 1;
                        } else {
                            totalNonTaxableSales += saleAmount;
                        }
                        // Process sale items to get VAT rates
                        if (sale.SaleItems) {
                            sale.SaleItems.forEach({
                                "useTaxReport.useQuery": (item)=>{
                                    const vatRate = parseFloat(item.vat_rate || "0");
                                    if (vatRate <= 0) return; // Skip non-taxable items
                                    const itemVatAmount = parseFloat(item.vat_amount || "0");
                                    const itemTaxableAmount = parseFloat(item.price_excluding_vat || "0") * item.quantity;
                                    // Get VAT name from product if available
                                    const vatName = item.Product?.VatRate?.name || `${vatRate}% VAT`;
                                    const existingVatData = vatRateMap.get(vatRate);
                                    if (existingVatData) {
                                        existingVatData.vatAmount += itemVatAmount;
                                        existingVatData.taxableSales += itemTaxableAmount;
                                        existingVatData.transactionCount += 1;
                                    } else {
                                        vatRateMap.set(vatRate, {
                                            vatRate,
                                            vatName,
                                            vatAmount: itemVatAmount,
                                            taxableSales: itemTaxableAmount,
                                            transactionCount: 1
                                        });
                                    }
                                }
                            }["useTaxReport.useQuery"]);
                        }
                    }
                }["useTaxReport.useQuery"]);
                // Convert map to array and sort by VAT rate
                const taxData = Array.from(vatRateMap.values());
                taxData.sort({
                    "useTaxReport.useQuery": (a, b)=>b.vatRate - a.vatRate
                }["useTaxReport.useQuery"]);
                // Process chart data
                const sortedDates = Array.from(vatByDate.keys()).sort();
                const vatChartData = {
                    labels: sortedDates.map({
                        "useTaxReport.useQuery": (date)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseISO"])(date), "MMM dd")
                    }["useTaxReport.useQuery"]),
                    datasets: [
                        {
                            label: "VAT Collected",
                            data: sortedDates.map({
                                "useTaxReport.useQuery": (date)=>vatByDate.get(date) || 0
                            }["useTaxReport.useQuery"]),
                            backgroundColor: "rgba(239, 68, 68, 0.5)",
                            borderColor: "rgb(239, 68, 68)",
                            borderWidth: 2
                        }
                    ]
                };
                const taxableChartData = {
                    labels: sortedDates.map({
                        "useTaxReport.useQuery": (date)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseISO"])(date), "MMM dd")
                    }["useTaxReport.useQuery"]),
                    datasets: [
                        {
                            label: "Taxable Sales",
                            data: sortedDates.map({
                                "useTaxReport.useQuery": (date)=>taxableSalesByDate.get(date) || 0
                            }["useTaxReport.useQuery"]),
                            backgroundColor: "rgba(16, 185, 129, 0.5)",
                            borderColor: "rgb(16, 185, 129)",
                            borderWidth: 2
                        }
                    ]
                };
                return {
                    salesData,
                    taxData,
                    summaryData: {
                        totalVatAmount,
                        totalTaxableSales,
                        totalNonTaxableSales,
                        totalTransactions
                    },
                    vatChartData,
                    taxableChartData
                };
            }
        }["useTaxReport.useQuery"]
    });
};
_s7(useTaxReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
/**
 * Process employee data into chart data
 */ function processEmployeeChartData(employeeData) {
    return {
        labels: employeeData.map((employee)=>employee.userName),
        datasets: [
            {
                label: "Sales",
                data: employeeData.map((employee)=>employee.totalSales),
                backgroundColor: "rgba(245, 158, 11, 0.5)",
                borderColor: "rgb(245, 158, 11)",
                borderWidth: 2
            }
        ]
    };
}
/**
 * Process payment type data into chart data
 */ function processPaymentChartData(paymentData) {
    return {
        labels: paymentData.map((payment)=>payment.paymentMethodName),
        datasets: [
            {
                label: "Sales",
                data: paymentData.map((payment)=>payment.totalSales),
                backgroundColor: [
                    "rgba(59, 130, 246, 0.5)",
                    "rgba(16, 185, 129, 0.5)",
                    "rgba(245, 158, 11, 0.5)",
                    "rgba(239, 68, 68, 0.5)",
                    "rgba(139, 92, 246, 0.5)"
                ],
                borderColor: [
                    "rgb(59, 130, 246)",
                    "rgb(16, 185, 129)",
                    "rgb(245, 158, 11)",
                    "rgb(239, 68, 68)",
                    "rgb(139, 92, 246)"
                ],
                borderWidth: 2
            }
        ]
    };
}
const useMpesaBankingReport = (filters)=>{
    _s8();
    // Convert ReportFilterParams to BankingSummaryFilters
    const bankingFilters = {
        branch_id: filters?.branch_id,
        start_date: filters?.start_date || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "yyyy-MM-dd"),
        end_date: filters?.end_date,
        banking_method: filters?.payment_method_id ? String(filters.payment_method_id) : undefined
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "mpesa-banking",
            filters
        ],
        queryFn: {
            "useMpesaBankingReport.useQuery": async ()=>{
                // Use the regular banking endpoint if branch_id is not provided
                let summaryData;
                if (bankingFilters.branch_id && bankingFilters.branch_id > 0) {
                    summaryData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$banking$2f$api$2f$banking$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getBankingSummary(bankingFilters);
                } else {
                    // Convert to BankingFilters
                    const regularFilters = {
                        branch_id: bankingFilters.branch_id,
                        start_date: bankingFilters.start_date,
                        end_date: bankingFilters.end_date,
                        banking_method: bankingFilters.banking_method
                    };
                    summaryData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$banking$2f$api$2f$banking$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getBankingRecordsAsSummary(regularFilters);
                }
                // Process data for charts
                const chartData = processMpesaBankingChartData(summaryData);
                // Calculate totals and metrics
                const totalBank = summaryData.reduce({
                    "useMpesaBankingReport.useQuery.totalBank": (sum, item)=>sum + item.bank
                }["useMpesaBankingReport.useQuery.totalBank"], 0);
                const totalMpesa = summaryData.reduce({
                    "useMpesaBankingReport.useQuery.totalMpesa": (sum, item)=>sum + item.mpesa
                }["useMpesaBankingReport.useQuery.totalMpesa"], 0);
                const totalAgent = summaryData.reduce({
                    "useMpesaBankingReport.useQuery.totalAgent": (sum, item)=>sum + item.agent
                }["useMpesaBankingReport.useQuery.totalAgent"], 0);
                const totalAmount = summaryData.reduce({
                    "useMpesaBankingReport.useQuery.totalAmount": (sum, item)=>sum + item.total
                }["useMpesaBankingReport.useQuery.totalAmount"], 0);
                const totalTransactions = summaryData.reduce({
                    "useMpesaBankingReport.useQuery.totalTransactions": (sum, item)=>sum + item.transaction_count
                }["useMpesaBankingReport.useQuery.totalTransactions"], 0);
                return {
                    summaryData,
                    chartData,
                    metrics: {
                        totalBank,
                        totalMpesa,
                        totalAgent,
                        totalAmount,
                        totalTransactions,
                        averageTransaction: totalTransactions > 0 ? totalAmount / totalTransactions : 0
                    }
                };
            }
        }["useMpesaBankingReport.useQuery"],
        enabled: !!bankingFilters.start_date
    });
};
_s8(useMpesaBankingReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
/**
 * Process chart data for Mpesa banking
 */ function processMpesaBankingChartData(summaryData) {
    return {
        labels: summaryData.map((item)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(item.date), "MMM dd")),
        datasets: [
            {
                label: "Bank",
                data: summaryData.map((item)=>item.bank),
                backgroundColor: "rgba(59, 130, 246, 0.5)",
                borderColor: "rgb(59, 130, 246)",
                borderWidth: 2
            },
            {
                label: "M-Pesa",
                data: summaryData.map((item)=>item.mpesa),
                backgroundColor: "rgba(16, 185, 129, 0.5)",
                borderColor: "rgb(16, 185, 129)",
                borderWidth: 2
            },
            {
                label: "Agent",
                data: summaryData.map((item)=>item.agent),
                backgroundColor: "rgba(245, 158, 11, 0.5)",
                borderColor: "rgb(245, 158, 11)",
                borderWidth: 2
            }
        ]
    };
}
const useDsaSalesReport = (filters)=>{
    _s9();
    // Ensure is_dsa is set to true
    const dsaFilters = {
        ...filters,
        is_dsa: true
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "dsa-sales",
            dsaFilters
        ],
        queryFn: {
            "useDsaSalesReport.useQuery": async ()=>{
                const salesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSalesReport(dsaFilters);
                const summaryData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processSalesSummary"])(salesData);
                // Process data for charts
                const chartData = processChartData(salesData);
                // Process data for payment methods
                const paymentMethodsMap = new Map();
                salesData.forEach({
                    "useDsaSalesReport.useQuery": (sale)=>{
                        if (sale.PaymentMethod) {
                            const methodId = sale.PaymentMethod.id;
                            const amount = parseFloat(sale.total_amount);
                            if (!paymentMethodsMap.has(methodId)) {
                                paymentMethodsMap.set(methodId, {
                                    paymentMethodId: methodId,
                                    paymentMethodName: sale.PaymentMethod.name,
                                    totalSales: 0,
                                    totalTransactions: 0,
                                    averageSale: 0,
                                    percentage: 0
                                });
                            }
                            const method = paymentMethodsMap.get(methodId);
                            method.totalSales += amount;
                            method.totalTransactions += 1;
                        }
                    }
                }["useDsaSalesReport.useQuery"]);
                const paymentData = Array.from(paymentMethodsMap.values());
                // Calculate total sales for percentage calculation
                const totalSalesAmount = paymentData.reduce({
                    "useDsaSalesReport.useQuery.totalSalesAmount": (sum, method)=>sum + method.totalSales
                }["useDsaSalesReport.useQuery.totalSalesAmount"], 0);
                // Calculate average sale and percentage for each payment method
                paymentData.forEach({
                    "useDsaSalesReport.useQuery": (method)=>{
                        method.averageSale = method.totalTransactions > 0 ? method.totalSales / method.totalTransactions : 0;
                        method.percentage = totalSalesAmount > 0 ? method.totalSales / totalSalesAmount * 100 : 0;
                    }
                }["useDsaSalesReport.useQuery"]);
                paymentData.sort({
                    "useDsaSalesReport.useQuery": (a, b)=>b.totalSales - a.totalSales
                }["useDsaSalesReport.useQuery"]);
                // Process data for agents
                const agentsMap = new Map();
                salesData.forEach({
                    "useDsaSalesReport.useQuery": (sale)=>{
                        if (sale.User) {
                            const userId = sale.User.id;
                            const amount = parseFloat(sale.total_amount);
                            if (!agentsMap.has(userId)) {
                                agentsMap.set(userId, {
                                    userId,
                                    userName: sale.User.name,
                                    totalSales: 0,
                                    totalTransactions: 0,
                                    averageSale: 0,
                                    profit: 0
                                });
                            }
                            const agent = agentsMap.get(userId);
                            agent.totalSales += amount;
                            agent.totalTransactions += 1;
                        }
                    }
                }["useDsaSalesReport.useQuery"]);
                const agentData = Array.from(agentsMap.values());
                // Calculate average sale for each agent
                agentData.forEach({
                    "useDsaSalesReport.useQuery": (agent)=>{
                        agent.averageSale = agent.totalTransactions > 0 ? agent.totalSales / agent.totalTransactions : 0;
                        // For simplicity, we're setting profit to 30% of sales as we don't have item-level data here
                        agent.profit = agent.totalSales * 0.3;
                    }
                }["useDsaSalesReport.useQuery"]);
                agentData.sort({
                    "useDsaSalesReport.useQuery": (a, b)=>b.totalSales - a.totalSales
                }["useDsaSalesReport.useQuery"]);
                // Process payment method chart data
                const paymentChartData = processPaymentChartData(paymentData);
                return {
                    salesData,
                    summaryData,
                    chartData,
                    paymentData,
                    agentData,
                    paymentChartData
                };
            }
        }["useDsaSalesReport.useQuery"]
    });
};
_s9(useDsaSalesReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const usePhoneRepairsReport = (filters)=>{
    _s10();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports",
            "phone-repairs",
            filters
        ],
        queryFn: {
            "usePhoneRepairsReport.useQuery": async ()=>{
                const repairsData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getPhoneRepairsReport(filters);
                // Process data for charts
                const chartData = processPhoneRepairsChartData(repairsData);
                // Process data for status breakdown
                const statusData = processPhoneRepairsStatusData(repairsData);
                return {
                    repairsData,
                    chartData,
                    statusData
                };
            }
        }["usePhoneRepairsReport.useQuery"]
    });
};
_s10(usePhoneRepairsReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
/**
 * Process phone repairs data for charts
 */ function processPhoneRepairsChartData(repairs) {
    // Count repairs by status
    const statusCounts = {};
    repairs.forEach((repair)=>{
        const status = repair.status;
        statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
    // Count repairs by branch
    const branchCounts = {};
    repairs.forEach((repair)=>{
        const branchName = repair.Branch?.name || "Unknown";
        branchCounts[branchName] = (branchCounts[branchName] || 0) + 1;
    });
    // Create chart data
    return {
        labels: Object.keys(statusCounts),
        datasets: [
            {
                label: "Repairs by Status",
                data: Object.values(statusCounts),
                backgroundColor: [
                    "rgba(59, 130, 246, 0.5)",
                    "rgba(16, 185, 129, 0.5)",
                    "rgba(245, 158, 11, 0.5)",
                    "rgba(239, 68, 68, 0.5)",
                    "rgba(139, 92, 246, 0.5)"
                ],
                borderColor: [
                    "rgb(59, 130, 246)",
                    "rgb(16, 185, 129)",
                    "rgb(245, 158, 11)",
                    "rgb(239, 68, 68)",
                    "rgb(139, 92, 246)"
                ],
                borderWidth: 2
            }
        ],
        secondaryData: {
            labels: Object.keys(branchCounts),
            datasets: [
                {
                    label: "Repairs by Branch",
                    data: Object.values(branchCounts),
                    backgroundColor: [
                        "rgba(59, 130, 246, 0.5)",
                        "rgba(16, 185, 129, 0.5)",
                        "rgba(245, 158, 11, 0.5)",
                        "rgba(239, 68, 68, 0.5)",
                        "rgba(139, 92, 246, 0.5)"
                    ],
                    borderColor: [
                        "rgb(59, 130, 246)",
                        "rgb(16, 185, 129)",
                        "rgb(245, 158, 11)",
                        "rgb(239, 68, 68)",
                        "rgb(139, 92, 246)"
                    ],
                    borderWidth: 2
                }
            ]
        }
    };
}
/**
 * Process phone repairs data for status breakdown
 */ function processPhoneRepairsStatusData(repairs) {
    // Group repairs by status
    const statusGroups = {};
    repairs.forEach((repair)=>{
        const status = repair.status;
        if (!statusGroups[status]) {
            statusGroups[status] = [];
        }
        statusGroups[status].push(repair);
    });
    // Calculate totals and averages
    const statusData = Object.entries(statusGroups).map(([status, repairs])=>{
        const count = repairs.length;
        // Calculate average estimated cost
        const totalEstimatedCost = repairs.reduce((sum, repair)=>sum + parseFloat(repair.estimated_cost || "0"), 0);
        const avgEstimatedCost = count > 0 ? totalEstimatedCost / count : 0;
        // Calculate average actual cost for completed repairs
        const completedRepairs = repairs.filter((r)=>r.amount_charged);
        const totalActualCost = completedRepairs.reduce((sum, repair)=>sum + parseFloat(repair.amount_charged || "0"), 0);
        const avgActualCost = completedRepairs.length > 0 ? totalActualCost / completedRepairs.length : 0;
        return {
            status,
            count,
            totalEstimatedCost,
            avgEstimatedCost,
            totalActualCost,
            avgActualCost,
            completedCount: completedRepairs.length
        };
    });
    return statusData;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/api/reports-api-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Reports API Service
 * Handles API calls for the new reports endpoints from REPORTS_ENDPOINT_GUIDE.md
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
/**
 * Reports API Service
 * Uses the new endpoints specified in REPORTS_ENDPOINT_GUIDE.md
 */ const reportsApiService = {
    /**
   * Get stock history for a specific product
   * Endpoint: GET /api/v1/reports/stock-history
   * Permission: stock_reports:read
   */ getStockHistory: async (params)=>{
        try {
            const config = {
                params
            };
            // If format is excel, expect blob response
            if (params.format === 'excel') {
                config.responseType = 'blob';
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/stock-history", config);
                return response;
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/stock-history", config);
            return response;
        } catch (error) {
            console.error('Error fetching stock history:', error);
            throw error;
        }
    },
    /**
   * Get sales summary report with enhanced filtering
   * Endpoint: GET /api/v1/reports/sales-summary
   * Permission: sales_reports:read
   */ getSalesSummary: async (params)=>{
        try {
            const config = {
                params
            };
            // If format is excel, expect blob response
            if (params.format === 'excel') {
                config.responseType = 'blob';
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/sales-summary", config);
                return response;
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/sales-summary", config);
            return response;
        } catch (error) {
            console.error('Error fetching sales summary:', error);
            throw error;
        }
    },
    /**
   * Get banking transactions report with pagination
   * Endpoint: GET /api/v1/reports/banking-transactions
   * Permission: banking_reports:read
   */ getBankingTransactions: async (params)=>{
        try {
            const config = {
                params
            };
            // If format is excel, expect blob response
            if (params?.format === 'excel') {
                config.responseType = 'blob';
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/banking-transactions", config);
                return response;
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/banking-transactions", config);
            return response;
        } catch (error) {
            console.error('Error fetching banking transactions:', error);
            throw error;
        }
    },
    /**
   * Export expenses in Excel format
   * Endpoint: GET /api/v1/expense-analytics/export
   * Permission: expense_reports:read
   */ exportExpenses: async (params)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/expense-analytics/export", {
                params,
                responseType: 'blob'
            });
            return response;
        } catch (error) {
            console.error('Error exporting expenses:', error);
            throw error;
        }
    },
    /**
   * Get stock history summary for dashboard
   * Simplified version for dashboard widgets
   */ getStockHistorySummary: async (params)=>{
        try {
            // This would be a simplified endpoint for dashboard use
            // For now, we'll use the main endpoint and process the data
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/reports/stock-summary", {
                params
            });
            return response;
        } catch (error) {
            console.error('Error fetching stock summary:', error);
            throw error;
        }
    },
    /**
   * Get banking transactions summary for dashboard
   * Simplified version for dashboard widgets
   */ getBankingTransactionsSummary: async (params)=>{
        try {
            // Get summary data from the main endpoint
            const response = await reportsApiService.getBankingTransactions({
                ...params,
                limit: 1
            });
            return {
                total_transactions: response.summary.total_transactions,
                total_amount: response.summary.total_amount,
                pending_count: response.transactions.filter((t)=>t.status === 'pending').length,
                completed_count: response.transactions.filter((t)=>t.status === 'completed').length
            };
        } catch (error) {
            console.error('Error fetching banking summary:', error);
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = reportsApiService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/hooks/use-reports-api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * React hooks for the new Reports API endpoints
 * Based on REPORTS_ENDPOINT_GUIDE.md specification
 */ __turbopack_context__.s({
    "useBankingTransactionsExport": (()=>useBankingTransactionsExport),
    "useBankingTransactionsReport": (()=>useBankingTransactionsReport),
    "useBankingTransactionsSummary": (()=>useBankingTransactionsSummary),
    "useExpenseExport": (()=>useExpenseExport),
    "useInvalidateReports": (()=>useInvalidateReports),
    "useSalesSummaryApi": (()=>useSalesSummaryApi),
    "useSalesSummaryExport": (()=>useSalesSummaryExport),
    "useStockHistory": (()=>useStockHistory),
    "useStockHistoryExport": (()=>useStockHistoryExport),
    "useStockHistorySummary": (()=>useStockHistorySummary)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/reports/api/reports-api-service.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature(), _s9 = __turbopack_context__.k.signature();
;
;
const useStockHistory = (params)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports-api",
            "stock-history",
            params
        ],
        queryFn: {
            "useStockHistory.useQuery": async ()=>{
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getStockHistory(params);
                return response;
            }
        }["useStockHistory.useQuery"],
        enabled: !!params.product_id && params.format !== 'excel',
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000
    });
};
_s(useStockHistory, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useSalesSummaryApi = (params)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports-api",
            "sales-summary",
            params
        ],
        queryFn: {
            "useSalesSummaryApi.useQuery": async ()=>{
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSalesSummary(params);
                return response;
            }
        }["useSalesSummaryApi.useQuery"],
        enabled: !!(params.start_date && params.end_date) && params.format !== 'excel',
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000
    });
};
_s1(useSalesSummaryApi, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useBankingTransactionsReport = (params)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports-api",
            "banking-transactions",
            params
        ],
        queryFn: {
            "useBankingTransactionsReport.useQuery": async ()=>{
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getBankingTransactions(params);
                return response;
            }
        }["useBankingTransactionsReport.useQuery"],
        enabled: params?.format !== 'excel',
        staleTime: 2 * 60 * 1000,
        gcTime: 5 * 60 * 1000
    });
};
_s2(useBankingTransactionsReport, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useStockHistorySummary = (params = {})=>{
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports-api",
            "stock-summary",
            params
        ],
        queryFn: {
            "useStockHistorySummary.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getStockHistorySummary(params)
        }["useStockHistorySummary.useQuery"],
        staleTime: 10 * 60 * 1000,
        gcTime: 30 * 60 * 1000
    });
};
_s3(useStockHistorySummary, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useBankingTransactionsSummary = (params = {})=>{
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "reports-api",
            "banking-summary",
            params
        ],
        queryFn: {
            "useBankingTransactionsSummary.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getBankingTransactionsSummary(params)
        }["useBankingTransactionsSummary.useQuery"],
        staleTime: 5 * 60 * 1000,
        gcTime: 15 * 60 * 1000
    });
};
_s4(useBankingTransactionsSummary, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useStockHistoryExport = ()=>{
    _s5();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useStockHistoryExport.useMutation": async (params)=>{
                const exportParams = {
                    ...params,
                    format: 'excel'
                };
                const blob = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getStockHistory(exportParams);
                return {
                    blob,
                    params
                };
            }
        }["useStockHistoryExport.useMutation"]
    });
};
_s5(useStockHistoryExport, "wwwtpB20p0aLiHIvSy5P98MwIUg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useSalesSummaryExport = ()=>{
    _s6();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useSalesSummaryExport.useMutation": async (params)=>{
                const exportParams = {
                    ...params,
                    format: 'excel'
                };
                const blob = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSalesSummary(exportParams);
                return {
                    blob,
                    params
                };
            }
        }["useSalesSummaryExport.useMutation"]
    });
};
_s6(useSalesSummaryExport, "wwwtpB20p0aLiHIvSy5P98MwIUg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useBankingTransactionsExport = ()=>{
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useBankingTransactionsExport.useMutation": async (params)=>{
                const exportParams = {
                    ...params,
                    format: 'excel'
                };
                const blob = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getBankingTransactions(exportParams);
                return {
                    blob,
                    params
                };
            }
        }["useBankingTransactionsExport.useMutation"]
    });
};
_s7(useBankingTransactionsExport, "wwwtpB20p0aLiHIvSy5P98MwIUg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useExpenseExport = ()=>{
    _s8();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useExpenseExport.useMutation": async (params)=>{
                const blob = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$api$2f$reports$2d$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].exportExpenses(params);
                return {
                    blob,
                    params
                };
            }
        }["useExpenseExport.useMutation"]
    });
};
_s8(useExpenseExport, "wwwtpB20p0aLiHIvSy5P98MwIUg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useInvalidateReports = ()=>{
    _s9();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return {
        invalidateAll: ()=>{
            queryClient.invalidateQueries({
                queryKey: [
                    "reports-api"
                ]
            });
        },
        invalidateStockHistory: ()=>{
            queryClient.invalidateQueries({
                queryKey: [
                    "reports-api",
                    "stock-history"
                ]
            });
        },
        invalidateSalesSummary: ()=>{
            queryClient.invalidateQueries({
                queryKey: [
                    "reports-api",
                    "sales-summary"
                ]
            });
        },
        invalidateBankingTransactions: ()=>{
            queryClient.invalidateQueries({
                queryKey: [
                    "reports-api",
                    "banking-transactions"
                ]
            });
        }
    };
};
_s9(useInvalidateReports, "4R+oYVB2Uc11P7bp1KcuhpkfaTw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/components/product-selector.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductSelector": (()=>ProductSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevrons$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronsUpDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevrons-up-down.js [app-client] (ecmascript) <export default as ChevronsUpDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/command.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/popover.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$hooks$2f$use$2d$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/products/hooks/use-products.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
function ProductSelector({ value, onValueChange, placeholder = "Select product...", disabled = false, includeAllOption = true }) {
    _s();
    const [open, setOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const { data: productsResponse, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$hooks$2f$use$2d$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProducts"])({
        search: searchTerm,
        page: 1,
        limit: 50
    });
    const products = productsResponse?.data || [];
    const selectedProduct = products.find((p)=>p.id === value);
    const handleSelect = (productId)=>{
        onValueChange(productId);
        setOpen(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Popover"], {
        open: open,
        onOpenChange: setOpen,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverTrigger"], {
                asChild: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    variant: "outline",
                    role: "combobox",
                    "aria-expanded": open,
                    className: "w-full justify-between",
                    disabled: disabled,
                    children: [
                        selectedProduct ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "truncate",
                            children: selectedProduct.name
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 65,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-muted-foreground",
                            children: placeholder
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 69,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevrons$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronsUpDown$3e$__["ChevronsUpDown"], {
                            className: "ml-2 h-4 w-4 shrink-0 opacity-50"
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 71,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                    lineNumber: 57,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/product-selector.tsx",
                lineNumber: 56,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverContent"], {
                className: "w-[300px] p-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandInput"], {
                            placeholder: "Search products...",
                            value: searchTerm,
                            onValueChange: setSearchTerm
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 76,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandList"], {
                            children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-center p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                        className: "h-4 w-4 animate-spin mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                        lineNumber: 84,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Loading products..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                        lineNumber: 85,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                lineNumber: 83,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandEmpty"], {
                                        children: "No products found."
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                        lineNumber: 89,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandGroup"], {
                                        children: [
                                            includeAllOption && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandItem"], {
                                                value: "all-products",
                                                onSelect: ()=>handleSelect(undefined),
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mr-2 h-4 w-4", !value ? "opacity-100" : "opacity-0")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                        lineNumber: 96,
                                                        columnNumber: 23
                                                    }, this),
                                                    "All Products"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                lineNumber: 92,
                                                columnNumber: 21
                                            }, this),
                                            products.map((product)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$command$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CommandItem"], {
                                                    value: product.id.toString(),
                                                    onSelect: ()=>handleSelect(product.id),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mr-2 h-4 w-4", value === product.id ? "opacity-100" : "opacity-0")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                            lineNumber: 111,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex flex-col",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "truncate",
                                                                    children: product.name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                                    lineNumber: 118,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-xs text-muted-foreground truncate",
                                                                    children: [
                                                                        "SKU: ",
                                                                        product.sku
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                                    lineNumber: 119,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                            lineNumber: 117,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, product.id, true, {
                                                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                                    lineNumber: 106,
                                                    columnNumber: 21
                                                }, this))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/reports/components/product-selector.tsx",
                                        lineNumber: 90,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/product-selector.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/reports/components/product-selector.tsx",
                    lineNumber: 75,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/product-selector.tsx",
                lineNumber: 74,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/reports/components/product-selector.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
}
_s(ProductSelector, "QgL6wRr2++6hUWqS9ROcU8k0mIs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$products$2f$hooks$2f$use$2d$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProducts"]
    ];
});
_c = ProductSelector;
var _c;
__turbopack_context__.k.register(_c, "ProductSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/components/report-filters.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReportFilters": (()=>ReportFilters)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$date$2d$range$2d$picker$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/date-range-picker.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/popover.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/select.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$hooks$2f$use$2d$branches$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/branches/hooks/use-branches.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$components$2f$employee$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/employees/components/employee-selector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$components$2f$payment$2d$method$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/payment-methods/components/payment-method-selector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$hooks$2f$use$2d$payment$2d$methods$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/payment-methods/hooks/use-payment-methods.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$hooks$2f$use$2d$pos$2d$sessions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/pos/hooks/use-pos-sessions.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$regions$2f$hooks$2f$use$2d$regions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/regions/hooks/use-regions.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$components$2f$product$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/reports/components/product-selector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/reports.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/endOfDay.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/startOfDay.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/subDays.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FilterIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-client] (ecmascript) <export default as FilterIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function ReportFilters({ filters, onFilterChange, showTimeFilter = true, showSessionFilter = false, showUserFilter = true, showBranchFilter = true, showRegionFilter = false, showPaymentMethodFilter = true, showStatusFilter = false, showDsaFilter = false, showProductFilter = false, showCategoryFilter = false, showLocationFilter = false, showBankingMethodFilter = false, showTransactionTypeFilter = false }) {
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [dateRange, setDateRange] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        from: filters.start_date ? new Date(filters.start_date) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 7),
        to: filters.end_date ? new Date(filters.end_date) : new Date()
    });
    const [selectedDateRange, setSelectedDateRange] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("this_week");
    // Time filter removed
    const { data: branchesResponse } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$hooks$2f$use$2d$branches$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBranches"])();
    const { data: paymentMethodsResponse } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$hooks$2f$use$2d$payment$2d$methods$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaymentMethods"])();
    const { data: posSessionsResponse } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$hooks$2f$use$2d$pos$2d$sessions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePosSessions"])();
    const { data: regionsResponse } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$regions$2f$hooks$2f$use$2d$regions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRegions"])();
    // Filter branches by selected region
    const filteredBranches = branchesResponse?.data?.filter((branch)=>{
        if (!filters.region_id) return true; // Show all branches if no region selected
        return branch.region_id === filters.region_id;
    }) || [];
    const handleDateRangeChange = (range)=>{
        setDateRange(range);
        if (range?.from && range?.to) {
            onFilterChange({
                ...filters,
                start_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(range.from, "yyyy-MM-dd"),
                end_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(range.to, "yyyy-MM-dd")
            });
        }
    };
    const handlePredefinedDateRange = (value)=>{
        setSelectedDateRange(value);
        let from;
        let to;
        const today = new Date();
        switch(value){
            case "today":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(today);
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(today);
                break;
            case "yesterday":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, 1));
                break;
            case "this_week":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, today.getDay()));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(today);
                break;
            case "last_week":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, today.getDay() + 7));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, today.getDay() + 1));
                break;
            case "this_month":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(new Date(today.getFullYear(), today.getMonth(), 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(today);
                break;
            case "last_month":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(new Date(today.getFullYear(), today.getMonth() - 1, 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(new Date(today.getFullYear(), today.getMonth(), 0));
                break;
            case "this_year":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(new Date(today.getFullYear(), 0, 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(today);
                break;
            case "last_year":
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfDay"])(new Date(today.getFullYear() - 1, 0, 1));
                to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfDay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfDay"])(new Date(today.getFullYear() - 1, 11, 31));
                break;
            case "custom":
                // Keep the current date range
                return;
            default:
                from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, 7);
                to = today;
        }
        setDateRange({
            from,
            to
        });
        if (from && to) {
            onFilterChange({
                ...filters,
                start_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(from, "yyyy-MM-dd"),
                end_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(to, "yyyy-MM-dd")
            });
        }
    };
    // Time filter removed
    const handleBranchChange = (value)=>{
        onFilterChange({
            ...filters,
            branch_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleRegionChange = (value)=>{
        const regionId = value === "all" ? undefined : parseInt(value, 10);
        onFilterChange({
            ...filters,
            region_id: regionId,
            // Reset branch filter when region changes
            branch_id: undefined
        });
    };
    const handleEmployeeChange = (value)=>{
        onFilterChange({
            ...filters,
            employee_id: value
        });
    };
    const handlePaymentMethodChange = (value)=>{
        onFilterChange({
            ...filters,
            payment_method_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleSessionChange = (value)=>{
        onFilterChange({
            ...filters,
            pos_session_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleStatusChange = (value)=>{
        onFilterChange({
            ...filters,
            status: value === "all" ? undefined : value
        });
    };
    const handleDsaChange = (value)=>{
        onFilterChange({
            ...filters,
            is_dsa: value === "all" ? undefined : value === "true"
        });
    };
    const handleProductChange = (value)=>{
        onFilterChange({
            ...filters,
            product_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleCategoryChange = (value)=>{
        onFilterChange({
            ...filters,
            category_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleLocationChange = (value)=>{
        onFilterChange({
            ...filters,
            location_id: value === "all" ? undefined : parseInt(value, 10)
        });
    };
    const handleBankingMethodChange = (value)=>{
        onFilterChange({
            ...filters,
            banking_method: value === "all" ? undefined : value
        });
    };
    const handleTransactionTypeChange = (value)=>{
        onFilterChange({
            ...filters,
            transaction_type: value === "all" ? undefined : value
        });
    };
    const handleReset = ()=>{
        setSelectedDateRange("this_week");
        const today = new Date();
        const from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(today, 7);
        const to = today;
        setDateRange({
            from,
            to
        });
        // Reset all filters to default values
        onFilterChange({
            start_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(from, "yyyy-MM-dd"),
            end_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(to, "yyyy-MM-dd"),
            branch_id: undefined,
            region_id: undefined,
            payment_method_id: undefined,
            employee_id: undefined,
            pos_session_id: undefined,
            status: undefined,
            is_dsa: undefined
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        className: "mb-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
            className: "p-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col space-y-4 md:flex-row md:flex-wrap md:items-end md:gap-4 md:space-y-0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Date Range"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 282,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                        value: selectedDateRange,
                                        onValueChange: handlePredefinedDateRange,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                className: "w-[180px]",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                    placeholder: "Select date range"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 289,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                lineNumber: 288,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DATE_RANGE_OPTIONS"].map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                        value: option.value,
                                                        children: option.label
                                                    }, option.value, false, {
                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                        lineNumber: 293,
                                                        columnNumber: 21
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                lineNumber: 291,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 284,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$date$2d$range$2d$picker$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DateRangePicker"], {
                                        value: dateRange,
                                        onChange: handleDateRangeChange
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 300,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 283,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 281,
                        columnNumber: 11
                    }, this),
                    showBranchFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Branch"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 310,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                value: filters.branch_id ? filters.branch_id.toString() : "all",
                                onValueChange: handleBranchChange,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                        className: "w-[180px]",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                            placeholder: "All Branches"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                            lineNumber: 316,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 315,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                value: "all",
                                                children: "All Branches"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                lineNumber: 319,
                                                columnNumber: 19
                                            }, this),
                                            filteredBranches.map((branch)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                    value: branch.id.toString(),
                                                    children: branch.name
                                                }, branch.id, false, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 321,
                                                    columnNumber: 21
                                                }, this))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 318,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 311,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 309,
                        columnNumber: 13
                    }, this),
                    showPaymentMethodFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Payment Method"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 333,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-[220px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$components$2f$payment$2d$method$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaymentMethodSelector"], {
                                    value: filters.payment_method_id,
                                    onValueChange: (value)=>handlePaymentMethodChange(value !== undefined ? value.toString() : "all"),
                                    placeholder: "All Payment Methods",
                                    includeAllOption: true
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                    lineNumber: 335,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 334,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 332,
                        columnNumber: 13
                    }, this),
                    showProductFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Product"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 352,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-[220px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$reports$2f$components$2f$product$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProductSelector"], {
                                    value: filters.product_id,
                                    onValueChange: (value)=>handleProductChange(value?.toString() || "all"),
                                    placeholder: "All Products",
                                    includeAllOption: true
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                    lineNumber: 354,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 353,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 351,
                        columnNumber: 13
                    }, this),
                    showRegionFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Region"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 369,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                value: filters.region_id ? filters.region_id.toString() : "all",
                                onValueChange: handleRegionChange,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                        className: "w-[180px]",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                            placeholder: "All Regions"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                            lineNumber: 375,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 374,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                value: "all",
                                                children: "All Regions"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                lineNumber: 378,
                                                columnNumber: 19
                                            }, this),
                                            regionsResponse?.data?.map((region)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                    value: region.id.toString(),
                                                    children: region.name
                                                }, region.id, false, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 380,
                                                    columnNumber: 21
                                                }, this))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 377,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 370,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 368,
                        columnNumber: 13
                    }, this),
                    showUserFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Employee"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 392,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-[220px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$employees$2f$components$2f$employee$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EmployeeSelector"], {
                                    value: filters.employee_id,
                                    onValueChange: handleEmployeeChange,
                                    placeholder: "All Employees",
                                    includeAllOption: true,
                                    branchId: filters.branch_id
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                    lineNumber: 394,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 393,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 391,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Popover"], {
                                open: isOpen,
                                onOpenChange: setIsOpen,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverTrigger"], {
                                        asChild: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "outline",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FilterIcon$3e$__["FilterIcon"], {
                                                    className: "mr-2 h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 412,
                                                    columnNumber: 19
                                                }, this),
                                                "More Filters"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                            lineNumber: 411,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 410,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverContent"], {
                                        className: "w-80 p-4",
                                        align: "end",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid gap-4",
                                            children: [
                                                showSessionFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "POS Session"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 424,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.pos_session_id ? filters.pos_session_id.toString() : "all",
                                                            onValueChange: handleSessionChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Sessions"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 434,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 433,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Sessions"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 437,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        posSessionsResponse?.map((session)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                                value: session.id.toString(),
                                                                                children: [
                                                                                    "Session #",
                                                                                    session.id
                                                                                ]
                                                                            }, session.id, true, {
                                                                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                                lineNumber: 439,
                                                                                columnNumber: 29
                                                                            }, this))
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 436,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 425,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 423,
                                                    columnNumber: 21
                                                }, this),
                                                showStatusFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "Status"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 454,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.status || "all",
                                                            onValueChange: handleStatusChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Statuses"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 460,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 459,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Statuses"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 463,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "completed",
                                                                            children: "Completed"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 464,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "pending",
                                                                            children: "Pending"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 465,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "cancelled",
                                                                            children: "Cancelled"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 466,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 462,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 455,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 453,
                                                    columnNumber: 21
                                                }, this),
                                                showDsaFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "Sale Type"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 475,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.is_dsa !== undefined ? filters.is_dsa.toString() : "all",
                                                            onValueChange: handleDsaChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Sales"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 485,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 484,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Sales"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 488,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "true",
                                                                            children: "DSA Sales"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 489,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "false",
                                                                            children: "Regular Sales"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 490,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 487,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 476,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 474,
                                                    columnNumber: 21
                                                }, this),
                                                showBankingMethodFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "Banking Method"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 499,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.banking_method || "all",
                                                            onValueChange: handleBankingMethodChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Methods"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 505,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 504,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Methods"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 508,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "bank",
                                                                            children: "Bank"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 509,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "agent",
                                                                            children: "Agent"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 510,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "mpesa",
                                                                            children: "M-Pesa"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 511,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 507,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 500,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 498,
                                                    columnNumber: 21
                                                }, this),
                                                showTransactionTypeFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            children: "Transaction Type"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 520,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                                            value: filters.transaction_type || "all",
                                                            onValueChange: handleTransactionTypeChange,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                                        placeholder: "All Types"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                        lineNumber: 526,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 525,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "all",
                                                                            children: "All Types"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 529,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "deposit",
                                                                            children: "Deposit"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 530,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "withdrawal",
                                                                            children: "Withdrawal"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 531,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                                            value: "transfer",
                                                                            children: "Transfer"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                            lineNumber: 532,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                                    lineNumber: 528,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                            lineNumber: 521,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                                    lineNumber: 519,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                            lineNumber: 417,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 416,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 409,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: handleReset,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                        lineNumber: 542,
                                        columnNumber: 15
                                    }, this),
                                    "Reset"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                                lineNumber: 541,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-filters.tsx",
                        lineNumber: 408,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/reports/components/report-filters.tsx",
                lineNumber: 279,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/reports/components/report-filters.tsx",
            lineNumber: 278,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/features/reports/components/report-filters.tsx",
        lineNumber: 277,
        columnNumber: 5
    }, this);
}
_s(ReportFilters, "9ust2ovXvqUSRp13iYKDnYCgWGA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$branches$2f$hooks$2f$use$2d$branches$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBranches"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$payment$2d$methods$2f$hooks$2f$use$2d$payment$2d$methods$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaymentMethods"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$pos$2f$hooks$2f$use$2d$pos$2d$sessions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePosSessions"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$regions$2f$hooks$2f$use$2d$regions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRegions"]
    ];
});
_c = ReportFilters;
var _c;
__turbopack_context__.k.register(_c, "ReportFilters");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/components/report-stat-card.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReportStatCard": (()=>ReportStatCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BanknoteIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/banknote.js [app-client] (ecmascript) <export default as BanknoteIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check.js [app-client] (ecmascript) <export default as CheckCircle2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js [app-client] (ecmascript) <export default as CreditCard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js [app-client] (ecmascript) <export default as DollarSign>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/smartphone.js [app-client] (ecmascript) <export default as Smartphone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wallet.js [app-client] (ecmascript) <export default as Wallet>");
"use client";
;
;
;
;
function ReportStatCard({ title, value, description, icon: Icon, trend, trendLabel, isCurrency = false, isPercentage = false, isCount = false, className }) {
    const formattedValue = isCurrency ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(typeof value === "string" ? parseFloat(value || "0") : value || 0) : isPercentage ? `${value || 0}%` : isCount ? value !== undefined && value !== null ? Number(value).toLocaleString() : "0" : value || "";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("overflow-hidden", className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                        className: "text-sm font-medium",
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                        lineNumber: 57,
                        columnNumber: 9
                    }, this),
                    typeof Icon === "string" ? getIconByName(Icon) : typeof Icon === "function" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                        className: "h-4 w-4 text-muted-foreground"
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                        lineNumber: 61,
                        columnNumber: 11
                    }, this) : Icon ? Icon : null
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 56,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-2xl font-bold",
                        children: formattedValue
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                        lineNumber: 67,
                        columnNumber: 9
                    }, this),
                    (description || trend !== undefined) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-xs text-muted-foreground",
                        children: [
                            description,
                            trend !== undefined && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("ml-1", trend > 0 ? "text-green-500" : trend < 0 ? "text-red-500" : ""),
                                children: [
                                    trend > 0 ? "+" : "",
                                    trend,
                                    "%",
                                    trendLabel && ` ${trendLabel}`
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                                lineNumber: 72,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                        lineNumber: 69,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
}
_c = ReportStatCard;
// Helper function to get icon by name
function getIconByName(iconName) {
    switch(iconName.toLowerCase()){
        case "bank":
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BanknoteIcon$3e$__["BanknoteIcon"], {
                className: "h-4 w-4 text-blue-500"
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 93,
                columnNumber: 14
            }, this);
        case "mpesa":
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                className: "h-4 w-4 text-green-500"
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 95,
                columnNumber: 14
            }, this);
        case "agent":
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__["Wallet"], {
                className: "h-4 w-4 text-amber-500"
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 97,
                columnNumber: 14
            }, this);
        case "money":
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"], {
                className: "h-4 w-4 text-emerald-500"
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 99,
                columnNumber: 14
            }, this);
        case "transaction":
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                className: "h-4 w-4 text-purple-500"
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 101,
                columnNumber: 14
            }, this);
        case "calculator":
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle2$3e$__["CheckCircle2"], {
                className: "h-4 w-4 text-green-500"
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 103,
                columnNumber: 14
            }, this);
        case "phone":
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__["Smartphone"], {
                className: "h-4 w-4 text-indigo-500"
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 105,
                columnNumber: 14
            }, this);
        default:
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"], {
                className: "h-4 w-4 text-muted-foreground"
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-stat-card.tsx",
                lineNumber: 107,
                columnNumber: 14
            }, this);
    }
}
var _c;
__turbopack_context__.k.register(_c, "ReportStatCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/components/report-chart.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReportChart": (()=>ReportChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Bar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/Bar.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$BarChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/chart/BarChart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$CartesianGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/CartesianGrid.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$component$2f$Cell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/component/Cell.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/Line.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$LineChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/chart/LineChart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$polar$2f$Pie$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/polar/Pie.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$PieChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/chart/PieChart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$XAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/XAxis.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$YAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/YAxis.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/chart.tsx [app-client] (ecmascript)");
"use client";
;
;
;
;
;
function ReportChart({ title, description, data, chartTypes = [
    "line",
    "bar"
], defaultChartType = "line", height = 250, showLegend = true, showGrid = true, showTooltip = true, className, trendInfo }) {
    // Generate chart config for Shadcn UI chart
    const chartConfig = {};
    // Define colors for datasets
    const colors = [
        {
            light: "#3b82f6",
            dark: "#60a5fa"
        },
        {
            light: "#10b981",
            dark: "#34d399"
        },
        {
            light: "#f59e0b",
            dark: "#fbbf24"
        },
        {
            light: "#ef4444",
            dark: "#f87171"
        },
        {
            light: "#8b5cf6",
            dark: "#a78bfa"
        },
        {
            light: "#ec4899",
            dark: "#f472b6"
        },
        {
            light: "#06b6d4",
            dark: "#22d3ee"
        }
    ];
    // Create chart config for each dataset
    data.datasets.forEach((dataset, index)=>{
        chartConfig[dataset.label] = {
            label: dataset.label,
            theme: {
                light: dataset.borderColor || colors[index % colors.length].light,
                dark: colors[index % colors.length].dark
            }
        };
        // For pie charts, add colors for each label
        if (chartTypes.includes("pie")) {
            data.labels.forEach((label, labelIndex)=>{
                const labelKey = label.toString().replace(/\s+/g, "-").toLowerCase();
                if (!chartConfig[labelKey]) {
                    chartConfig[labelKey] = {
                        label: label.toString(),
                        theme: {
                            light: colors[labelIndex % colors.length].light,
                            dark: colors[labelIndex % colors.length].dark
                        }
                    };
                }
            });
        }
    });
    // Prepare data for charts
    const chartData = data.labels.map((label, i)=>({
            name: label,
            ...data.datasets.reduce((acc, dataset)=>({
                    ...acc,
                    [dataset.label]: dataset.data[i]
                }), {})
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                        lineNumber: 116,
                        columnNumber: 9
                    }, this),
                    description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-muted-foreground",
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                        lineNumber: 118,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                lineNumber: 115,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                    defaultValue: defaultChartType,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                            className: "mb-4",
                            children: [
                                chartTypes.includes("line") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                    value: "line",
                                    children: "Line"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 125,
                                    columnNumber: 15
                                }, this),
                                chartTypes.includes("bar") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                    value: "bar",
                                    children: "Bar"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 128,
                                    columnNumber: 15
                                }, this),
                                chartTypes.includes("pie") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                    value: "pie",
                                    children: "Pie"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 131,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                            lineNumber: 123,
                            columnNumber: 11
                        }, this),
                        chartTypes.includes("line") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                            value: "line",
                            className: "space-y-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartContainer"], {
                                config: chartConfig,
                                className: "h-[250px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$LineChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LineChart"], {
                                    accessibilityLayer: true,
                                    data: chartData,
                                    margin: {
                                        top: 10,
                                        right: 30,
                                        left: 20,
                                        bottom: 20
                                    },
                                    children: [
                                        showGrid && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$CartesianGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CartesianGrid"], {
                                            strokeDasharray: "3 3",
                                            vertical: false
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 147,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$XAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["XAxis"], {
                                            dataKey: "name",
                                            tickLine: false,
                                            axisLine: false,
                                            tickMargin: 10,
                                            tickFormatter: (value)=>typeof value === "string" && value.length > 10 ? `${value.slice(0, 10)}...` : value
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 149,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$YAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["YAxis"], {
                                            tickLine: false,
                                            axisLine: false,
                                            tickMargin: 10
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 160,
                                            columnNumber: 19
                                        }, this),
                                        showTooltip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltip"], {
                                            cursor: false,
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltipContent"], {
                                                hideLabel: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 164,
                                                columnNumber: 32
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 162,
                                            columnNumber: 21
                                        }, this),
                                        showLegend && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegend"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegendContent"], {}, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 169,
                                                columnNumber: 32
                                            }, void 0),
                                            verticalAlign: "top",
                                            height: 36
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 168,
                                            columnNumber: 21
                                        }, this),
                                        data.datasets.map((dataset)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Line"], {
                                                type: "natural",
                                                dataKey: dataset.label,
                                                stroke: `var(--color-${dataset.label})`,
                                                strokeWidth: 2,
                                                dot: {
                                                    fill: `var(--color-${dataset.label})`,
                                                    r: 4
                                                },
                                                activeDot: {
                                                    r: 6,
                                                    strokeWidth: 2
                                                }
                                            }, dataset.label, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 175,
                                                columnNumber: 21
                                            }, this))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 141,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 137,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                            lineNumber: 136,
                            columnNumber: 13
                        }, this),
                        chartTypes.includes("bar") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                            value: "bar",
                            className: "space-y-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartContainer"], {
                                config: chartConfig,
                                className: "h-[250px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$BarChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BarChart"], {
                                    accessibilityLayer: true,
                                    data: chartData,
                                    margin: {
                                        top: 10,
                                        right: 30,
                                        left: 20,
                                        bottom: 20
                                    },
                                    children: [
                                        showGrid && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$CartesianGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CartesianGrid"], {
                                            strokeDasharray: "3 3",
                                            vertical: false
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 208,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$XAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["XAxis"], {
                                            dataKey: "name",
                                            tickLine: false,
                                            axisLine: false,
                                            tickMargin: 10,
                                            tickFormatter: (value)=>typeof value === "string" && value.length > 10 ? `${value.slice(0, 10)}...` : value
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 210,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$YAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["YAxis"], {
                                            tickLine: false,
                                            axisLine: false,
                                            tickMargin: 10
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 221,
                                            columnNumber: 19
                                        }, this),
                                        showTooltip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltip"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltipContent"], {
                                                hideLabel: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 223,
                                                columnNumber: 44
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 223,
                                            columnNumber: 21
                                        }, this),
                                        showLegend && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegend"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegendContent"], {}, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 227,
                                                columnNumber: 32
                                            }, void 0),
                                            verticalAlign: "top",
                                            height: 36
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 226,
                                            columnNumber: 21
                                        }, this),
                                        data.datasets.map((dataset)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Bar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Bar"], {
                                                dataKey: dataset.label,
                                                fill: `var(--color-${dataset.label})`,
                                                radius: [
                                                    4,
                                                    4,
                                                    4,
                                                    4
                                                ],
                                                maxBarSize: 60
                                            }, dataset.label, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 233,
                                                columnNumber: 21
                                            }, this))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 202,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 198,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                            lineNumber: 197,
                            columnNumber: 13
                        }, this),
                        chartTypes.includes("pie") && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                            value: "pie",
                            className: "space-y-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartContainer"], {
                                config: chartConfig,
                                className: "h-[250px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$PieChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PieChart"], {
                                    margin: {
                                        top: 10,
                                        right: 30,
                                        left: 20,
                                        bottom: 20
                                    },
                                    children: [
                                        data.datasets.map((dataset)=>{
                                            const pieData = data.labels.map((label, i)=>({
                                                    name: label,
                                                    value: dataset.data[i],
                                                    dataKey: dataset.label,
                                                    fill: `var(--color-${label.toString().replace(/\s+/g, "-").toLowerCase()})`
                                                }));
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$polar$2f$Pie$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Pie"], {
                                                data: pieData,
                                                cx: "50%",
                                                cy: "50%",
                                                outerRadius: 80,
                                                innerRadius: 30,
                                                paddingAngle: 2,
                                                dataKey: "value",
                                                nameKey: "name",
                                                label: ({ name, percent })=>percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : "",
                                                labelLine: false,
                                                className: "[&_.recharts-pie-label-text]:fill-foreground",
                                                children: pieData.map((entry, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$component$2f$Cell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Cell"], {
                                                        fill: entry.fill
                                                    }, `cell-${index}`, false, {
                                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                        lineNumber: 284,
                                                        columnNumber: 27
                                                    }, this))
                                            }, dataset.label, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 265,
                                                columnNumber: 23
                                            }, this);
                                        }),
                                        showTooltip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltip"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartTooltipContent"], {
                                                hideLabel: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 290,
                                                columnNumber: 44
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 290,
                                            columnNumber: 21
                                        }, this),
                                        showLegend && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegend"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartLegendContent"], {}, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                                lineNumber: 294,
                                                columnNumber: 32
                                            }, void 0),
                                            verticalAlign: "bottom",
                                            height: 36,
                                            iconSize: 10,
                                            iconType: "circle"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                            lineNumber: 293,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                    lineNumber: 252,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 248,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/reports/components/report-chart.tsx",
                            lineNumber: 247,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                    lineNumber: 122,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                lineNumber: 121,
                columnNumber: 7
            }, this),
            trendInfo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t px-6 py-4 flex-col items-start gap-2 text-sm",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-2 font-medium leading-none",
                    children: trendInfo.isPositive ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            "Trending up by ",
                            Math.abs(trendInfo.value).toFixed(1),
                            "%",
                            " ",
                            trendInfo.label || "",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                xmlns: "http://www.w3.org/2000/svg",
                                viewBox: "0 0 24 24",
                                fill: "none",
                                stroke: "currentColor",
                                strokeWidth: "2",
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                className: "h-4 w-4 text-emerald-500",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "m6 9 6-6 6 6"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 324,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 12h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 325,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 15h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 326,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 18h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 327,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 314,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            "Trending down by ",
                            Math.abs(trendInfo.value).toFixed(1),
                            "%",
                            " ",
                            trendInfo.label || "",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                xmlns: "http://www.w3.org/2000/svg",
                                viewBox: "0 0 24 24",
                                fill: "none",
                                stroke: "currentColor",
                                strokeWidth: "2",
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                className: "h-4 w-4 text-red-500",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "m6 15 6 6 6-6"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 344,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 6h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 345,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 9h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 346,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 12h12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                        lineNumber: 347,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                                lineNumber: 334,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-chart.tsx",
                    lineNumber: 309,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-chart.tsx",
                lineNumber: 308,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/reports/components/report-chart.tsx",
        lineNumber: 114,
        columnNumber: 5
    }, this);
}
_c = ReportChart;
var _c;
__turbopack_context__.k.register(_c, "ReportChart");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/reports/components/report-data-table.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReportDataTable": (()=>ReportDataTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$data$2d$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/data-pagination.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$search$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/search-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/select.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/table.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$export$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/export-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-table/build/lib/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/table-core/build/lib/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-down.js [app-client] (ecmascript) <export default as ArrowDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-up.js [app-client] (ecmascript) <export default as ArrowUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
function ReportDataTable({ columns, data, title, description, searchPlaceholder = "Search...", searchColumn, exportFilename = "export", showExport = true, showSearch = true, showPagination = true, rowClassName, mobileBreakpoint = 768, enableMobileCards = true, pagination, isLoading = false }) {
    _s();
    const [sorting, setSorting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [columnFilters, setColumnFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Check for mobile view
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ReportDataTable.useEffect": ()=>{
            const checkMobile = {
                "ReportDataTable.useEffect.checkMobile": ()=>{
                    setIsMobile(window.innerWidth < mobileBreakpoint);
                }
            }["ReportDataTable.useEffect.checkMobile"];
            checkMobile();
            window.addEventListener("resize", checkMobile);
            return ({
                "ReportDataTable.useEffect": ()=>window.removeEventListener("resize", checkMobile)
            })["ReportDataTable.useEffect"];
        }
    }["ReportDataTable.useEffect"], [
        mobileBreakpoint
    ]);
    const table = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useReactTable"])({
        data,
        columns,
        getCoreRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCoreRowModel"])(),
        // Use server-side pagination if pagination prop is provided
        ...pagination ? {
            manualPagination: true,
            pageCount: pagination.totalPages
        } : {
            getPaginationRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPaginationRowModel"])()
        },
        onSortingChange: setSorting,
        getSortedRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSortedRowModel"])(),
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFilteredRowModel"])(),
        state: {
            sorting,
            columnFilters,
            ...pagination ? {
                pagination: {
                    pageIndex: pagination.currentPage - 1,
                    pageSize: pagination.pageSize || 10
                }
            } : {}
        }
    });
    const handleExport = ()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$export$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["downloadTableAsExcel"])(table, exportFilename);
    };
    // Render mobile card for a row
    const renderMobileCard = (row, index)=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
            className: "mb-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-2",
                    children: table.getAllColumns().map((column)=>{
                        const cell = row.getVisibleCells().find((c)=>c.column.id === column.id);
                        if (!cell) return null;
                        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(cell.column.columnDef.cell, cell.getContext());
                        // Skip empty values
                        if (!value || value === "-" || value === "") return null;
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-between items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm font-medium text-muted-foreground",
                                    children: [
                                        typeof column.columnDef.header === "string" ? column.columnDef.header : column.id,
                                        ":"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 164,
                                    columnNumber: 19
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm",
                                    children: value
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 170,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, column.id, true, {
                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                            lineNumber: 160,
                            columnNumber: 17
                        }, this);
                    })
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 144,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                lineNumber: 143,
                columnNumber: 9
            }, this)
        }, `mobile-card-${index}`, false, {
            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
            lineNumber: 142,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            (title || description || showSearch || showExport) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                className: "space-y-1",
                children: [
                    title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 184,
                        columnNumber: 21
                    }, this),
                    description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardDescription"], {
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 185,
                        columnNumber: 27
                    }, this),
                    (showSearch || showExport) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            showSearch && searchColumn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$search$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SearchInput"], {
                                placeholder: searchPlaceholder,
                                value: table.getColumn(searchColumn)?.getFilterValue() ?? "",
                                onSearch: (value)=>table.getColumn(searchColumn)?.setFilterValue(value),
                                onClear: ()=>table.getColumn(searchColumn)?.setFilterValue(""),
                                mode: "realtime",
                                className: "max-w-sm"
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 189,
                                columnNumber: 17
                            }, this),
                            showExport && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: handleExport,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 208,
                                        columnNumber: 19
                                    }, this),
                                    "Export"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 207,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 187,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                lineNumber: 183,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                children: isMobile && enableMobileCards ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: isLoading || pagination?.isLoading ? // Show skeleton cards when loading
                    Array.from({
                        length: pagination?.pageSize || 10
                    }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                            className: "mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                className: "p-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 bg-muted animate-pulse rounded w-3/4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 227,
                                            columnNumber: 25
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 bg-muted animate-pulse rounded w-1/2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 228,
                                            columnNumber: 25
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 bg-muted animate-pulse rounded w-2/3"
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 229,
                                            columnNumber: 25
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 226,
                                    columnNumber: 23
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 225,
                                columnNumber: 21
                            }, this)
                        }, `skeleton-card-${index}`, false, {
                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                            lineNumber: 224,
                            columnNumber: 19
                        }, this)) : table.getRowModel().rows?.length ? table.getRowModel().rows.map((row, index)=>renderMobileCard(row, index)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-8 text-muted-foreground",
                        children: "No data available"
                    }, void 0, false, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 240,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 219,
                    columnNumber: 11
                }, this) : /* Desktop Table Layout */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "rounded-md border overflow-x-auto",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Table"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHeader"], {
                                children: table.getHeaderGroups().map((headerGroup)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        children: headerGroup.headers.map((header)=>{
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                className: "whitespace-nowrap",
                                                children: header.isPlaceholder ? null : header.column.getCanSort() ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center space-x-1 cursor-pointer",
                                                    onClick: header.column.getToggleSortingHandler(),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(header.column.columnDef.header, header.getContext())
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                            lineNumber: 263,
                                                            columnNumber: 31
                                                        }, this),
                                                        header.column.getIsSorted() === "asc" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUp$3e$__["ArrowUp"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                            lineNumber: 270,
                                                            columnNumber: 33
                                                        }, this) : header.column.getIsSorted() === "desc" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowDown$3e$__["ArrowDown"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                            lineNumber: 272,
                                                            columnNumber: 33
                                                        }, this) : null
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                    lineNumber: 259,
                                                    columnNumber: 29
                                                }, this) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(header.column.columnDef.header, header.getContext())
                                            }, header.id, false, {
                                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                lineNumber: 254,
                                                columnNumber: 25
                                            }, this);
                                        })
                                    }, headerGroup.id, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 251,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 249,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableBody"], {
                                children: isLoading || pagination?.isLoading ? // Show skeleton rows when loading
                                Array.from({
                                    length: pagination?.pageSize || 10
                                }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        children: columns.map((_, colIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "h-4 bg-muted animate-pulse rounded"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                    lineNumber: 295,
                                                    columnNumber: 29
                                                }, this)
                                            }, `skeleton-cell-${colIndex}`, false, {
                                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                lineNumber: 294,
                                                columnNumber: 27
                                            }, this))
                                    }, `skeleton-${index}`, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 292,
                                        columnNumber: 23
                                    }, this)) : table.getRowModel().rows?.length ? table.getRowModel().rows.map((row)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        "data-state": row.getIsSelected() && "selected",
                                        className: rowClassName ? rowClassName(row) : "",
                                        children: row.getVisibleCells().map((cell)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(cell.column.columnDef.cell, cell.getContext())
                                            }, cell.id, false, {
                                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                lineNumber: 309,
                                                columnNumber: 25
                                            }, this))
                                    }, row.original.uniqueKey || row.id, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 303,
                                        columnNumber: 21
                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                        colSpan: columns.length,
                                        className: "h-24 text-center",
                                        children: "No results."
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                        lineNumber: 320,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 319,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                lineNumber: 287,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                        lineNumber: 248,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 247,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                lineNumber: 216,
                columnNumber: 7
            }, this),
            showPagination && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardFooter"], {
                className: "py-4",
                children: pagination ? // Server-side pagination
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$data$2d$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DataPagination"], {
                    currentPage: pagination.currentPage,
                    totalPages: pagination.totalPages,
                    onPageChange: pagination.onPageChange,
                    pageSize: pagination.pageSize || 10,
                    onPageSizeChange: pagination.onPageSizeChange,
                    totalItems: pagination.totalItems || 0,
                    isLoading: pagination.isLoading || isLoading,
                    showPageSizeSelector: true,
                    showItemsInfo: true,
                    showFirstLastButtons: true
                }, void 0, false, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 337,
                    columnNumber: 13
                }, this) : // Client-side pagination (fallback)
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between space-x-2 w-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 text-sm text-muted-foreground",
                            children: [
                                "Showing",
                                " ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: [
                                        table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1,
                                        "-",
                                        Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, table.getFilteredRowModel().rows.length)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 354,
                                    columnNumber: 17
                                }, this),
                                " ",
                                "of ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: table.getFilteredRowModel().rows.length
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 365,
                                    columnNumber: 20
                                }, this),
                                " ",
                                "results"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                            lineNumber: 352,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    size: "sm",
                                    onClick: ()=>table.previousPage(),
                                    disabled: !table.getCanPreviousPage(),
                                    children: "Previous"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 369,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    size: "sm",
                                    onClick: ()=>table.nextPage(),
                                    disabled: !table.getCanNextPage(),
                                    children: "Next"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 377,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                                    value: table.getState().pagination.pageSize.toString(),
                                    onValueChange: (value)=>{
                                        table.setPageSize(Number(value));
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                            className: "h-8 w-[70px]",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                                placeholder: table.getState().pagination.pageSize
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                lineNumber: 392,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 391,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                            side: "top",
                                            children: [
                                                10,
                                                20,
                                                30,
                                                40,
                                                50
                                            ].map((pageSize)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                                    value: pageSize.toString(),
                                                    children: pageSize
                                                }, pageSize, false, {
                                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                                    lineNumber: 398,
                                                    columnNumber: 23
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                            lineNumber: 396,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                                    lineNumber: 385,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                            lineNumber: 368,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                    lineNumber: 351,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/reports/components/report-data-table.tsx",
                lineNumber: 334,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/reports/components/report-data-table.tsx",
        lineNumber: 181,
        columnNumber: 5
    }, this);
}
_s(ReportDataTable, "sjlrSubY4gphho2tCPIgdKOQuxk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useReactTable"]
    ];
});
_c = ReportDataTable;
var _c;
__turbopack_context__.k.register(_c, "ReportDataTable");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_features_reports_d85033fa._.js.map