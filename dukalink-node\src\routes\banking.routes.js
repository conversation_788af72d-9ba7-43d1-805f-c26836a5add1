const express = require("express");
const router = express.Router();
const { authenticate } = require("../middleware/auth.middleware");
const bankingTransactionController = require("../controllers/banking-transaction.controller");
const { uploadReceiptImageAllTypes } = require("../utils/file-upload");

// Get all banking transactions
router.get(
  "/",
  authenticate,
  bankingTransactionController.getAllBankingTransactions
);

// Get daily banking summary
router.get(
  "/summary",
  authenticate,
  bankingTransactionController.getDailyBankingSummary
);

// Get a banking transaction by ID
router.get(
  "/:id",
  authenticate,
  bankingTransactionController.getBankingTransactionById
);

// Create a new banking transaction (with optional receipt image) - accepts all file types
router.post(
  "/",
  authenticate,
  uploadReceiptImageAllTypes.single("receipt"),
  bankingTransactionController.createBankingTransaction
);

// Update a banking transaction
router.put(
  "/:id",
  authenticate,
  bankingTransactionController.updateBankingTransaction
);

// Delete a banking transaction
router.delete(
  "/:id",
  authenticate,
  bankingTransactionController.deleteBankingTransaction
);

// Upload receipt image for a banking transaction - accepts all file types
router.post(
  "/:id/receipts",
  authenticate,
  uploadReceiptImageAllTypes.single("receipt"),
  bankingTransactionController.uploadBankingTransactionReceipt
);

// Get receipt image for a banking transaction
router.get(
  "/:id/receipts/:receiptId",
  authenticate,
  bankingTransactionController.getBankingTransactionReceipt
);

// Delete receipt image for a banking transaction
router.delete(
  "/:id/receipts/:receiptId",
  authenticate,
  bankingTransactionController.deleteBankingTransactionReceipt
);

// Approve a banking transaction (permission checked in controller)
router.post(
  "/:id/approve",
  authenticate,
  bankingTransactionController.approveBankingTransaction
);

// Reject a banking transaction (permission checked in controller)
router.post(
  "/:id/reject",
  authenticate,
  bankingTransactionController.rejectBankingTransaction
);

module.exports = router;
