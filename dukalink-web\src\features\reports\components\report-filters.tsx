"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useBranches } from "@/features/branches/hooks/use-branches";
import { EmployeeSelector } from "@/features/employees/components/employee-selector";
import { PaymentMethodSelector } from "@/features/payment-methods/components/payment-method-selector";
import { usePaymentMethods } from "@/features/payment-methods/hooks/use-payment-methods";
import { usePosSessions } from "@/features/pos/hooks/use-pos-sessions";
import { useRegions } from "@/features/regions/hooks/use-regions";
import { ProductSelector } from "./product-selector";
import {
  DATE_RANGE_OPTIONS,
  ReportFilterParams,
  TIME_RANGE_OPTIONS,
} from "@/types";
import { endOfDay, format, startOfDay, subDays } from "date-fns";
import { FilterIcon, RefreshCw } from "lucide-react";
import { useState } from "react";
import { DateRange } from "react-day-picker";

interface ReportFiltersProps {
  filters: ReportFilterParams;
  onFilterChange: (filters: ReportFilterParams) => void;
  showTimeFilter?: boolean;
  showSessionFilter?: boolean;
  showUserFilter?: boolean;
  showBranchFilter?: boolean;
  showRegionFilter?: boolean;
  showPaymentMethodFilter?: boolean;
  showStatusFilter?: boolean;
  showDsaFilter?: boolean;
  showProductFilter?: boolean;
  showCategoryFilter?: boolean;
  showLocationFilter?: boolean;
  showBankingMethodFilter?: boolean;
  showTransactionTypeFilter?: boolean;
}

export function ReportFilters({
  filters,
  onFilterChange,
  showTimeFilter = true,
  showSessionFilter = false,
  showUserFilter = true,
  showBranchFilter = true,
  showRegionFilter = false,
  showPaymentMethodFilter = true,
  showStatusFilter = false,
  showDsaFilter = false,
  showProductFilter = false,
  showCategoryFilter = false,
  showLocationFilter = false,
  showBankingMethodFilter = false,
  showTransactionTypeFilter = false,
}: ReportFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: filters.start_date
      ? new Date(filters.start_date)
      : subDays(new Date(), 7),
    to: filters.end_date ? new Date(filters.end_date) : new Date(),
  });
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>("this_week");
  // Time filter removed

  const { data: branchesResponse } = useBranches();
  const { data: paymentMethodsResponse } = usePaymentMethods();
  const { data: posSessionsResponse } = usePosSessions();
  const { data: regionsResponse } = useRegions();

  // Filter branches by selected region
  const filteredBranches = branchesResponse?.data?.filter((branch) => {
    if (!filters.region_id) return true; // Show all branches if no region selected
    return branch.region_id === filters.region_id;
  }) || [];

  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range);
    if (range?.from && range?.to) {
      onFilterChange({
        ...filters,
        start_date: format(range.from, "yyyy-MM-dd"),
        end_date: format(range.to, "yyyy-MM-dd"),
      });
    }
  };

  const handlePredefinedDateRange = (value: string) => {
    setSelectedDateRange(value);
    let from: Date | undefined;
    let to: Date | undefined;

    const today = new Date();

    switch (value) {
      case "today":
        from = startOfDay(today);
        to = endOfDay(today);
        break;
      case "yesterday":
        from = startOfDay(subDays(today, 1));
        to = endOfDay(subDays(today, 1));
        break;
      case "this_week":
        from = startOfDay(subDays(today, today.getDay()));
        to = endOfDay(today);
        break;
      case "last_week":
        from = startOfDay(subDays(today, today.getDay() + 7));
        to = endOfDay(subDays(today, today.getDay() + 1));
        break;
      case "this_month":
        from = startOfDay(new Date(today.getFullYear(), today.getMonth(), 1));
        to = endOfDay(today);
        break;
      case "last_month":
        from = startOfDay(
          new Date(today.getFullYear(), today.getMonth() - 1, 1)
        );
        to = endOfDay(new Date(today.getFullYear(), today.getMonth(), 0));
        break;
      case "this_year":
        from = startOfDay(new Date(today.getFullYear(), 0, 1));
        to = endOfDay(today);
        break;
      case "last_year":
        from = startOfDay(new Date(today.getFullYear() - 1, 0, 1));
        to = endOfDay(new Date(today.getFullYear() - 1, 11, 31));
        break;
      case "custom":
        // Keep the current date range
        return;
      default:
        from = subDays(today, 7);
        to = today;
    }

    setDateRange({ from, to });

    if (from && to) {
      onFilterChange({
        ...filters,
        start_date: format(from, "yyyy-MM-dd"),
        end_date: format(to, "yyyy-MM-dd"),
      });
    }
  };

  // Time filter removed

  const handleBranchChange = (value: string) => {
    onFilterChange({
      ...filters,
      branch_id: value === "all" ? undefined : parseInt(value, 10),
    });
  };

  const handleRegionChange = (value: string) => {
    const regionId = value === "all" ? undefined : parseInt(value, 10);

    onFilterChange({
      ...filters,
      region_id: regionId,
      // Reset branch filter when region changes
      branch_id: undefined,
    });
  };

  const handleEmployeeChange = (value: number | undefined) => {
    onFilterChange({
      ...filters,
      employee_id: value,
    });
  };

  const handlePaymentMethodChange = (value: string) => {
    onFilterChange({
      ...filters,
      payment_method_id: value === "all" ? undefined : parseInt(value, 10),
    });
  };

  const handleSessionChange = (value: string) => {
    onFilterChange({
      ...filters,
      pos_session_id: value === "all" ? undefined : parseInt(value, 10),
    });
  };

  const handleStatusChange = (value: string) => {
    onFilterChange({
      ...filters,
      status: value === "all" ? undefined : value,
    });
  };

  const handleDsaChange = (value: string) => {
    onFilterChange({
      ...filters,
      is_dsa: value === "all" ? undefined : value === "true",
    });
  };

  const handleProductChange = (value: string) => {
    onFilterChange({
      ...filters,
      product_id: value === "all" ? undefined : parseInt(value, 10),
    });
  };

  const handleCategoryChange = (value: string) => {
    onFilterChange({
      ...filters,
      category_id: value === "all" ? undefined : parseInt(value, 10),
    });
  };

  const handleLocationChange = (value: string) => {
    onFilterChange({
      ...filters,
      location_id: value === "all" ? undefined : parseInt(value, 10),
    });
  };

  const handleBankingMethodChange = (value: string) => {
    onFilterChange({
      ...filters,
      banking_method: value === "all" ? undefined : value,
    });
  };

  const handleTransactionTypeChange = (value: string) => {
    onFilterChange({
      ...filters,
      transaction_type: value === "all" ? undefined : value,
    });
  };

  const handleReset = () => {
    setSelectedDateRange("this_week");
    const today = new Date();
    const from = subDays(today, 7);
    const to = today;
    setDateRange({ from, to });

    // Reset all filters to default values
    onFilterChange({
      start_date: format(from, "yyyy-MM-dd"),
      end_date: format(to, "yyyy-MM-dd"),
      branch_id: undefined,
      region_id: undefined,
      payment_method_id: undefined,
      employee_id: undefined,
      pos_session_id: undefined,
      status: undefined,
      is_dsa: undefined,
    });
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex flex-col space-y-4 md:flex-row md:flex-wrap md:items-end md:gap-4 md:space-y-0">
          {/* Date Range Filter */}
          <div className="flex-1 space-y-2">
            <Label>Date Range</Label>
            <div className="flex flex-wrap gap-2">
              <Select
                value={selectedDateRange}
                onValueChange={handlePredefinedDateRange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  {DATE_RANGE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <DateRangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
              />
            </div>
          </div>

          {/* Branch Filter - Moved outside of More Filters */}
          {showBranchFilter && (
            <div className="space-y-2">
              <Label>Branch</Label>
              <Select
                value={filters.branch_id ? filters.branch_id.toString() : "all"}
                onValueChange={handleBranchChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Branches" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Branches</SelectItem>
                  {filteredBranches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id.toString()}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Payment Method Filter - Moved outside of More Filters */}
          {showPaymentMethodFilter && (
            <div className="space-y-2">
              <Label>Payment Method</Label>
              <div className="w-[220px]">
                <PaymentMethodSelector
                  value={filters.payment_method_id}
                  onValueChange={(value) =>
                    handlePaymentMethodChange(
                      value !== undefined ? value.toString() : "all"
                    )
                  }
                  placeholder="All Payment Methods"
                  includeAllOption={true}
                />
              </div>
            </div>
          )}

          {/* Product Filter */}
          {showProductFilter && (
            <div className="space-y-2">
              <Label>Product</Label>
              <div className="w-[220px]">
                <ProductSelector
                  value={filters.product_id}
                  onValueChange={(value) =>
                    handleProductChange(value?.toString() || "all")
                  }
                  placeholder="All Products"
                  includeAllOption={true}
                />
              </div>
            </div>
          )}

          {/* Region Filter */}
          {showRegionFilter && (
            <div className="space-y-2">
              <Label>Region</Label>
              <Select
                value={filters.region_id ? filters.region_id.toString() : "all"}
                onValueChange={handleRegionChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Regions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Regions</SelectItem>
                  {regionsResponse?.data?.map((region) => (
                    <SelectItem key={region.id} value={region.id.toString()}>
                      {region.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Employee Filter - Moved outside of More Filters */}
          {showUserFilter && (
            <div className="space-y-2">
              <Label>Employee</Label>
              <div className="w-[220px]">
                <EmployeeSelector
                  value={filters.employee_id}
                  onValueChange={handleEmployeeChange}
                  placeholder="All Employees"
                  includeAllOption={true}
                  branchId={filters.branch_id}
                />
              </div>
            </div>
          )}

          {/* Time Range Filter removed */}

          {/* More Filters Button */}
          <div className="flex space-x-2">
            <Popover open={isOpen} onOpenChange={setIsOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline">
                  <FilterIcon className="mr-2 h-4 w-4" />
                  More Filters
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="end">
                <div className="grid gap-4">
                  {/* Branch Filter - Moved outside */}
                  {/* Employee Filter - Moved outside */}

                  {/* Session Filter */}
                  {showSessionFilter && (
                    <div className="space-y-2">
                      <Label>POS Session</Label>
                      <Select
                        value={
                          filters.pos_session_id
                            ? filters.pos_session_id.toString()
                            : "all"
                        }
                        onValueChange={handleSessionChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="All Sessions" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Sessions</SelectItem>
                          {posSessionsResponse?.map((session) => (
                            <SelectItem
                              key={session.id}
                              value={session.id.toString()}
                            >
                              Session #{session.id}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Status Filter */}
                  {showStatusFilter && (
                    <div className="space-y-2">
                      <Label>Status</Label>
                      <Select
                        value={filters.status || "all"}
                        onValueChange={handleStatusChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="All Statuses" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* DSA Filter */}
                  {showDsaFilter && (
                    <div className="space-y-2">
                      <Label>Sale Type</Label>
                      <Select
                        value={
                          filters.is_dsa !== undefined
                            ? filters.is_dsa.toString()
                            : "all"
                        }
                        onValueChange={handleDsaChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="All Sales" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Sales</SelectItem>
                          <SelectItem value="true">DSA Sales</SelectItem>
                          <SelectItem value="false">Regular Sales</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Banking Method Filter */}
                  {showBankingMethodFilter && (
                    <div className="space-y-2">
                      <Label>Banking Method</Label>
                      <Select
                        value={filters.banking_method || "all"}
                        onValueChange={handleBankingMethodChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="All Methods" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Methods</SelectItem>
                          <SelectItem value="bank">Bank</SelectItem>
                          <SelectItem value="agent">Agent</SelectItem>
                          <SelectItem value="mpesa">M-Pesa</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Transaction Type Filter */}
                  {showTransactionTypeFilter && (
                    <div className="space-y-2">
                      <Label>Transaction Type</Label>
                      <Select
                        value={filters.transaction_type || "all"}
                        onValueChange={handleTransactionTypeChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="All Types" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Types</SelectItem>
                          <SelectItem value="deposit">Deposit</SelectItem>
                          <SelectItem value="withdrawal">Withdrawal</SelectItem>
                          <SelectItem value="transfer">Transfer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>

            <Button variant="outline" onClick={handleReset}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
