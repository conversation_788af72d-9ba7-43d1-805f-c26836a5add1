/**
 * TypeScript interfaces for Stock Levels Endpoint
 * Based on the backend implementation in stock-levels-endpoint.md
 */

// Query Parameters Interface
export interface StockLevelsParams {
  branch_id?: number;
  region_id?: number;
  category_id?: number;
  include_zero_stock?: boolean;
  include_inactive?: boolean;
  low_stock_threshold?: number;
  page?: number;
  limit?: number;
  search?: string;
  sort_by?: 'name' | 'quantity' | 'value' | 'category';
  sort_direction?: 'asc' | 'desc';
  format?: 'json' | 'excel';
}

// Applied Filters Interface
export interface StockLevelsFilters {
  branch_id: number | null;
  region_id: number | null;
  category_id: number | null;
  include_zero_stock: boolean;
  include_inactive: boolean;
  low_stock_threshold: number | null;
  search: string | null;
  sort_by: string;
  sort_direction: string;
}

// Summary Statistics Interface
export interface StockLevelsSummary {
  total_products: number;
  total_value: number;
  total_quantity: number;
  in_stock_count: number;
  low_stock_count: number;
  out_of_stock_count: number;
  categories_count: number;
  branches_count: number;
  last_updated: string;
}

// Category Breakdown Interface
export interface CategoryBreakdown {
  category_id: number;
  category_name: string;
  product_count: number;
  total_quantity: number;
  total_value: number;
  low_stock_count: number;
  out_of_stock_count: number;
}

// Branch Breakdown Interface
export interface BranchBreakdown {
  branch_id: number;
  branch_name: string;
  branch_location: string;
  product_count: number;
  total_quantity: number;
  total_value: number;
  low_stock_count: number;
  out_of_stock_count: number;
}

// Stock Alert Interfaces
export interface CriticalLowStockAlert {
  product_id: number;
  product_name: string;
  sku: string;
  current_quantity: number;
  min_stock_level: number;
  branch_name: string;
  branch_location: string;
  urgency_level: 'high' | 'medium' | 'critical';
  last_updated: string;
}

export interface OutOfStockAlert {
  product_id: number;
  product_name: string;
  sku: string;
  branch_name: string;
  branch_location: string;
  last_sale_date: string | null;
  days_out_of_stock: number;
  last_updated: string;
}

export interface StockAlerts {
  critical_low_stock: CriticalLowStockAlert[];
  out_of_stock: OutOfStockAlert[];
}

// Product Related Interfaces
export interface ProductCategory {
  id: number;
  name: string;
  parent_category: string | null;
}

export interface ProductBrand {
  id: number;
  name: string;
}

export interface ProductStockInfo {
  current_quantity: number;
  min_stock_level: number;
  max_stock_level: number;
  reorder_point: number;
  stock_status: 'in_stock' | 'low_stock' | 'out_of_stock';
  days_of_stock: number | null;
  last_restocked: string | null;
}

export interface ProductPricing {
  buying_price: number;
  selling_price: number;
  margin_percentage: string;
  total_value: string;
}

export interface ProductLocation {
  branch_id: number;
  branch_name: string;
  branch_location: string;
  region_id: number;
  region_name: string | null;
  warehouse_location: string | null;
}

export interface ProductMovementSummary {
  sales_last_30_days: number | null;
  purchases_last_30_days: number | null;
  adjustments_last_30_days: number | null;
  transfers_in_last_30_days: number | null;
  transfers_out_last_30_days: number | null;
}

export interface ProductDetails {
  is_active: boolean;
  is_serialized: boolean;
  has_expiry: boolean;
  weight: string | null;
  dimensions: string | null;
  supplier: string | null;
  created_at: string;
  updated_at: string;
}

// Complete Product Interface
export interface StockLevelProduct {
  id: number;
  name: string;
  sku: string;
  barcode: string | null;
  category: ProductCategory;
  brand: ProductBrand;
  stock_info: ProductStockInfo;
  pricing: ProductPricing;
  location: ProductLocation;
  movement_summary: ProductMovementSummary;
  product_details: ProductDetails;
}

// Pagination Interface
export interface StockLevelsPagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
  has_next: boolean;
  has_prev: boolean;
}

// Main Response Interface
export interface StockLevelsResponse {
  status: 'success';
  filters: StockLevelsFilters;
  summary: StockLevelsSummary;
  by_category: CategoryBreakdown[];
  by_branch: BranchBreakdown[];
  stock_alerts: StockAlerts;
  products: StockLevelProduct[];
  pagination: StockLevelsPagination;
}

// Error Response Interface
export interface StockLevelsErrorResponse {
  status: 'error';
  message: string;
  error: {
    code: string;
    details: string;
  };
}

// Union type for all possible responses
export type StockLevelsApiResponse = StockLevelsResponse | StockLevelsErrorResponse;

// Export response for Excel format (Blob)
export type StockLevelsExportResponse = Blob;

// Hook return type
export interface UseStockLevelsReturn {
  data: StockLevelsResponse | undefined;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
}

// Utility types for component props
export interface StockLevelsTableProps {
  products: StockLevelProduct[];
  isLoading?: boolean;
  onSort?: (field: string, direction: 'asc' | 'desc') => void;
}

export interface StockAlertCardProps {
  alert: CriticalLowStockAlert | OutOfStockAlert;
  type: 'low_stock' | 'out_of_stock';
}

export interface StockSummaryCardsProps {
  summary: StockLevelsSummary;
  isLoading?: boolean;
}

export interface CategoryBreakdownChartProps {
  categories: CategoryBreakdown[];
  isLoading?: boolean;
}

export interface BranchBreakdownChartProps {
  branches: BranchBreakdown[];
  isLoading?: boolean;
}

// Filter form interface
export interface StockLevelsFilterForm {
  branch_id: string;
  category_id: string;
  include_zero_stock: boolean;
  include_inactive: boolean;
  search: string;
  sort_by: string;
  sort_direction: string;
}

// Export handler interface
export interface StockLevelsExportParams extends Omit<StockLevelsParams, 'format'> {
  filename?: string;
}
