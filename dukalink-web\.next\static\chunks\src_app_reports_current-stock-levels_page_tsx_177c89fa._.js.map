{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/reports/current-stock-levels/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  useHQInventory,\r\n  useInventoryReportSummary,\r\n} from \"@/features/inventory/hooks/use-inventory\";\r\nimport { ReportChart } from \"@/features/reports/components/report-chart\";\r\nimport { ReportDataTable } from \"@/features/reports/components/report-data-table\";\r\nimport { ReportFilters } from \"@/features/reports/components/report-filters\";\r\nimport {\r\n  useStockLevelsExport,\r\n  useStockLevelsReport,\r\n} from \"@/features/reports/hooks/use-stock-levels\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { StockLevelProduct } from \"@/types/stock-levels\";\r\nimport { ColumnDef } from \"@tanstack/react-table\";\r\nimport { format } from \"date-fns\";\r\nimport {\r\n  AlertCircle,\r\n  <PERSON><PERSON><PERSON>riangle,\r\n  CheckCircle,\r\n  Download,\r\n  Package,\r\n  RefreshCw,\r\n  TrendingDown,\r\n  TrendingUp,\r\n  Wifi,\r\n  WifiOff,\r\n} from \"lucide-react\";\r\nimport { useState } from \"react\";\r\n\r\nexport default function CurrentStockLevelsPage() {\r\n  const [filters, setFilters] = useState({\r\n    branch_id: undefined as number | undefined,\r\n    region_id: undefined as number | undefined,\r\n    category_id: undefined as number | undefined,\r\n    search: \"\",\r\n    page: 1,\r\n    limit: 50,\r\n    include_zero_stock: true,\r\n    sort_by: \"name\" as const,\r\n    sort_direction: \"asc\" as const,\r\n  });\r\n\r\n  // Use the new comprehensive stock levels endpoint\r\n  const {\r\n    data: stockLevelsData,\r\n    isLoading,\r\n    error,\r\n    refetch,\r\n  } = useStockLevelsReport(filters);\r\n\r\n  // Excel export mutation\r\n  const exportMutation = useStockLevelsExport();\r\n\r\n  // Error state management\r\n  const isNetworkError =\r\n    error?.message?.includes(\"Network\") || error?.message?.includes(\"fetch\");\r\n  const isPermissionError =\r\n    error?.message?.includes(\"permission\") || error?.message?.includes(\"403\");\r\n  const isServerError =\r\n    error?.message?.includes(\"Server\") || error?.message?.includes(\"500\");\r\n  const isEndpointNotFound =\r\n    error?.message?.includes(\"404\") || error?.message?.includes(\"not found\");\r\n\r\n  // Fallback to old endpoints when new endpoint is not available\r\n  const shouldUseFallback = isEndpointNotFound || isServerError;\r\n\r\n  // Fallback hooks (only enabled when needed)\r\n  const { data: fallbackStockData, isLoading: isFallbackStockLoading } =\r\n    useHQInventory(\r\n      { ...filters, limit: filters.limit || 50 },\r\n      shouldUseFallback\r\n    );\r\n\r\n  const { data: fallbackSummaryData, isLoading: isFallbackSummaryLoading } =\r\n    useInventoryReportSummary({\r\n      branch_id: filters.branch_id,\r\n      category_id: filters.category_id,\r\n    });\r\n\r\n  const handleFilterChange = (newFilters: any) => {\r\n    setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));\r\n  };\r\n\r\n  const handleExport = async () => {\r\n    try {\r\n      await exportMutation.mutateAsync({\r\n        branch_id: filters.branch_id,\r\n        category_id: filters.category_id,\r\n        search: filters.search,\r\n        include_zero_stock: filters.include_zero_stock,\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Export failed:\", error);\r\n    }\r\n  };\r\n\r\n  const handleRetry = () => {\r\n    refetch();\r\n  };\r\n\r\n  // Error display component\r\n  const ErrorDisplay = () => {\r\n    if (!error) return null;\r\n\r\n    return (\r\n      <Card className=\"border-red-200 bg-red-50\">\r\n        <CardContent className=\"pt-6\">\r\n          <div className=\"flex items-center gap-3\">\r\n            {isNetworkError ? (\r\n              <WifiOff className=\"h-8 w-8 text-red-500\" />\r\n            ) : isPermissionError ? (\r\n              <AlertCircle className=\"h-8 w-8 text-red-500\" />\r\n            ) : isServerError ? (\r\n              <AlertTriangle className=\"h-8 w-8 text-red-500\" />\r\n            ) : (\r\n              <AlertCircle className=\"h-8 w-8 text-red-500\" />\r\n            )}\r\n\r\n            <div className=\"flex-1\">\r\n              <h3 className=\"font-semibold text-red-800\">\r\n                {isNetworkError && \"Network Connection Error\"}\r\n                {isPermissionError && \"Access Denied\"}\r\n                {isServerError && \"Server Error\"}\r\n                {isEndpointNotFound && \"Feature Not Available\"}\r\n                {!isNetworkError &&\r\n                  !isPermissionError &&\r\n                  !isServerError &&\r\n                  !isEndpointNotFound &&\r\n                  \"Error Loading Data\"}\r\n              </h3>\r\n              <p className=\"text-sm text-red-600 mt-1\">\r\n                {isNetworkError &&\r\n                  \"Please check your internet connection and try again.\"}\r\n                {isPermissionError &&\r\n                  \"You don't have permission to view stock reports. Contact your administrator.\"}\r\n                {isServerError &&\r\n                  \"The server is experiencing issues. Please try again later.\"}\r\n                {isEndpointNotFound &&\r\n                  \"The stock levels feature is not available. Please contact support.\"}\r\n                {!isNetworkError &&\r\n                  !isPermissionError &&\r\n                  !isServerError &&\r\n                  !isEndpointNotFound &&\r\n                  error.message}\r\n              </p>\r\n            </div>\r\n\r\n            <Button\r\n              onClick={handleRetry}\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"border-red-300 text-red-700 hover:bg-red-100\"\r\n            >\r\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n              Retry\r\n            </Button>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  };\r\n\r\n  // Column definitions for fallback data structure\r\n  const fallbackStockColumns: ColumnDef<any>[] = [\r\n    {\r\n      accessorKey: \"product.name\",\r\n      header: \"Product Name\",\r\n      cell: ({ row }) => (\r\n        <div>\r\n          <div className=\"font-medium\">\r\n            {row.original.product?.name || \"Unknown Product\"}\r\n          </div>\r\n          <div className=\"text-sm text-muted-foreground\">\r\n            SKU: {row.original.product?.sku || \"N/A\"}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      accessorKey: \"product.category.name\",\r\n      header: \"Category\",\r\n      cell: ({ row }) =>\r\n        row.original.product?.category?.name || \"Uncategorized\",\r\n    },\r\n    {\r\n      accessorKey: \"quantity\",\r\n      header: \"Current Stock\",\r\n      cell: ({ row }) => {\r\n        const quantity = row.getValue(\"quantity\") as number;\r\n        const minStock = row.original.product?.min_stock_level || 0;\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-2\">\r\n            <span\r\n              className={`font-medium ${\r\n                quantity <= 0\r\n                  ? \"text-red-600\"\r\n                  : quantity <= minStock\r\n                  ? \"text-yellow-600\"\r\n                  : \"text-green-600\"\r\n              }`}\r\n            >\r\n              {quantity}\r\n            </span>\r\n            {quantity <= 0 && (\r\n              <AlertTriangle className=\"h-4 w-4 text-red-500\" />\r\n            )}\r\n            {quantity > 0 && quantity <= minStock && (\r\n              <TrendingDown className=\"h-4 w-4 text-yellow-500\" />\r\n            )}\r\n            {quantity > minStock && (\r\n              <CheckCircle className=\"h-4 w-4 text-green-500\" />\r\n            )}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"buying_price\",\r\n      header: \"Unit Cost\",\r\n      cell: ({ row }) => formatCurrency(row.getValue(\"buying_price\") || 0),\r\n    },\r\n    {\r\n      accessorKey: \"selling_price\",\r\n      header: \"Unit Price\",\r\n      cell: ({ row }) => formatCurrency(row.getValue(\"selling_price\") || 0),\r\n    },\r\n    {\r\n      header: \"Total Value\",\r\n      cell: ({ row }) => {\r\n        const quantity = row.original.quantity || 0;\r\n        const price = row.original.buying_price || 0;\r\n        return formatCurrency(quantity * price);\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"branch.name\",\r\n      header: \"Branch\",\r\n      cell: ({ row }) => row.original.branch?.name || \"Unknown Branch\",\r\n    },\r\n    {\r\n      header: \"Region\",\r\n      cell: ({ row }) => row.original.branch?.region?.name || \"Unknown Region\",\r\n    },\r\n    {\r\n      header: \"Stock Status\",\r\n      cell: ({ row }) => {\r\n        const quantity = row.original.quantity || 0;\r\n        const minStock = row.original.product?.min_stock_level || 0;\r\n\r\n        let status = \"In Stock\";\r\n        let className = \"bg-green-100 text-green-800\";\r\n\r\n        if (quantity <= 0) {\r\n          status = \"Out of Stock\";\r\n          className = \"bg-red-100 text-red-800\";\r\n        } else if (quantity <= minStock) {\r\n          status = \"Low Stock\";\r\n          className = \"bg-yellow-100 text-yellow-800\";\r\n        }\r\n\r\n        return (\r\n          <span\r\n            className={`px-2 py-1 rounded text-xs font-medium ${className}`}\r\n          >\r\n            {status}\r\n          </span>\r\n        );\r\n      },\r\n    },\r\n  ];\r\n\r\n  // Column definitions for current stock levels using new data structure\r\n  const stockColumns: ColumnDef<StockLevelProduct>[] = [\r\n    {\r\n      accessorKey: \"name\",\r\n      header: \"Product Name\",\r\n      cell: ({ row }) => (\r\n        <div>\r\n          <div className=\"font-medium\">\r\n            {row.original.name || \"Unknown Product\"}\r\n          </div>\r\n          <div className=\"text-sm text-muted-foreground\">\r\n            SKU: {row.original.sku || \"N/A\"}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      accessorKey: \"category.name\",\r\n      header: \"Category\",\r\n      cell: ({ row }) => row.original.category?.name || \"Uncategorized\",\r\n    },\r\n    {\r\n      accessorKey: \"stock_info.current_quantity\",\r\n      header: \"Current Stock\",\r\n      cell: ({ row }) => {\r\n        const quantity = row.original.stock_info.current_quantity;\r\n        const minStock = row.original.stock_info.min_stock_level;\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-2\">\r\n            <span\r\n              className={`font-medium ${\r\n                quantity <= 0\r\n                  ? \"text-red-600\"\r\n                  : quantity <= minStock\r\n                  ? \"text-yellow-600\"\r\n                  : \"text-green-600\"\r\n              }`}\r\n            >\r\n              {quantity}\r\n            </span>\r\n            {quantity <= 0 && (\r\n              <AlertTriangle className=\"h-4 w-4 text-red-500\" />\r\n            )}\r\n            {quantity > 0 && quantity <= minStock && (\r\n              <TrendingDown className=\"h-4 w-4 text-yellow-500\" />\r\n            )}\r\n            {quantity > minStock && (\r\n              <CheckCircle className=\"h-4 w-4 text-green-500\" />\r\n            )}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"pricing.buying_price\",\r\n      header: \"Unit Cost\",\r\n      cell: ({ row }) => formatCurrency(row.original.pricing.buying_price || 0),\r\n    },\r\n    {\r\n      accessorKey: \"pricing.selling_price\",\r\n      header: \"Unit Price\",\r\n      cell: ({ row }) =>\r\n        formatCurrency(row.original.pricing.selling_price || 0),\r\n    },\r\n    {\r\n      header: \"Total Value\",\r\n      cell: ({ row }) => {\r\n        const totalValue = parseFloat(row.original.pricing.total_value || \"0\");\r\n        return formatCurrency(totalValue);\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"location.branch_name\",\r\n      header: \"Branch\",\r\n      cell: ({ row }) => (\r\n        <div>\r\n          <div className=\"font-medium\">\r\n            {row.original.location.branch_name || \"Unknown Branch\"}\r\n          </div>\r\n          <div className=\"text-sm text-muted-foreground\">\r\n            {row.original.location.branch_location || \"\"}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      accessorKey: \"location.region_name\",\r\n      header: \"Region\",\r\n      cell: ({ row }) => row.original.location.region_name || \"Unknown Region\",\r\n    },\r\n    {\r\n      accessorKey: \"brand.name\",\r\n      header: \"Brand\",\r\n      cell: ({ row }) => row.original.brand?.name || \"No Brand\",\r\n    },\r\n    {\r\n      accessorKey: \"pricing.margin_percentage\",\r\n      header: \"Margin %\",\r\n      cell: ({ row }) => {\r\n        const margin = parseFloat(\r\n          row.original.pricing.margin_percentage || \"0\"\r\n        );\r\n        return (\r\n          <span\r\n            className={`font-medium ${\r\n              margin >= 30\r\n                ? \"text-green-600\"\r\n                : margin >= 15\r\n                ? \"text-yellow-600\"\r\n                : \"text-red-600\"\r\n            }`}\r\n          >\r\n            {margin.toFixed(1)}%\r\n          </span>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"stock_info.last_restocked\",\r\n      header: \"Last Restocked\",\r\n      cell: ({ row }) => {\r\n        const lastRestocked = row.original.stock_info.last_restocked;\r\n        return lastRestocked\r\n          ? format(new Date(lastRestocked), \"MMM dd, yyyy\")\r\n          : \"Never\";\r\n      },\r\n    },\r\n    {\r\n      header: \"Stock Status\",\r\n      cell: ({ row }) => {\r\n        const status = row.original.stock_info.stock_status;\r\n\r\n        let displayStatus = \"In Stock\";\r\n        let className = \"bg-green-100 text-green-800\";\r\n\r\n        if (status === \"out_of_stock\") {\r\n          displayStatus = \"Out of Stock\";\r\n          className = \"bg-red-100 text-red-800\";\r\n        } else if (status === \"low_stock\") {\r\n          displayStatus = \"Low Stock\";\r\n          className = \"bg-yellow-100 text-yellow-800\";\r\n        }\r\n\r\n        return (\r\n          <span\r\n            className={`px-2 py-1 rounded text-xs font-medium ${className}`}\r\n          >\r\n            {displayStatus}\r\n          </span>\r\n        );\r\n      },\r\n    },\r\n  ];\r\n\r\n  // Process chart data using new data structure or fallback\r\n  const processChartData = () => {\r\n    if (isUsingFallback) {\r\n      // Fallback: process data from old endpoint\r\n      if (!fallbackStockData?.data) return { labels: [], datasets: [] };\r\n\r\n      const categoryData = fallbackStockData.data.reduce(\r\n        (acc: any, item: any) => {\r\n          const category = item.product?.category?.name || \"Uncategorized\";\r\n          if (!acc[category]) {\r\n            acc[category] = { quantity: 0, value: 0 };\r\n          }\r\n          acc[category].quantity += item.quantity || 0;\r\n          return acc;\r\n        },\r\n        {}\r\n      );\r\n\r\n      return {\r\n        labels: Object.keys(categoryData),\r\n        datasets: [\r\n          {\r\n            label: \"Stock Quantity by Category\",\r\n            data: Object.values(categoryData).map((cat: any) => cat.quantity),\r\n            backgroundColor: [\r\n              \"#3B82F6\",\r\n              \"#EF4444\",\r\n              \"#10B981\",\r\n              \"#F59E0B\",\r\n              \"#8B5CF6\",\r\n              \"#EC4899\",\r\n              \"#06B6D4\",\r\n              \"#84CC16\",\r\n            ],\r\n          },\r\n        ],\r\n      };\r\n    }\r\n\r\n    if (!stockLevelsData?.by_category) return { labels: [], datasets: [] };\r\n\r\n    // Use the category breakdown from the new endpoint\r\n    const categoryData = stockLevelsData.by_category;\r\n\r\n    return {\r\n      labels: categoryData.map((cat) => cat.category_name),\r\n      datasets: [\r\n        {\r\n          label: \"Stock Quantity by Category\",\r\n          data: categoryData.map((cat) => cat.total_quantity),\r\n          backgroundColor: [\r\n            \"#3B82F6\",\r\n            \"#EF4444\",\r\n            \"#10B981\",\r\n            \"#F59E0B\",\r\n            \"#8B5CF6\",\r\n            \"#EC4899\",\r\n            \"#06B6D4\",\r\n            \"#84CC16\",\r\n          ],\r\n        },\r\n      ],\r\n    };\r\n  };\r\n\r\n  // Process branch chart data\r\n  const processBranchChartData = () => {\r\n    if (isUsingFallback) {\r\n      // Fallback: process data from old endpoint\r\n      if (!fallbackStockData?.data) return { labels: [], datasets: [] };\r\n\r\n      const branchData = fallbackStockData.data.reduce(\r\n        (acc: any, item: any) => {\r\n          const branch = item.branch?.name || \"Unknown Branch\";\r\n          if (!acc[branch]) {\r\n            acc[branch] = { quantity: 0, value: 0 };\r\n          }\r\n          acc[branch].quantity += item.quantity || 0;\r\n          return acc;\r\n        },\r\n        {}\r\n      );\r\n\r\n      return {\r\n        labels: Object.keys(branchData),\r\n        datasets: [\r\n          {\r\n            label: \"Stock Quantity by Branch\",\r\n            data: Object.values(branchData).map(\r\n              (branch: any) => branch.quantity\r\n            ),\r\n            backgroundColor: [\r\n              \"#10B981\",\r\n              \"#3B82F6\",\r\n              \"#F59E0B\",\r\n              \"#EF4444\",\r\n              \"#8B5CF6\",\r\n              \"#EC4899\",\r\n            ],\r\n          },\r\n        ],\r\n      };\r\n    }\r\n\r\n    if (!stockLevelsData?.by_branch) return { labels: [], datasets: [] };\r\n\r\n    // Use the branch breakdown from the new endpoint\r\n    const branchData = stockLevelsData.by_branch;\r\n\r\n    return {\r\n      labels: branchData.map((branch) => branch.branch_name),\r\n      datasets: [\r\n        {\r\n          label: \"Stock Quantity by Branch\",\r\n          data: branchData.map((branch) => branch.total_quantity),\r\n          backgroundColor: [\r\n            \"#10B981\",\r\n            \"#3B82F6\",\r\n            \"#F59E0B\",\r\n            \"#EF4444\",\r\n            \"#8B5CF6\",\r\n            \"#EC4899\",\r\n          ],\r\n        },\r\n      ],\r\n    };\r\n  };\r\n\r\n  // Extract data from new endpoint structure or fallback\r\n  const isUsingFallback = shouldUseFallback && !stockLevelsData;\r\n  const currentIsLoading = isUsingFallback\r\n    ? isFallbackStockLoading || isFallbackSummaryLoading\r\n    : isLoading;\r\n\r\n  const stockItems = isUsingFallback\r\n    ? fallbackStockData?.data || []\r\n    : stockLevelsData?.products || [];\r\n\r\n  const totalItems = isUsingFallback\r\n    ? fallbackStockData?.pagination?.total || 0\r\n    : stockLevelsData?.pagination?.total || 0;\r\n\r\n  const summary = isUsingFallback\r\n    ? {\r\n        total_products: fallbackSummaryData?.total_products || 0,\r\n        total_value: fallbackSummaryData?.total_value || 0,\r\n        total_quantity: 0, // Not available in fallback\r\n        in_stock_count:\r\n          (fallbackSummaryData?.total_products || 0) -\r\n          (fallbackSummaryData?.out_of_stock_count || 0),\r\n        low_stock_count: fallbackSummaryData?.low_stock_count || 0,\r\n        out_of_stock_count: fallbackSummaryData?.out_of_stock_count || 0,\r\n        categories_count: fallbackSummaryData?.by_category?.length || 0,\r\n        branches_count: fallbackSummaryData?.by_branch?.length || 0,\r\n        last_updated: new Date().toISOString(),\r\n      }\r\n    : stockLevelsData?.summary;\r\n\r\n  const stockAlerts = isUsingFallback ? null : stockLevelsData?.stock_alerts;\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold tracking-tight\">\r\n              Current Stock Levels\r\n            </h1>\r\n            <p className=\"text-muted-foreground\">\r\n              View real-time stock levels for all products across your locations\r\n            </p>\r\n          </div>\r\n          <Button\r\n            onClick={handleExport}\r\n            disabled={exportMutation.isPending}\r\n            variant=\"outline\"\r\n          >\r\n            {exportMutation.isPending ? (\r\n              <>\r\n                <div className=\"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600\" />\r\n                Exporting...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Download className=\"mr-2 h-4 w-4\" />\r\n                Export to Excel\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n\r\n        <ReportFilters\r\n          filters={filters}\r\n          onFilterChange={handleFilterChange}\r\n          showProductFilter={false}\r\n          showBranchFilter={true}\r\n          showRegionFilter={true}\r\n          showTimeFilter={false}\r\n          showUserFilter={false}\r\n          showPaymentMethodFilter={false}\r\n          showCategoryFilter={true}\r\n        />\r\n\r\n        {/* Error Display */}\r\n        <ErrorDisplay />\r\n\r\n        {/* Fallback Notice */}\r\n        {isUsingFallback && (\r\n          <Card className=\"border-blue-200 bg-blue-50\">\r\n            <CardContent className=\"pt-6\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <Wifi className=\"h-6 w-6 text-blue-500\" />\r\n                <div>\r\n                  <h3 className=\"font-semibold text-blue-800\">\r\n                    Using Basic Stock View\r\n                  </h3>\r\n                  <p className=\"text-sm text-blue-600\">\r\n                    Advanced features are temporarily unavailable. Showing basic\r\n                    stock information.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n\r\n        {/* Show content only if no error or using fallback */}\r\n        {(!error || isUsingFallback) && (\r\n          <>\r\n            {currentIsLoading ? (\r\n              <div className=\"grid grid-cols-1 gap-4 md:grid-cols-5\">\r\n                {[...Array(5)].map((_, i) => (\r\n                  <Skeleton key={i} className=\"h-32\" />\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div className=\"grid grid-cols-1 gap-4 md:grid-cols-5\">\r\n                <Card>\r\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                    <CardTitle className=\"text-sm font-medium\">\r\n                      Total Products\r\n                    </CardTitle>\r\n                    <Package className=\"h-4 w-4 text-muted-foreground\" />\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className=\"text-2xl font-bold\">\r\n                      {summary?.total_products || 0}\r\n                    </div>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Products in inventory\r\n                    </p>\r\n                  </CardContent>\r\n                </Card>\r\n\r\n                <Card>\r\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                    <CardTitle className=\"text-sm font-medium\">\r\n                      Total Value\r\n                    </CardTitle>\r\n                    <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className=\"text-2xl font-bold\">\r\n                      {formatCurrency(summary?.total_value || 0)}\r\n                    </div>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Current inventory value\r\n                    </p>\r\n                  </CardContent>\r\n                </Card>\r\n\r\n                <Card>\r\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                    <CardTitle className=\"text-sm font-medium\">\r\n                      In Stock\r\n                    </CardTitle>\r\n                    <CheckCircle className=\"h-4 w-4 text-green-500\" />\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className=\"text-2xl font-bold text-green-600\">\r\n                      {summary?.in_stock_count || 0}\r\n                    </div>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Items in stock\r\n                    </p>\r\n                  </CardContent>\r\n                </Card>\r\n\r\n                <Card>\r\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                    <CardTitle className=\"text-sm font-medium\">\r\n                      Low Stock Items\r\n                    </CardTitle>\r\n                    <AlertTriangle className=\"h-4 w-4 text-yellow-500\" />\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className=\"text-2xl font-bold text-yellow-600\">\r\n                      {summary?.low_stock_count || 0}\r\n                    </div>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Items below minimum level\r\n                    </p>\r\n                  </CardContent>\r\n                </Card>\r\n\r\n                <Card>\r\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                    <CardTitle className=\"text-sm font-medium\">\r\n                      Out of Stock\r\n                    </CardTitle>\r\n                    <TrendingDown className=\"h-4 w-4 text-red-500\" />\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className=\"text-2xl font-bold text-red-600\">\r\n                      {summary?.out_of_stock_count || 0}\r\n                    </div>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Items with zero stock\r\n                    </p>\r\n                  </CardContent>\r\n                </Card>\r\n              </div>\r\n            )}\r\n\r\n            {/* Stock Alerts Section */}\r\n            {stockAlerts &&\r\n              (stockAlerts.critical_low_stock.length > 0 ||\r\n                stockAlerts.out_of_stock.length > 0) && (\r\n                <div className=\"space-y-4\">\r\n                  <h2 className=\"text-lg font-semibold text-red-600 flex items-center gap-2\">\r\n                    <AlertTriangle className=\"h-5 w-5\" />\r\n                    Stock Alerts\r\n                  </h2>\r\n\r\n                  <div className=\"grid grid-cols-1 gap-4 lg:grid-cols-2\">\r\n                    {/* Critical Low Stock Alerts */}\r\n                    {stockAlerts.critical_low_stock.length > 0 && (\r\n                      <Card className=\"border-yellow-200 bg-yellow-50\">\r\n                        <CardHeader>\r\n                          <CardTitle className=\"text-yellow-800 flex items-center gap-2\">\r\n                            <TrendingDown className=\"h-4 w-4\" />\r\n                            Critical Low Stock (\r\n                            {stockAlerts.critical_low_stock.length})\r\n                          </CardTitle>\r\n                          <CardDescription>\r\n                            Products that urgently need restocking\r\n                          </CardDescription>\r\n                        </CardHeader>\r\n                        <CardContent>\r\n                          <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n                            {stockAlerts.critical_low_stock.map((alert) => (\r\n                              <div\r\n                                key={alert.product_id}\r\n                                className=\"flex items-center justify-between p-3 bg-white rounded-lg border\"\r\n                              >\r\n                                <div className=\"flex-1\">\r\n                                  <div className=\"font-medium text-sm\">\r\n                                    {alert.product_name}\r\n                                  </div>\r\n                                  <div className=\"text-xs text-muted-foreground\">\r\n                                    SKU: {alert.sku}\r\n                                  </div>\r\n                                  <div className=\"text-xs text-muted-foreground\">\r\n                                    {alert.branch_name}\r\n                                  </div>\r\n                                </div>\r\n                                <div className=\"text-right\">\r\n                                  <div className=\"text-sm font-medium text-red-600\">\r\n                                    {alert.current_quantity} /{\" \"}\r\n                                    {alert.min_stock_level}\r\n                                  </div>\r\n                                  <div\r\n                                    className={`text-xs px-2 py-1 rounded ${\r\n                                      alert.urgency_level === \"critical\"\r\n                                        ? \"bg-red-100 text-red-800\"\r\n                                        : alert.urgency_level === \"high\"\r\n                                        ? \"bg-orange-100 text-orange-800\"\r\n                                        : \"bg-yellow-100 text-yellow-800\"\r\n                                    }`}\r\n                                  >\r\n                                    {alert.urgency_level}\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                        </CardContent>\r\n                      </Card>\r\n                    )}\r\n\r\n                    {/* Out of Stock Alerts */}\r\n                    {stockAlerts.out_of_stock.length > 0 && (\r\n                      <Card className=\"border-red-200 bg-red-50\">\r\n                        <CardHeader>\r\n                          <CardTitle className=\"text-red-800 flex items-center gap-2\">\r\n                            <Package className=\"h-4 w-4\" />\r\n                            Out of Stock ({stockAlerts.out_of_stock.length})\r\n                          </CardTitle>\r\n                          <CardDescription>\r\n                            Products that are completely out of stock\r\n                          </CardDescription>\r\n                        </CardHeader>\r\n                        <CardContent>\r\n                          <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n                            {stockAlerts.out_of_stock.map((alert) => (\r\n                              <div\r\n                                key={alert.product_id}\r\n                                className=\"flex items-center justify-between p-3 bg-white rounded-lg border\"\r\n                              >\r\n                                <div className=\"flex-1\">\r\n                                  <div className=\"font-medium text-sm\">\r\n                                    {alert.product_name}\r\n                                  </div>\r\n                                  <div className=\"text-xs text-muted-foreground\">\r\n                                    SKU: {alert.sku}\r\n                                  </div>\r\n                                  <div className=\"text-xs text-muted-foreground\">\r\n                                    {alert.branch_name}\r\n                                  </div>\r\n                                </div>\r\n                                <div className=\"text-right\">\r\n                                  <div className=\"text-sm font-medium text-red-600\">\r\n                                    {alert.days_out_of_stock} days\r\n                                  </div>\r\n                                  <div className=\"text-xs text-muted-foreground\">\r\n                                    {alert.last_sale_date\r\n                                      ? `Last sale: ${format(\r\n                                          new Date(alert.last_sale_date),\r\n                                          \"MMM dd\"\r\n                                        )}`\r\n                                      : \"No recent sales\"}\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                        </CardContent>\r\n                      </Card>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n            {/* Stock Levels Charts */}\r\n            <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\r\n              <ReportChart\r\n                title=\"Stock Distribution by Category\"\r\n                description=\"Current stock quantities grouped by product category\"\r\n                data={processChartData()}\r\n                chartTypes={[\"bar\", \"pie\"]}\r\n                defaultChartType=\"bar\"\r\n              />\r\n\r\n              <ReportChart\r\n                title=\"Stock Distribution by Branch\"\r\n                description=\"Current stock quantities across different branches\"\r\n                data={processBranchChartData()}\r\n                chartTypes={[\"bar\", \"pie\"]}\r\n                defaultChartType=\"pie\"\r\n              />\r\n            </div>\r\n\r\n            {/* Stock Levels Table */}\r\n            {currentIsLoading ? (\r\n              <Skeleton className=\"h-96\" />\r\n            ) : (\r\n              <ReportDataTable\r\n                columns={isUsingFallback ? fallbackStockColumns : stockColumns}\r\n                data={stockItems}\r\n                title=\"Current Stock Levels\"\r\n                description={`Showing ${\r\n                  stockItems.length\r\n                } of ${totalItems} products${\r\n                  isUsingFallback ? \" (Basic View)\" : \"\"\r\n                }`}\r\n                searchColumn={isUsingFallback ? \"product.name\" : \"name\"}\r\n                searchPlaceholder=\"Search products...\"\r\n                exportFilename=\"current-stock-levels\"\r\n                showExport={false}\r\n              />\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AACA;AAIA;AACA;AACA;AACA;AAIA;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAvCA;;;;;;;;;;;;;;AAyCe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,WAAW;QACX,WAAW;QACX,aAAa;QACb,QAAQ;QACR,MAAM;QACN,OAAO;QACP,oBAAoB;QACpB,SAAS;QACT,gBAAgB;IAClB;IAEA,kDAAkD;IAClD,MAAM,EACJ,MAAM,eAAe,EACrB,SAAS,EACT,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE;IAEzB,wBAAwB;IACxB,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD;IAE1C,yBAAyB;IACzB,MAAM,iBACJ,OAAO,SAAS,SAAS,cAAc,OAAO,SAAS,SAAS;IAClE,MAAM,oBACJ,OAAO,SAAS,SAAS,iBAAiB,OAAO,SAAS,SAAS;IACrE,MAAM,gBACJ,OAAO,SAAS,SAAS,aAAa,OAAO,SAAS,SAAS;IACjE,MAAM,qBACJ,OAAO,SAAS,SAAS,UAAU,OAAO,SAAS,SAAS;IAE9D,+DAA+D;IAC/D,MAAM,oBAAoB,sBAAsB;IAEhD,4CAA4C;IAC5C,MAAM,EAAE,MAAM,iBAAiB,EAAE,WAAW,sBAAsB,EAAE,GAClE,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EACX;QAAE,GAAG,OAAO;QAAE,OAAO,QAAQ,KAAK,IAAI;IAAG,GACzC;IAGJ,MAAM,EAAE,MAAM,mBAAmB,EAAE,WAAW,wBAAwB,EAAE,GACtE,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE;QACxB,WAAW,QAAQ,SAAS;QAC5B,aAAa,QAAQ,WAAW;IAClC;IAEF,MAAM,qBAAqB,CAAC;QAC1B,WAAW,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,UAAU;gBAAE,MAAM;YAAE,CAAC;IAC3D;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,eAAe,WAAW,CAAC;gBAC/B,WAAW,QAAQ,SAAS;gBAC5B,aAAa,QAAQ,WAAW;gBAChC,QAAQ,QAAQ,MAAM;gBACtB,oBAAoB,QAAQ,kBAAkB;YAChD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,MAAM,cAAc;QAClB;IACF;IAEA,0BAA0B;IAC1B,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,OAAO;QAEnB,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,+BACC,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;mCACjB,kCACF,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;mCACrB,8BACF,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;iDAEzB,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCAGzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCACX,kBAAkB;wCAClB,qBAAqB;wCACrB,iBAAiB;wCACjB,sBAAsB;wCACtB,CAAC,kBACA,CAAC,qBACD,CAAC,iBACD,CAAC,sBACD;;;;;;;8CAEJ,6LAAC;oCAAE,WAAU;;wCACV,kBACC;wCACD,qBACC;wCACD,iBACC;wCACD,sBACC;wCACD,CAAC,kBACA,CAAC,qBACD,CAAC,iBACD,CAAC,sBACD,MAAM,OAAO;;;;;;;;;;;;;sCAInB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,iDAAiD;IACjD,MAAM,uBAAyC;QAC7C;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;sCACZ,IAAI,QAAQ,CAAC,OAAO,EAAE,QAAQ;;;;;;sCAEjC,6LAAC;4BAAI,WAAU;;gCAAgC;gCACvC,IAAI,QAAQ,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;;;;QAI3C;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GACZ,IAAI,QAAQ,CAAC,OAAO,EAAE,UAAU,QAAQ;QAC5C;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,WAAW,IAAI,QAAQ,CAAC;gBAC9B,MAAM,WAAW,IAAI,QAAQ,CAAC,OAAO,EAAE,mBAAmB;gBAE1D,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAW,CAAC,YAAY,EACtB,YAAY,IACR,iBACA,YAAY,WACZ,oBACA,kBACJ;sCAED;;;;;;wBAEF,YAAY,mBACX,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBAE1B,WAAW,KAAK,YAAY,0BAC3B,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAEzB,WAAW,0BACV,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAI/B;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,QAAQ,CAAC,mBAAmB;QACpE;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,QAAQ,CAAC,oBAAoB;QACrE;QACA;YACE,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,WAAW,IAAI,QAAQ,CAAC,QAAQ,IAAI;gBAC1C,MAAM,QAAQ,IAAI,QAAQ,CAAC,YAAY,IAAI;gBAC3C,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;YACnC;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,QAAQ;QAClD;QACA;YACE,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,QAAQ,QAAQ;QAC1D;QACA;YACE,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,WAAW,IAAI,QAAQ,CAAC,QAAQ,IAAI;gBAC1C,MAAM,WAAW,IAAI,QAAQ,CAAC,OAAO,EAAE,mBAAmB;gBAE1D,IAAI,SAAS;gBACb,IAAI,YAAY;gBAEhB,IAAI,YAAY,GAAG;oBACjB,SAAS;oBACT,YAAY;gBACd,OAAO,IAAI,YAAY,UAAU;oBAC/B,SAAS;oBACT,YAAY;gBACd;gBAEA,qBACE,6LAAC;oBACC,WAAW,CAAC,sCAAsC,EAAE,WAAW;8BAE9D;;;;;;YAGP;QACF;KACD;IAED,uEAAuE;IACvE,MAAM,eAA+C;QACnD;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;sCACZ,IAAI,QAAQ,CAAC,IAAI,IAAI;;;;;;sCAExB,6LAAC;4BAAI,WAAU;;gCAAgC;gCACvC,IAAI,QAAQ,CAAC,GAAG,IAAI;;;;;;;;;;;;;QAIlC;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,IAAI,QAAQ,CAAC,QAAQ,EAAE,QAAQ;QACpD;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,WAAW,IAAI,QAAQ,CAAC,UAAU,CAAC,gBAAgB;gBACzD,MAAM,WAAW,IAAI,QAAQ,CAAC,UAAU,CAAC,eAAe;gBAExD,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAW,CAAC,YAAY,EACtB,YAAY,IACR,iBACA,YAAY,WACZ,oBACA,kBACJ;sCAED;;;;;;wBAEF,YAAY,mBACX,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBAE1B,WAAW,KAAK,YAAY,0BAC3B,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAEzB,WAAW,0BACV,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAI/B;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,YAAY,IAAI;QACzE;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,aAAa,IAAI;QACzD;QACA;YACE,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,aAAa,WAAW,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,IAAI;gBAClE,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;sCACZ,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,IAAI;;;;;;sCAExC,6LAAC;4BAAI,WAAU;sCACZ,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,IAAI;;;;;;;;;;;;QAIlD;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,IAAI;QAC1D;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,QAAQ;QACjD;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,WACb,IAAI,QAAQ,CAAC,OAAO,CAAC,iBAAiB,IAAI;gBAE5C,qBACE,6LAAC;oBACC,WAAW,CAAC,YAAY,EACtB,UAAU,KACN,mBACA,UAAU,KACV,oBACA,gBACJ;;wBAED,OAAO,OAAO,CAAC;wBAAG;;;;;;;YAGzB;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,gBAAgB,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc;gBAC5D,OAAO,gBACH,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,gBAAgB,kBAChC;YACN;QACF;QACA;YACE,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,CAAC,YAAY;gBAEnD,IAAI,gBAAgB;gBACpB,IAAI,YAAY;gBAEhB,IAAI,WAAW,gBAAgB;oBAC7B,gBAAgB;oBAChB,YAAY;gBACd,OAAO,IAAI,WAAW,aAAa;oBACjC,gBAAgB;oBAChB,YAAY;gBACd;gBAEA,qBACE,6LAAC;oBACC,WAAW,CAAC,sCAAsC,EAAE,WAAW;8BAE9D;;;;;;YAGP;QACF;KACD;IAED,0DAA0D;IAC1D,MAAM,mBAAmB;QACvB,IAAI,iBAAiB;YACnB,2CAA2C;YAC3C,IAAI,CAAC,mBAAmB,MAAM,OAAO;gBAAE,QAAQ,EAAE;gBAAE,UAAU,EAAE;YAAC;YAEhE,MAAM,eAAe,kBAAkB,IAAI,CAAC,MAAM,CAChD,CAAC,KAAU;gBACT,MAAM,WAAW,KAAK,OAAO,EAAE,UAAU,QAAQ;gBACjD,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;oBAClB,GAAG,CAAC,SAAS,GAAG;wBAAE,UAAU;wBAAG,OAAO;oBAAE;gBAC1C;gBACA,GAAG,CAAC,SAAS,CAAC,QAAQ,IAAI,KAAK,QAAQ,IAAI;gBAC3C,OAAO;YACT,GACA,CAAC;YAGH,OAAO;gBACL,QAAQ,OAAO,IAAI,CAAC;gBACpB,UAAU;oBACR;wBACE,OAAO;wBACP,MAAM,OAAO,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,MAAa,IAAI,QAAQ;wBAChE,iBAAiB;4BACf;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;iBACD;YACH;QACF;QAEA,IAAI,CAAC,iBAAiB,aAAa,OAAO;YAAE,QAAQ,EAAE;YAAE,UAAU,EAAE;QAAC;QAErE,mDAAmD;QACnD,MAAM,eAAe,gBAAgB,WAAW;QAEhD,OAAO;YACL,QAAQ,aAAa,GAAG,CAAC,CAAC,MAAQ,IAAI,aAAa;YACnD,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM,aAAa,GAAG,CAAC,CAAC,MAAQ,IAAI,cAAc;oBAClD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;QACH;IACF;IAEA,4BAA4B;IAC5B,MAAM,yBAAyB;QAC7B,IAAI,iBAAiB;YACnB,2CAA2C;YAC3C,IAAI,CAAC,mBAAmB,MAAM,OAAO;gBAAE,QAAQ,EAAE;gBAAE,UAAU,EAAE;YAAC;YAEhE,MAAM,aAAa,kBAAkB,IAAI,CAAC,MAAM,CAC9C,CAAC,KAAU;gBACT,MAAM,SAAS,KAAK,MAAM,EAAE,QAAQ;gBACpC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;oBAChB,GAAG,CAAC,OAAO,GAAG;wBAAE,UAAU;wBAAG,OAAO;oBAAE;gBACxC;gBACA,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,KAAK,QAAQ,IAAI;gBACzC,OAAO;YACT,GACA,CAAC;YAGH,OAAO;gBACL,QAAQ,OAAO,IAAI,CAAC;gBACpB,UAAU;oBACR;wBACE,OAAO;wBACP,MAAM,OAAO,MAAM,CAAC,YAAY,GAAG,CACjC,CAAC,SAAgB,OAAO,QAAQ;wBAElC,iBAAiB;4BACf;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;iBACD;YACH;QACF;QAEA,IAAI,CAAC,iBAAiB,WAAW,OAAO;YAAE,QAAQ,EAAE;YAAE,UAAU,EAAE;QAAC;QAEnE,iDAAiD;QACjD,MAAM,aAAa,gBAAgB,SAAS;QAE5C,OAAO;YACL,QAAQ,WAAW,GAAG,CAAC,CAAC,SAAW,OAAO,WAAW;YACrD,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM,WAAW,GAAG,CAAC,CAAC,SAAW,OAAO,cAAc;oBACtD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;QACH;IACF;IAEA,uDAAuD;IACvD,MAAM,kBAAkB,qBAAqB,CAAC;IAC9C,MAAM,mBAAmB,kBACrB,0BAA0B,2BAC1B;IAEJ,MAAM,aAAa,kBACf,mBAAmB,QAAQ,EAAE,GAC7B,iBAAiB,YAAY,EAAE;IAEnC,MAAM,aAAa,kBACf,mBAAmB,YAAY,SAAS,IACxC,iBAAiB,YAAY,SAAS;IAE1C,MAAM,UAAU,kBACZ;QACE,gBAAgB,qBAAqB,kBAAkB;QACvD,aAAa,qBAAqB,eAAe;QACjD,gBAAgB;QAChB,gBACE,CAAC,qBAAqB,kBAAkB,CAAC,IACzC,CAAC,qBAAqB,sBAAsB,CAAC;QAC/C,iBAAiB,qBAAqB,mBAAmB;QACzD,oBAAoB,qBAAqB,sBAAsB;QAC/D,kBAAkB,qBAAqB,aAAa,UAAU;QAC9D,gBAAgB,qBAAqB,WAAW,UAAU;QAC1D,cAAc,IAAI,OAAO,WAAW;IACtC,IACA,iBAAiB;IAErB,MAAM,cAAc,kBAAkB,OAAO,iBAAiB;IAE9D,qBACE,6LAAC,kJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,eAAe,SAAS;4BAClC,SAAQ;sCAEP,eAAe,SAAS,iBACvB;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAAsF;;6DAIvG;;kDACE,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;8BAO7C,6LAAC,iKAAA,CAAA,gBAAa;oBACZ,SAAS;oBACT,gBAAgB;oBAChB,mBAAmB;oBACnB,kBAAkB;oBAClB,kBAAkB;oBAClB,gBAAgB;oBAChB,gBAAgB;oBAChB,yBAAyB;oBACzB,oBAAoB;;;;;;8BAItB,6LAAC;;;;;gBAGA,iCACC,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAG5C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAW9C,CAAC,CAAC,SAAS,eAAe,mBACzB;;wBACG,iCACC,6LAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,uIAAA,CAAA,WAAQ;oCAAS,WAAU;mCAAb;;;;;;;;;iDAInB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAG3C,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;sDAErB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DACZ,SAAS,kBAAkB;;;;;;8DAE9B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAG3C,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,eAAe;;;;;;8DAE1C,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAG3C,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;sDAEzB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DACZ,SAAS,kBAAkB;;;;;;8DAE9B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAG3C,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;sDAE3B,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DACZ,SAAS,mBAAmB;;;;;;8DAE/B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAG3C,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;sDAE1B,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DACZ,SAAS,sBAAsB;;;;;;8DAElC,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;wBASpD,eACC,CAAC,YAAY,kBAAkB,CAAC,MAAM,GAAG,KACvC,YAAY,YAAY,CAAC,MAAM,GAAG,CAAC,mBACnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;wCAEZ,YAAY,kBAAkB,CAAC,MAAM,GAAG,mBACvC,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,6LAAC,mIAAA,CAAA,aAAU;;sEACT,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAY;gEAEnC,YAAY,kBAAkB,CAAC,MAAM;gEAAC;;;;;;;sEAEzC,6LAAC,mIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;kEACZ,YAAY,kBAAkB,CAAC,GAAG,CAAC,CAAC,sBACnC,6LAAC;gEAEC,WAAU;;kFAEV,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,MAAM,YAAY;;;;;;0FAErB,6LAAC;gFAAI,WAAU;;oFAAgC;oFACvC,MAAM,GAAG;;;;;;;0FAEjB,6LAAC;gFAAI,WAAU;0FACZ,MAAM,WAAW;;;;;;;;;;;;kFAGtB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;oFACZ,MAAM,gBAAgB;oFAAC;oFAAG;oFAC1B,MAAM,eAAe;;;;;;;0FAExB,6LAAC;gFACC,WAAW,CAAC,0BAA0B,EACpC,MAAM,aAAa,KAAK,aACpB,4BACA,MAAM,aAAa,KAAK,SACxB,kCACA,iCACJ;0FAED,MAAM,aAAa;;;;;;;;;;;;;+DA5BnB,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;wCAuChC,YAAY,YAAY,CAAC,MAAM,GAAG,mBACjC,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,6LAAC,mIAAA,CAAA,aAAU;;sEACT,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAY;gEAChB,YAAY,YAAY,CAAC,MAAM;gEAAC;;;;;;;sEAEjD,6LAAC,mIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;kEACZ,YAAY,YAAY,CAAC,GAAG,CAAC,CAAC,sBAC7B,6LAAC;gEAEC,WAAU;;kFAEV,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,MAAM,YAAY;;;;;;0FAErB,6LAAC;gFAAI,WAAU;;oFAAgC;oFACvC,MAAM,GAAG;;;;;;;0FAEjB,6LAAC;gFAAI,WAAU;0FACZ,MAAM,WAAW;;;;;;;;;;;;kFAGtB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;oFACZ,MAAM,iBAAiB;oFAAC;;;;;;;0FAE3B,6LAAC;gFAAI,WAAU;0FACZ,MAAM,cAAc,GACjB,CAAC,WAAW,EAAE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EACjB,IAAI,KAAK,MAAM,cAAc,GAC7B,WACC,GACH;;;;;;;;;;;;;+DAxBH,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAsCzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,cAAW;oCACV,OAAM;oCACN,aAAY;oCACZ,MAAM;oCACN,YAAY;wCAAC;wCAAO;qCAAM;oCAC1B,kBAAiB;;;;;;8CAGnB,6LAAC,+JAAA,CAAA,cAAW;oCACV,OAAM;oCACN,aAAY;oCACZ,MAAM;oCACN,YAAY;wCAAC;wCAAO;qCAAM;oCAC1B,kBAAiB;;;;;;;;;;;;wBAKpB,iCACC,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;iDAEpB,6LAAC,uKAAA,CAAA,kBAAe;4BACd,SAAS,kBAAkB,uBAAuB;4BAClD,MAAM;4BACN,OAAM;4BACN,aAAa,CAAC,QAAQ,EACpB,WAAW,MAAM,CAClB,IAAI,EAAE,WAAW,SAAS,EACzB,kBAAkB,kBAAkB,IACpC;4BACF,cAAc,kBAAkB,iBAAiB;4BACjD,mBAAkB;4BAClB,gBAAe;4BACf,YAAY;;;;;;;;;;;;;;;;;;;AAQ5B;GAp3BwB;;QAmBlB,gKAAA,CAAA,uBAAoB;QAGD,gKAAA,CAAA,uBAAoB;QAiBzC,4JAAA,CAAA,iBAAc;QAMd,4JAAA,CAAA,4BAAyB;;;KA7CL", "debugId": null}}]}