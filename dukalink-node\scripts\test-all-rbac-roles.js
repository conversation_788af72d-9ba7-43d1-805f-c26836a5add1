const mysql = require('mysql2/promise');
require('dotenv').config();

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${new Date().toISOString()} - ${msg}`)
};

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dukalink_api',
  port: process.env.DB_PORT || 3306
};

async function testAllRbacRoles() {
  let connection;
  
  try {
    logger.info('🔍 Testing all RBAC roles...');
    
    // Connect to database
    logger.info('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Step 1: Get all roles from database
    logger.info('=== STEP 1: Getting roles from database ===');
    const [dbRoles] = await connection.execute(`
      SELECT DISTINCT role FROM rbac_grants ORDER BY role
    `);
    
    logger.info(`✓ Found ${dbRoles.length} roles in database:`);
    dbRoles.forEach(row => {
      logger.info(`  - ${row.role}`);
    });
    
    // Step 2: Test RBAC service
    logger.info('=== STEP 2: Testing RBAC service ===');
    
    try {
      // Add the project root to the require path
      const path = require('path');
      const projectRoot = path.resolve(__dirname, '..');
      process.env.NODE_PATH = projectRoot;
      require('module').Module._initPaths();
      
      const rbacService = require('../src/services/rbac.service');
      const ac = await rbacService.getAccessControl();
      
      const acGrants = ac.getGrants();
      const acRoles = Object.keys(acGrants);
      
      logger.success(`✅ RBAC service loaded successfully with ${acRoles.length} roles!`);
      
      // Step 3: Compare roles
      logger.info('=== STEP 3: Comparing database vs AccessControl roles ===');
      
      const dbRoleNames = dbRoles.map(r => r.role);
      const missingFromAC = dbRoleNames.filter(role => !acRoles.includes(role));
      const extraInAC = acRoles.filter(role => !dbRoleNames.includes(role));
      
      if (missingFromAC.length === 0) {
        logger.success('✅ All database roles are present in AccessControl!');
      } else {
        logger.error(`❌ ${missingFromAC.length} roles missing from AccessControl:`);
        missingFromAC.forEach(role => {
          logger.error(`  - ${role}`);
        });
      }
      
      if (extraInAC.length > 0) {
        logger.warn(`⚠️ ${extraInAC.length} extra roles in AccessControl (not in database):`);
        extraInAC.forEach(role => {
          logger.warn(`  - ${role}`);
        });
      }
      
      // Step 4: Test float_manager specifically
      logger.info('=== STEP 4: Testing float_manager permissions ===');
      
      if (acRoles.includes('float_manager')) {
        logger.success('✅ float_manager role found in AccessControl!');
        
        try {
          const approvalPerm = ac.can('float_manager').createAny('banking_approval');
          logger.success(`✅ banking_approval permission: ${approvalPerm.granted ? 'GRANTED' : 'DENIED'}`);
          
          const rejectionPerm = ac.can('float_manager').createAny('banking_rejection');
          logger.success(`✅ banking_rejection permission: ${rejectionPerm.granted ? 'GRANTED' : 'DENIED'}`);
          
          if (approvalPerm.granted && rejectionPerm.granted) {
            logger.success('🎉 float_manager banking permissions are working!');
          } else {
            logger.error('❌ float_manager banking permissions not working');
          }
          
        } catch (permError) {
          logger.error(`Permission test failed: ${permError.message}`);
        }
        
      } else {
        logger.error('❌ float_manager role still not found in AccessControl');
      }
      
      // Step 5: Test other important roles
      logger.info('=== STEP 5: Testing other important roles ===');
      
      const importantRoles = ['stock_admin', 'finance_manager', 'auditor', 'operations'];
      
      for (const role of importantRoles) {
        if (acRoles.includes(role)) {
          logger.success(`✅ ${role} role found in AccessControl`);
        } else {
          logger.error(`❌ ${role} role missing from AccessControl`);
        }
      }
      
      // Step 6: Summary
      logger.info('=== SUMMARY ===');
      logger.success(`✅ Database roles: ${dbRoles.length}`);
      logger.success(`✅ AccessControl roles: ${acRoles.length}`);
      logger.success(`✅ Missing from AC: ${missingFromAC.length}`);
      logger.success(`✅ Extra in AC: ${extraInAC.length}`);
      
      if (missingFromAC.length === 0) {
        logger.success('🎉 ALL ROLES SUCCESSFULLY LOADED!');
        logger.info('\n🔄 NEXT STEPS:');
        logger.info('1. Test banking approval with float_manager role');
        logger.info('2. Verify that all role-based access is working correctly');
        logger.info('3. No application restart needed - roles are loaded dynamically');
      } else {
        logger.error('❌ Some roles are still missing from AccessControl');
        logger.info('Check the default configuration file for missing role definitions');
      }
      
    } catch (rbacError) {
      logger.error(`RBAC service test failed: ${rbacError.message}`);
      logger.error(rbacError.stack);
    }
    
  } catch (error) {
    logger.error(`Error testing RBAC roles: ${error.message}`);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info('Database connection closed.');
    }
  }
}

// Execute the script
if (require.main === module) {
  testAllRbacRoles()
    .then(() => {
      logger.success('🎉 RBAC role test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`❌ RBAC role test failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { testAllRbacRoles };
